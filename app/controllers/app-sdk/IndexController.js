const BaseController = require('../base/BaseController');
const RetailAppShopRemoteService = require('../../services/api/bifrost/RetailAppShopRemoteService');
const AppShopRemoteService = require('../../services/api/bifrost/AppShopRemoteService');

class AppSdkController extends BaseController {
  constructor(ctx) {
    super(ctx);
    this.ctx.biz = 'app-sdk';
  }

  async getIndexHtml(ctx) {
    const { grantId } = this;
    const hasAppShopAbility = await new RetailAppShopRemoteService(ctx).hasRetailAppShopAbility(
      grantId
    );
    if (hasAppShopAbility) {
      await ctx.render('app-sdk.html');
    } else {
      return ctx.redirect('/v4/subscribe/appmarket/appdesc?id=13160');
    }
  }

  get grantId() {
    const { kdtId: grantId } = this.ctx;
    return `${grantId}`;
  }

  /**
   * 获取开店信息
   */
  async fetchAppInfo(ctx) {
    const { grantId } = this;
    const data = await new RetailAppShopRemoteService(ctx).getRetailAppShopInfo(grantId);
    ctx.json(0, 'ok', data);
  }

  /**
   * 获取待授权绑定店铺列表
   */
  async fetchAvaliableShopList(ctx) {
    const { grantId } = this;
    const { clientId } = ctx.getRequestData();
    const data = await new RetailAppShopRemoteService(ctx).getAuthSubShopList({
      grantId,
      clientId,
    });
    ctx.json(0, 'ok', data);
  }

  /**
   * 获取已经被绑定的分店店铺列表
   */
  async fetchAuthorizedShopList(ctx) {
    const { grantId } = this;
    const params = ctx.getRequestData();
    const data = await new RetailAppShopRemoteService(ctx).getUnbindSubShopList({
      grantId,
      ...params,
    });
    ctx.json(0, 'ok', data);
  }

  /**
   * 绑定店铺
   */
  async bindSelectedShop(ctx) {
    const { grantId } = this;
    const params = ctx.getRequestData();
    const data = await new RetailAppShopRemoteService(ctx).bind({
      grantId,
      ...params,
    });
    ctx.json(0, 'ok', data);
  }

  /**
   * 解绑店铺
   */
  async unbindSelectedShop(ctx) {
    const { clientId, grantId } = ctx.getRequestData();
    const data = await new AppShopRemoteService(ctx).unbind({
      grantId,
      clientId,
    });
    ctx.json(0, 'ok', data);
  }
}

module.exports = AppSdkController;
