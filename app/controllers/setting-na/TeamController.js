const BaseController = require('../base/BaseController');
const BenefitDisplayCarrierService = require('../../services/api/scrm/BenefitDisplayCarrierService');
const CardTemplateService = require('../../services/api/scrm/CardTemplateService');
const StaffService = require('../../services/api/sam/StaffServiceV2');
const ShopAddressService = require('../../services/api/shop/ShopAddressServiceV2');
const MallTradeSettingService = require('../../services/api/ebiz/mall/TradeSettingService');
const WeappAccountService = require('../../services/api/channels/WeappAccountService');
const InvoiceSwitchService = require('../../services/api/trade/InvoiceSwitchService');
const DiscountService = require('../../services/api/bigdata/ad/DiscountService');
const ShopConfigReadService = require('../../services/api/shop-config/ShopConfigReadService');
const ShopChainReadService = require('../../services/api/shop-center/ShopChainReadService');
const LevelGroupService = require('../../services/api/scrm/api/LevelGroupService');
const ShopConfigWriteService = require('../../services/api/shop-config/ShopConfigWriteService');

const _get = require('lodash/get');
const { fromApp } = require('../../constants');
const { checkUnifiedOnlineBranchStore, checkWscBranchStore } = require('@youzan/utils-shop');

const CARD_PAGE_SIZE = 50;

class TeamController extends BaseController {
  async getIndexHtml(ctx) {
    const kdtId = ctx.kdtId;
    const userInfo = ctx.getLocalSession('userInfo');
    const isBranchStore =
      checkWscBranchStore(ctx.state.shopInfo) || checkUnifiedOnlineBranchStore(ctx.state.shopInfo);
    this.initShopInfo();

    const [
      role,
      weappCode,
      defRefundAddress,
      isInRealNameAuthWhiteList,
      isInCityOrderWhiteList,
      isInvoiceServiceOpening,
      inGuaranteeWhiteList,
      inEduPaybackWhiteList,
      isRetailSupportSalesScrollWhiteList,
      branchShopInfo,
    ] = await Promise.all([
      new StaffService(ctx).findStaffRole({
        kdtId,
        isPhysical: true,
        adminId: userInfo.userId,
        biz: fromApp,
      }),
      new WeappAccountService(ctx).getWeappCodeLcByKdtId(kdtId),
      new ShopAddressService(ctx).queryDefaultShopAddress(kdtId, 1),
      // 实名验证白名单
      this.grayRelease('real_name_auth', kdtId),
      // 同城订单白名单
      this.grayRelease('local_order_print', kdtId),
      new InvoiceSwitchService(ctx).isInvoiceServiceOpening(kdtId).catch(() => false),
      // 是否在精简版店铺有赞担保白名单中
      this.grayRelease('zwsc_guarantee', kdtId),
      // 教育快速回款入口白名单
      this.grayRelease('edu_quick_payback', kdtId),
      // 零售销量和成交记录
      this.grayRelease('retail-sales-scroll', kdtId).catch(() => false),
      // 是网店才获取店铺信息，rootKdtId为该网店的总部id
      isBranchStore ? new ShopChainReadService(ctx).queryShopNodeInfo(kdtId) : Promise.resolve({}),
    ]);

    const isAdmin = role && role.length && role[0].roleId === 1;
    const { rootKdtId } = branchShopInfo;
    const params = ['goods_recommend'];
    let hqShopGoodsRecommend = {};
    if (rootKdtId) {
      hqShopGoodsRecommend = await new ShopConfigReadService(ctx).queryShopConfigs(
        rootKdtId,
        params
      );
    }

    ctx.setGlobal('weappVersion', weappCode && weappCode.releasedVersion);
    ctx.setGlobal('teamStatus', ctx.getState('teamStatus'));
    ctx.setGlobal('isInvoiceServiceOpening', isInvoiceServiceOpening);
    ctx.setGlobal('inGuaranteeWhiteList', inGuaranteeWhiteList);
    ctx.setGlobal('inEduPaybackWhiteList', inEduPaybackWhiteList);
    ctx.setGlobal('hqShopGoodsRecommend', hqShopGoodsRecommend);

    // 实名验证白名单
    ctx.setGlobal('isInRealNameAuthWhiteList', isInRealNameAuthWhiteList);
    // 同城订单白名单
    ctx.setGlobal('isInCityOrderWhiteList', isInCityOrderWhiteList);
    ctx.setGlobal('isAdmin', isAdmin); // 是否是高级管理员
    ctx.setGlobal('defRefundAddress', defRefundAddress); // 默认退货地址
    // 零售销量和成交记录
    ctx.setGlobal('isRetailSupportSalesScroll', isRetailSupportSalesScrollWhiteList);
    // 是否需要隐藏有赞担保标
    const hideGuaranteeList = ctx.apolloClient.getConfig({
      appId: 'yz-fin-aigis',
      namespace: 'yz-fin-aigis.black-gold-shop-list',
      key: 'yzdb.shield',
    });
    ctx.setGlobal('hideGuarantee', (hideGuaranteeList || []).includes(kdtId.toString()));

    await ctx.render('setting-na/team.html');
  }

  /**
   * 获取展示载体信息
   * @param {*} ctx
   */
  async getDefault(ctx) {
    const { kdtId } = ctx;
    const result = await new BenefitDisplayCarrierService(ctx).getDefault({
      kdtId,
    });
    ctx.successRes(result);
  }

  /**
   * 更新展示载体信息
   * @param {*} ctx
   */
  async setDefault(ctx) {
    const { displayCarriers } = ctx.getPostData();
    const { kdtId } = ctx;
    const result = await new BenefitDisplayCarrierService(ctx).setDefault({
      displayCarriers,
      kdtId,
    });
    ctx.successRes(result);
  }

  /**
   * 获取可设置会员卡列表
   * @param {*} ctx
   */
  async getAvailableList(ctx) {
    const { kdtId } = ctx;
    const cardTemplateService = new CardTemplateService(ctx);
    const commonParam = {
      kdtId,
      grantConditionList: [
        // 付费卡
        {
          allowBuy: true,
        },
      ],
      options: {
        // 带上详情，用于过滤已经过期的会员卡
        withDetail: true,
      },
      pageSize: CARD_PAGE_SIZE,
    };
    let cardList = [];

    const firstResult = await cardTemplateService.getAvailableList({
      ...commonParam,
      page: 1,
    });

    // 一次接口不能获取全部会员卡的情况
    const totalCount = firstResult.paginator.totalCount;
    // const totalCount = 400;
    cardList = cardList.concat(firstResult.items);
    if (cardList.length < totalCount) {
      const pageCount = Math.ceil(totalCount / CARD_PAGE_SIZE);
      await Promise.all(
        Array.from({ length: pageCount - 1 }, (v, i) => i + 2).map(page =>
          cardTemplateService.getAvailableList({
            ...commonParam,
            page,
          })
        )
      ).then(results => {
        cardList = cardList.concat(...results.map(result => result.items));
      });
    }

    ctx.successRes(cardList);
  }

  /**
   * 获取付费等级列表
   */
  async getGroupDetailList() {
    const { type = 2 } = this.ctx.query;
    let result = await new LevelGroupService(this.ctx).getGroupDetailList({
      kdtId: this.ctx.kdtId,
      type,
    });
    if (result.length) {
      // 后端大哥说取返回数组中第一项就好了
      result = _get(result, '[0].levelV2List', []);
    }

    this.ctx.successRes(result);
  }

  async editSettingByKdtId(ctx) {
    const { kdtId } = ctx;
    const data = await new MallTradeSettingService(ctx).editSettingByKdtId({
      ...ctx.request.body,
      source: 'merchant-pc', // 保持跟 queryTradeSetting 入参的 source 保持一致，给后端做操作日志比对
      kdtId,
    });
    const success = await new DiscountService(ctx).setDiscount({
      kdtId,
      isOn: ctx.request.body.enableDiscount ? true : false,
      percentage: Number(ctx.request.body.discount) * 10,
    });
    const { orderMode } = ctx.request.body;
    const userInfo = ctx.getLocalSession('userInfo');
    const shopWriteSuccess = await new ShopConfigWriteService(ctx).setShopConfig({
      kdtId,
      operator: {
        fromApp,
        id: userInfo.id,
        name: userInfo.nickName,
      },
      key: 'payment_setting_order_mode',
      value: String(orderMode || '0'),
    });
    return ctx.json(0, 'ok', data && success && shopWriteSuccess);
  }

  async getSettingByKdtId(ctx) {
    const { kdtId } = ctx;
    const data = await new MallTradeSettingService(ctx).queryTradeSetting({
      kdtId,
      source: 'merchant-pc', // 保持跟 editSettingByKdtId 入参的 source 保持一致，给后端做操作日志比对
    });
    const discountSetting = await new DiscountService(ctx).getDiscount({
      kdtId,
    });
    const orderModeConfig = await new ShopConfigReadService(ctx).queryShopConfig(
      kdtId,
      'payment_setting_order_mode'
    );
    const orderMode = _get(orderModeConfig, 'value', '0');
    return ctx.json(0, 'ok', {
      ...data,
      enableDiscount: discountSetting.isOn ? 1 : 0,
      discount: discountSetting.isOn ? (discountSetting.discount / 10).toString() : '',
      orderMode: String(orderMode),
    });
  }
  // 获取是否显示微信支付优惠设置
  async getIsShowWepaySetting(ctx) {
    const { kdtId } = ctx;
    let isShowWepaySetting;
    const shopInfo = ctx.getState('shopInfo');
    if (shopInfo.shopType === 7) {
      isShowWepaySetting = false;
    } else {
      isShowWepaySetting = await this.grayRelease('wsc_wepay_discount', kdtId);
    }

    return ctx.json(0, 'ok', {
      isShowWepaySetting,
    });
  }
  // 查询小程序自有是否打开
  async getSelfPayIsOpen(ctx) {
    const { kdtId } = ctx;
    const params = ['weixin_pay_origin', 'wx_applet_origin'];
    const result = await new ShopConfigReadService(ctx).queryShopConfigs(kdtId, params);
    return ctx.json(0, 'OK', result);
  }
}

module.exports = TeamController;
