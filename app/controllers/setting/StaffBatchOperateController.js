const BaseController = require('./BaseStaffController');
const SingleStaffService = require('../../services/api/staff/SingleStaffService');
const StaffBatchOperateService = require('../../services/api/sam/StaffBatchOperateService');
const StorageQiniuImageWriteService = require('../../services/api/material/materialcenter/StorageQiniuFileWriteInnerService');
const StaffCommonService = require('../../services/api/staff/StaffCommonService');
const StaffService = require('../../services/api/staff/StaffService');
const StaffQueryOuterService = require('../../services/api/staff/StaffQueryOuterService');
const StaffTemplateService = require('../../services/api/staff/StaffTemplateService');
const {
  fromApp,
  RETAIL_ROLE,
  WSC_SAM_ROLE,
  IDENTITY_BIZ_TYPE,
  SHOP_MODE,
  STAFF_ACTION_MAP,
  AUTH_ROLE_TYPE,
} = require('../../constants');
const { checkRetailShop, checkHqStore, checkPartnerStore } = require('@youzan/utils-shop');
const xlsx = require('node-xlsx');

// biz枚举 http://doc.qima-inc.com/pages/viewpage.action?pageId=12612833
const WSC = 'wsc';
const CASHIER = 'cashier'; // 收银
const RETAIL = 'retail'; // 超级门店
const BEAUTY = 'beauty'; // 美业
const CANYAN = 'cy'; // 餐饮
const RETAILHQ = 'retailhq'; // 零售集团版
const SUPPLIER = 'supplier'; // 供货

const ShopTypeBizMap = {
  0: WSC,
  5: CANYAN,
  6: BEAUTY,
  7: RETAIL,
  8: CASHIER,
  9: WSC,
  10: RETAILHQ,
  14: SUPPLIER,
};

class StaffBatchOperateController extends BaseController {
  constructor(ctx) {
    super(ctx);
    this.ctx.biz = '设置-员工管理-批量操作';
  }

  getBiz(ctx) {
    const kdtId = +ctx.kdtId;
    const shopInfo = ctx.getState('shopInfo');
    return this.getBizByShopType(kdtId <= 0 ? -1 : shopInfo.shopType);
  }

  getBizByShopType(shopType) {
    return ShopTypeBizMap[shopType] || WSC;
  }

  // 批量删除员工前，查询活动通知人关系
  async listStaffDeleteHooks(ctx) {
    const { adminIds } = ctx.validator(
      ctx.joi
        .object({
          adminIds: ctx.joi
            .array()
            .single()
            .items(ctx.joi.number()),
        })
        .rename('adminIds[]', 'adminIds')
    );
    const param = {
      kdtId: +ctx.kdtId,
      biz: this.getBiz(ctx),
      adminIds,
    };
    const res = await new StaffService(ctx).listStaffDeleteHooks(param);
    ctx.success(res);
  }

  // 批量删除单店员工
  async singleBatchDelete(ctx) {
    const {
      kdtId,
      userId: operatorId,
      request: {
        body: { token },
      },
    } = ctx;
    const { adminIds } = ctx.validator({
      adminIds: ctx.joi.array().items(ctx.joi.number()),
    });
    const needTokenCheck = await this.checkNeedTokenCheckForBatch(
      STAFF_ACTION_MAP.DELETE,
      adminIds
    );
    if (needTokenCheck) {
      this.validator.required(token, '核实身份 token 不能为空');
      const result = await this.tokenCheck(token, IDENTITY_BIZ_TYPE.DELETE);
      if (!result) return ctx.fail(-1, '身份验证失败');
    }

    const params = {
      kdtId: +kdtId,
      operatorId,
      adminIds,
    };
    const res = await new SingleStaffService(ctx).batchDeleteAsync(params);
    ctx.success(res);
  }

  // 批量创建记录
  async pageBatchCreateTaskResult(ctx) {
    const { kdtId, userId } = ctx;
    const query = ctx.validator({
      startDate: ctx.joi.number().integer(),
      endDate: ctx.joi.number().integer(),
      pageNo: ctx.joi
        .number()
        .integer()
        .required(),
      pageSize: ctx.joi
        .number()
        .integer()
        .required(),
      keyword: ctx.joi.string(),
    });
    const param = {
      ...query,
      kdtId: +kdtId,
      operatorId: userId,
    };

    const res = await new StaffBatchOperateService(ctx).pageBatchCreateTaskResult(param);
    ctx.success(res);
  }

  // 批量创建
  async submitBatchCreateTask(ctx) {
    const {
      kdtId,
      userId,
      request: {
        body: { token },
      },
    } = ctx;
    const { filePath, fileName } = ctx.validator({
      filePath: ctx.joi.string().required(),
      fileName: ctx.joi.string().required(),
    });
    // 解析excel是否包含高管或财务
    let needTokenCheck;
    try {
      const data = await new StaffTemplateService(ctx).getTemplate(filePath);
      const { data: excelData } = xlsx.parse(data)[0];
      const roleIndex = excelData[0].indexOf('所属店铺角色*');
      needTokenCheck = excelData.some(item => {
        return AUTH_ROLE_TYPE.includes(item[roleIndex]);
      });
    } catch (e) {
      needTokenCheck = false;
    }

    if (needTokenCheck) {
      this.validator.required(token, '核实身份 token 不能为空');
      const result = await this.tokenCheck(token, IDENTITY_BIZ_TYPE.CREATE_OR_UPDATE);
      if (!result) return ctx.fail(-1, '身份验证失败');
    }

    const param = {
      fileName,
      filePath,
      kdtId: +kdtId,
      operatorId: userId,
    };

    const res = await new StaffBatchOperateService(ctx).submitBatchCreateTask(param);
    ctx.success(res);
  }

  // 获取批量创建店铺素材中心文件上传token
  async getFileUploadToken(ctx) {
    const query = {
      channel: 'staff-batch-create',
      fromApp,
      maxSize: 1024 * 1024 * 5,
      operatorType: 1,
      operatorId: +ctx.userId,
    };

    const result = await new StorageQiniuImageWriteService(ctx).getPrivateFileUploadToken(query);
    ctx.success(result);
  }

  // 分页获取店铺能力
  async pageStaffAbility(ctx) {
    const { kdtId, userId } = ctx;
    const query = ctx.validator({
      pageNo: ctx.joi
        .number()
        .integer()
        .required(),
      pageSize: ctx.joi
        .number()
        .integer()
        .required(),
      keyword: ctx.joi.string(),
    });
    const param = {
      ...query,
      kdtId: +kdtId,
      operatorId: userId,
    };
    const res = await new StaffCommonService(ctx).pageStaffAbility(param);
    ctx.success(res);
  }

  // 批量启用连锁员工
  async batchEnable(ctx) {
    const {
      kdtId,
      userId: operatorId,
      request: {
        body: { token },
      },
    } = ctx;
    const { adminIds, salesKdtId, biz } = ctx.validator({
      adminIds: ctx.joi.array().items(ctx.joi.number()),
      salesKdtId: ctx.joi.number().integer(),
      biz: ctx.joi.string(),
    });
    const needTokenCheck = await this.checkNeedTokenCheckForBatch(
      STAFF_ACTION_MAP.ENABLE,
      adminIds,
      salesKdtId
    );
    if (needTokenCheck) {
      this.validator.required(token, '核实身份 token 不能为空');
      const result = await this.tokenCheck(token, IDENTITY_BIZ_TYPE.ENABLE_OR_DISABLE);
      if (!result) return ctx.fail(-1, '身份验证失败');
    }

    const params = {
      kdtId: +kdtId,
      operatorId,
      adminIds,
      salesKdtId,
      biz,
    };
    const res = await new StaffBatchOperateService(ctx).batchEnable(params);
    ctx.success(res);
  }

  // 批量停用连锁员工
  async batchDisable(ctx) {
    const {
      kdtId,
      userId: operatorId,
      request: {
        body: { token },
      },
    } = ctx;
    const { adminIds, salesKdtId, biz } = ctx.validator({
      adminIds: ctx.joi.array().items(ctx.joi.number()),
      salesKdtId: ctx.joi.number().integer(),
      biz: ctx.joi.string(),
    });

    const needTokenCheck = await this.checkNeedTokenCheckForBatch(
      STAFF_ACTION_MAP.DISABLE,
      adminIds,
      salesKdtId
    );
    if (needTokenCheck) {
      this.validator.required(token, '核实身份 token 不能为空');
      const result = await this.tokenCheck(token, IDENTITY_BIZ_TYPE.ENABLE_OR_DISABLE);
      if (!result) return ctx.fail(-1, '身份验证失败');
    }

    const params = {
      kdtId: +kdtId,
      operatorId,
      adminIds,
      salesKdtId,
      biz,
    };
    const res = await new StaffBatchOperateService(ctx).batchDisable(params);
    ctx.success(res);
  }

  // 批量删除连锁员工
  async chainBatchDelete(ctx) {
    const {
      kdtId,
      userId: operatorId,
      request: {
        body: { token },
      },
    } = ctx;
    const body = ctx.validator({
      adminIds: ctx.joi.array().items(ctx.joi.number()),
      salesKdtId: ctx.joi.number(),
      mode: ctx.joi.string(),
      biz: ctx.joi.string(),
      operatorName: ctx.joi.string(),
    });

    const needTokenCheck = await this.checkNeedTokenCheckForBatch(
      STAFF_ACTION_MAP.DELETE,
      body.adminIds,
      body.salesKdtId
    );
    if (needTokenCheck) {
      this.validator.required(token, '核实身份 token 不能为空');
      const result = await this.tokenCheck(token, IDENTITY_BIZ_TYPE.DELETE);
      if (!result) return ctx.fail(-1, '身份验证失败');
    }

    const params = {
      ...body,
      kdtId: +kdtId,
      operatorId,
    };

    const res = await new StaffBatchOperateService(ctx).batchDeleteAsync(params);
    ctx.success(res);
  }

  // 获取导入任务详情 url
  async getDownloadUrl(ctx) {
    const { kdtId, userId: operatorId } = ctx;
    const { batchId } = ctx.validator({
      batchId: ctx.joi.number(),
    });
    const params = {
      kdtId: +kdtId,
      operatorId,
      batchId,
    };
    const res = await new StaffBatchOperateService(ctx).getDownloadUrl(params);
    ctx.success(res);
  }

  // 批量删除员工前，获取员工信息
  async listStaffDetailInfo(ctx) {
    const { kdtId, userId: operatorId } = ctx;
    const query = ctx.validator(
      ctx.joi
        .object({
          adminIds: ctx.joi
            .array()
            .single()
            .items(ctx.joi.number()),
          mode: ctx.joi.string(),
          salesKdtId: ctx.joi.number().integer(),
          biz: ctx.joi.string(),
          operatorName: ctx.joi.string(),
        })
        .rename('adminIds[]', 'adminIds')
    );

    const params = {
      ...query,
      kdtId: +kdtId,
      operatorId,
    };

    const res = await new StaffBatchOperateService(ctx).listStaffDetailInfoAsync(params);
    ctx.success(res);
  }

  // 查询批量获取员工详情结果
  async queryListStaffDetailInfoResult(ctx) {
    const { kdtId, userId: operatorId } = ctx;
    const { taskId } = ctx.validator({
      taskId: ctx.joi.number(),
    });
    const params = {
      kdtId: +kdtId,
      operatorId,
      taskId,
    };
    const res = await new StaffBatchOperateService(ctx).queryListStaffDetailInfoResult(params);
    ctx.success(res);
  }

  // 异步查询批量删除员工结果
  async queryBatchDeleteResult(ctx) {
    const { kdtId, userId: operatorId } = ctx;
    const { taskId } = ctx.validator({
      taskId: ctx.joi.number(),
    });
    const params = {
      kdtId: +kdtId,
      operatorId,
      taskId,
    };

    const res = await new StaffBatchOperateService(ctx).queryBatchDeleteResult(params);
    ctx.success(res);
  }

  // 异步查询单店批量删除员工结果
  async querySingleBatchDeleteResult(ctx) {
    const { kdtId, userId: operatorId } = ctx;
    const { taskId } = ctx.validator({
      taskId: ctx.joi.number(),
    });
    const params = {
      kdtId: +kdtId,
      operatorId,
      taskId,
    };
    const res = await new SingleStaffService(ctx).queryBatchDeleteResult(params);
    ctx.success(res);
  }

  /**
   *
   * @param type 操作类型 delete、enable、disable
   * @param adminIds 操作员工id列表
   * @param targetKdtId 目标店铺kdtId
   */
  async checkNeedTokenCheckForBatch(type, adminIds, targetKdtId = null) {
    try {
      const { ctx } = this;
      const {
        userId: operatorId,
        kdtId,
        request: {
          body: { token },
        },
      } = ctx;
      const shopInfo = ctx.getState('shopInfo');
      const RoleIds = checkRetailShop(shopInfo)
        ? [RETAIL_ROLE.SUPER_ADMIN, RETAIL_ROLE.FINANCE_ADMIN]
        : [WSC_SAM_ROLE.SUPER_ADMIN, WSC_SAM_ROLE.FINANCE_ADMIN];

      let mode = '';
      targetKdtId = targetKdtId || kdtId;
      // 总部后台删除操作，查总部下所有组织的角色信息
      if (checkHqStore(shopInfo) && type === STAFF_ACTION_MAP.DELETE) {
        mode = SHOP_MODE.CHAIN_SHOP;
        // 合伙人后台删除操作，查合伙人下所有组织的角色信息
      } else if (checkPartnerStore(shopInfo) && type === STAFF_ACTION_MAP.DELETE) {
        mode = SHOP_MODE.PARTNER_SHOP;
      } else {
        mode = SHOP_MODE.SELF_SHOP;
      }

      const params = {
        adminIds,
        kdtId: +targetKdtId,
        operatorId,
        mode,
        roleIds: RoleIds,
        withRole: false,
        pageNo: 1,
        pageSize: 10,
      };
      const res = await new StaffQueryOuterService(ctx).batchShopStaffInfo(params);
      if (res.length > 0 && !token) {
        ctx.logger.error('扫码核身token为空', null, {
          type,
          targetKdtId,
          kdtId,
          adminIds,
        });
      }
      return res.length > 0;
    } catch (e) {
      return false;
    }
  }
}
module.exports = StaffBatchOperateController;
