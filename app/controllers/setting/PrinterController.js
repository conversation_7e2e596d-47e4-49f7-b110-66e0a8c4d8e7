const BaseController = require('../base/BaseController');
const PrinterService = require('../../services/api/printer/PrinterService');
const PrinterTicketStyleService = require('../../services/api/printer/PrinterTicketStyleService');
const ItemGroupService = require('../../services/api/shop/ItemGroupService');
const PrintUpgradeApi = require('../../services/api/iot/PrintUpgradeApi');
const PrinterMigrationService = require('../../services/api/mall-o2o/PrinterMigrationService');
const WaybillService = require('../../services/api/delivery/WaybillService');
const { fromApp } = require('../../constants');

class PrinterController extends BaseController {
  constructor(ctx) {
    super(ctx);
    this.ctx.biz = '设置-打印机';
  }

  async getIndexHtml(ctx) {
    const wayBillVersion = await new WaybillService(ctx)
      .getWaybillVersion(ctx.kdtId)
      .catch(() => 1);
    const isNewWayBill = wayBillVersion === 2;
    ctx.setGlobal({ isNewWayBill });
    await this.initPrinterUpgradeWhiteList(ctx);
    const data = await this.initPrinterUpgradeStatus(ctx);
    ctx.setGlobal({ printerUpgradeStatus: data.status });
    await ctx.render('setting/printer.html');
  }

  get userId() {
    const { ctx } = this;
    const userInfo = ctx.getLocalSession('userInfo');
    return +userInfo.userId;
  }

  async initPrinterUpgradeWhiteList(ctx) {
    const { kdtId } = ctx;
    const showPrinterUpgrade = await this.grayRelease('show-printer-upgrade', kdtId);
    ctx.setGlobal({ showPrinterUpgrade });
  }

  async initPrinterUpgradeStatus(ctx) {
    const { kdtId, userId: adminId } = ctx;
    return new PrintUpgradeApi(ctx).queryUpgradeStatus({
      adminId,
      kdtId,
      retailSource: fromApp,
    });
  }

  async getUpgradeStatus(ctx) {
    const result = await this.initPrinterUpgradeStatus(ctx);

    ctx.json(0, 'ok', result);
  }

  async upgradePrinter(ctx) {
    const { kdtId, userId } = ctx;

    const params = {
      kdtId,
      operatorHost: ctx.firstXff,
      operatorId: userId,
    };

    const result = await new PrinterMigrationService(ctx).migrationToIot(params);
    ctx.json(0, 'ok', result);
  }

  // 获取打印机列表
  async getPrinterListJson(ctx) {
    const query = ctx.query;
    const kdtId = +ctx.kdtId;
    const page = +query.page || 1;
    const size = +query.size || 20;

    const data = await new PrinterService(ctx).getPrinterList(kdtId, page, size);

    return ctx.json(0, 'ok', data || {});
  }

  // 新建打印机
  async postPrinterJson(ctx) {
    const query = ctx.request.body;
    const kdtId = +ctx.kdtId;
    const adminId = this.userId;
    const printerBrand = query.printer_brand;
    const paperWidth = +query.paper_width;
    const printerName = query.printer_name;
    const deviceNo = query.device_no;
    const deviceKey = query.device_key;
    const ticketStyleId = +query.ticket_style_id;
    const printTimes = +query.print_times;
    const supportLocalDelivery = !!query.supportLocalDelivery;
    const supportSelfFetch = !!query.supportSelfFetch;

    this.validator
      .required(adminId, 'adminId不能为空')
      .required(printerBrand, '打印机品牌不能为空')
      .required(paperWidth, '打印纸宽度不能为空')
      .required(printerName, '打印机备注名称不能为空')
      .required(deviceNo, '打印机设备号不能为空')
      .required(deviceKey, '打印机密钥不能为空')
      .required(ticketStyleId, '小票样式不能为空');

    const data = await new PrinterService(ctx).addPrinter(
      kdtId,
      adminId,
      printerBrand,
      paperWidth,
      printerName,
      deviceNo,
      deviceKey,
      ticketStyleId,
      printTimes,
      supportLocalDelivery,
      supportSelfFetch
    );

    return ctx.json(0, 'ok', data);
  }

  // 连接打印机
  async postConnectPrinterJson(ctx) {
    const kdtId = +ctx.kdtId;
    const query = ctx.request.body;
    const id = +query.id;
    const adminId = this.userId;

    this.validator.required(id, '打印机id不能为空');
    this.validator.required(adminId, '操作人id不能为空');

    const data = await new PrinterService(ctx).connectPrinter(kdtId, id, adminId);

    return ctx.json(0, 'ok', data);
  }

  // 断开连接打印机
  async postDisconnectPrinterJson(ctx) {
    const kdtId = +ctx.kdtId;
    const query = ctx.request.body;
    const id = +query.id;
    const adminId = this.userId;

    this.validator.required(id, '打印机id不能为空');
    this.validator.required(adminId, '操作人id不能为空');

    const data = await new PrinterService(ctx).disconnectPrinter(kdtId, id, adminId);

    return ctx.json(0, 'ok', data);
  }

  // 编辑打印机
  async putPrinterJson(ctx) {
    const query = ctx.request.body;
    const id = +query.id;
    const kdtId = +ctx.kdtId;
    const adminId = this.userId;
    const paperWidth = +query.paper_width;
    const printerName = query.printer_name;
    const ticketStyleId = +query.ticket_style_id;
    const printTimes = +query.print_times;
    const supportLocalDelivery = !!query.supportLocalDelivery;
    const supportSelfFetch = !!query.supportSelfFetch;

    this.validator
      .required(id, '打印机id不能为空')
      .required(adminId, '操作人id不能为空')
      .required(paperWidth, '打印纸宽度不能为空')
      .required(printerName, '打印机备注名称不能为空')
      .required(ticketStyleId, '小票样式不能为空');

    const data = await new PrinterService(ctx).updatePrinter(
      id,
      kdtId,
      adminId,
      paperWidth,
      printerName,
      ticketStyleId,
      printTimes,
      supportLocalDelivery,
      supportSelfFetch
    );

    return ctx.json(0, 'ok', data);
  }

  // 删除打印机
  async deletePrinterJson(ctx) {
    const kdtId = +ctx.kdtId;
    const query = ctx.request.body;
    const id = +query.id;
    const adminId = this.userId;
    this.validator.required(id, '打印机id不能为空');
    this.validator.required(adminId, '操作人id不能为空');

    const data = await new PrinterService(ctx).deletePrinter(kdtId, id, adminId);

    return ctx.json(0, 'ok', data);
  }

  // 打印机测试
  async postPrinterAssertJson(ctx) {
    const kdtId = +ctx.kdtId;
    const query = ctx.request.body;
    const printerBrand = query.printer_brand;
    const paperWidth = +query.paper_width;
    const deviceNo = query.device_no;
    const deviceKey = query.device_key;
    const ticketStyleId = +query.ticket_style_id;
    const printTimes = +query.print_times;

    this.validator
      .required(printerBrand, '打印机品牌不能为空')
      .required(paperWidth, '打印纸宽度不能为空')
      .required(deviceNo, '打印机设备号不能为空')
      .required(deviceKey, '打印机密钥不能为空')
      .required(ticketStyleId, '小票样式不能为空');

    const data = await new PrinterService(ctx).printTest(
      kdtId,
      printerBrand,
      paperWidth,
      deviceNo,
      deviceKey,
      ticketStyleId,
      printTimes
    );

    return ctx.json(0, 'ok', data);
  }

  // 获取小票样式列表
  async getReceiptListJson(ctx) {
    const kdtId = +ctx.kdtId;
    const adminId = this.userId;

    this.validator.required(adminId, 'adminId不能为空');

    const data = await new PrinterTicketStyleService(ctx).getReceiptList(kdtId, adminId);

    return ctx.json(0, 'ok', data);
  }

  // 新建小票样式
  async postReceiptJson(ctx) {
    const query = ctx.request.body;
    const kdtId = +ctx.kdtId;
    const ticketStyleName = query.ticket_style_name;
    const ticketStyleFont = +query.ticket_style_font;
    const itemGroupType = +query.item_group_type;
    const itemGroupIds = query.item_group_ids;
    const adminId = this.userId;

    this.validator
      .required(adminId, 'adminId不能为空')
      .required(ticketStyleName, '小票样式名称不能为空')
      .required(ticketStyleFont, '打印字体不能为空')
      .required(itemGroupType, '商品分组类型不能为空');

    // 选择部分商品，需要校验商品id
    if (+itemGroupType === 1) {
      this.validator.required(itemGroupIds, '商品分组的id不能为空');
    }

    const data = await new PrinterTicketStyleService(ctx).addReceiptStyle(
      kdtId,
      ticketStyleName,
      ticketStyleFont,
      itemGroupType,
      itemGroupIds,
      adminId
    );

    return ctx.json(0, 'ok', data);
  }

  // 删除小票样式
  async deleteReceiptJson(ctx) {
    const query = ctx.request.body;
    const kdtId = +ctx.kdtId;
    const id = +query.id;
    const adminId = this.userId;

    this.validator.required(id, '小票样式id不能为空');
    this.validator.required(adminId, 'adminId不能为空');

    const data = await new PrinterTicketStyleService(ctx).deleteReceipt(kdtId, id, adminId);

    return ctx.json(0, 'ok', data);
  }

  // 获取小票数据
  async getReceiptJson(ctx) {
    const kdtId = +ctx.kdtId;
    const query = ctx.query;
    const id = +query.id;

    this.validator.required(id, '小票样式id不能为空');

    const data = await new PrinterTicketStyleService(ctx).getDetailById(kdtId, id);

    return ctx.json(0, 'ok', data);
  }

  // 编辑小票样式
  async putReceiptJson(ctx) {
    const kdtId = +ctx.kdtId;
    const query = ctx.request.body;
    const id = +query.id;
    const ticketStyleName = query.ticket_style_name;
    const ticketStyleFont = +query.ticket_style_font;
    const itemGroupType = +query.item_group_type;
    const itemGroupIds = query.item_group_ids;
    const adminId = this.userId;

    this.validator
      .required(id, '小票样式id不能为空')
      .required(adminId, 'adminId不能为空')
      .required(ticketStyleName, '小票样式名称不能为空')
      .required(ticketStyleFont, '打印字体不能为空')
      .required(itemGroupType, '商品分组类型不能为空');

    if (+itemGroupType === 1) {
      this.validator.required(itemGroupIds, '商品分组的id不能为空');
    }

    const data = await new PrinterTicketStyleService(ctx).updateReceiptStyle(
      kdtId,
      id,
      ticketStyleName,
      ticketStyleFont,
      itemGroupType,
      itemGroupIds,
      adminId
    );

    return ctx.json(0, 'ok', data);
  }

  // 获取商品分组（TODO）
  async getGoodsGroupJson(ctx) {
    const query = ctx.query;
    const kdtId = +ctx.kdtId;
    const page = +query.page || 1;
    const size = +query.size || 20;

    const data = await new ItemGroupService(ctx).getGoodsGroup(kdtId, page, size);

    return ctx.json(0, 'ok', data);
  }
}

module.exports = PrinterController;
