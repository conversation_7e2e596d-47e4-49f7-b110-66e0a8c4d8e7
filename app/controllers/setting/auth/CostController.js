const BaseController = require('../../base/BaseController');
const OperationCertService = require('../../../services/api/shop-center/OperationCertService');
const MobileVerificationRecordService = require('../../../services/api/uic/MobileVerificationRecordService');
const MobileVerificationManagerService = require('../../../services/api/uic/MobileVerificationManagerService');

class AuthorizeController extends BaseController {
  constructor(ctx) {
    super(ctx);
  }

  async getCostExportListHtml(ctx) {
    await this.initUserInfo();
    await ctx.render('setting/auth/cost-export-list.html');
  }

  _getCostListParams(ctx) {
    const {
      validateTimeRange,
      subKdtIds = '',
      mobile = '',
      verifyTypes,
      deductionStatus,
      pageNo,
      pageSize,
    } = ctx.getRequestData();
    let [beginTime, endTime] = (validateTimeRange || '').split('_');
    beginTime = beginTime ? +new Date(beginTime) : null;
    endTime = endTime ? +new Date(endTime) : null;

    const params = {
      beginTime,
      endTime,

      kdtId: ctx.kdtId,
      subKdtIds: subKdtIds.split('_').filter((a) => a),
      adminId: ctx.userId,

      mobile: mobile.trim() || null,
      verifyTypeList: verifyTypes?.split(','),
      deductionStatus,

      pageNo,
      pageSize,
    };
    return params;
  }

  // 获取授权记录
  async getAuthCostList(ctx) {
    const params = this._getCostListParams(ctx);
    const data = await new MobileVerificationRecordService(ctx).getMobileVerificationRecords(
      params
    );

    return ctx.json(0, 'ok', data);
  }

  async exportAuthCostList(ctx) {
    const params = this._getCostListParams(ctx);
    const data = await new MobileVerificationRecordService(ctx).handleExportApply(params);

    return ctx.json(0, 'ok', data);
  }

  // 获取报表
  async getCostExportList(ctx) {
    const { page: pageNo = 1, size: pageSize = 20 } = ctx.query;
    const params = {
      pageNo,
      pageSize,
      kdtId: ctx.kdtId,
    };
    const data = await new MobileVerificationRecordService(ctx).listExportApply(params);
    return ctx.json(0, 'ok', data);
  }

  // 获取指定报表的下载地址
  async queryPrivateUrl(ctx) {
    const { kdtId, userId } = ctx;
    const { exportId, operationType } = ctx.request.body;

    // 检查是否通过短信校验
    const captchaResult = await new OperationCertService(ctx).getOperationCertMethod({
      kdtId,
      operationType,
      userId,
    });

    // 通过短信校验
    if (captchaResult.method === 'NO_CERT') {
      const params = {
        kdtId,
        applyId: +exportId,
        adminId: userId,
      };
      const realUrl = await new MobileVerificationRecordService(
        ctx
      ).getMobileVerificationDownloadUrl(params);

      return ctx.json(0, 'ok', realUrl);
    }

    return ctx.json(0, 'ok', '');
  }

  // 获取指定报表的下载记录
  async getListRecords(ctx) {
    const { page, pageSize, operateObjectId } = ctx.query;
    const params = {
      kdtId: ctx.kdtId,
      pageNo: +page,
      pageSize: +pageSize,
      applyId: +operateObjectId,
    };
    const result = await new MobileVerificationRecordService(ctx).listExportDownloadRecord(params);
    ctx.json(0, 'ok', result);
  }

  async getRevealConfig(ctx) {
    const data = await new MobileVerificationManagerService(ctx).getRevealConfig({
      kdtId: ctx.kdtId,
    });

    return ctx.json(0, 'ok', data);
  }
}

module.exports = AuthorizeController;
