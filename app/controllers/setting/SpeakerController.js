const get = require('lodash/get');
const { ShopAbility, branchStoreRole, checkLiteOnlineStoreManager } = require('@youzan/utils-shop');
const { ShopAbilityUtil } = require('@youzan/plugin-shop-ability');
const BaseController = require('../base/BaseController');
const HQStoreSearchService = require('../../services/api/shop/HQStoreSearchService');

class SpeakerController extends BaseController {
  constructor(ctx) {
    super(ctx);
    this.ctx.biz = '设置-音箱';
  }

  async getIndexHtml(ctx) {
    let roles = [];
    let liteStores = [];
    if (
      await new ShopAbilityUtil(ctx).checkAbilityValid({
        keys: [ShopAbility.LiteOnlineStoreManageAbility],
      })
    ) {
      roles = await this.getStaffRoles(ctx);
    }
    const isLiteOnlineStoremanager = checkLiteOnlineStoreManager(roles);
    if (isLiteOnlineStoremanager) {
      const res = await new HQStoreSearchService(ctx).searchWithDataPermission({
        kdtId: ctx.kdtId,
        adminId: ctx.userId,
        pageSize: 20,
        retailSource: 'wsc-pc-v4',
        shopRoleList: [branchStoreRole],
        isOnlineOpen: true,
        isLiteOnlineStore: true,
        // NOTE: 默认不去获取如下信息，加快接口调用速度
        appendShopLifecycleEndTime: false,
        appendOfflineBusinessHours: false,
        appendPosPointNum: false,
        appendLackInfo: false,
      });
      liteStores = get(res, 'items', []);
    }
    ctx.setGlobal('roles', roles);
    ctx.setGlobal('liteStores', liteStores);
    ctx.setGlobal('isLiteOnlineStoremanager', isLiteOnlineStoremanager);
    await ctx.render('setting/speaker.html');
  }
}

module.exports = SpeakerController;
