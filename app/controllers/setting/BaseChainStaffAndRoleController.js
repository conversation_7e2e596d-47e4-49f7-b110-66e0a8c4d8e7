const BaseController = require('./BaseStaffController');
const ChainStaffService = require('../../services/api/sam/ChainStaffService');
const SalesmanWhiteOrGrayApiService = require('../../services/api/salesman/SalesmanWhiteOrGrayApiService');
const SalesmanUpgradeFrontService = require('../../services/api/salesman/SalesmanUpgradeFrontService');
const ChainManageService = require('../../services/api/staff/ChainManageService');
const SingleManageService = require('../../services/api/staff/SingleManageService');
const RoleService = require('../../services/api/rig/RoleService');
const RetailRoleService = require('../../services/api/sam/RetailRoleService');
const DescendentShopUsageStatisticsService = require('../../services/api/staff/DescendentShopUsageStatisticsService');
const AbilityReadService = require('../../services/api/shop-center/AbilityReadService');
const SalesOrgComponentService = require('../../services/api/shopcenter/shopfront/SalesOrgComponentService');
const SalesOrgStructureMgrService = require('../../services/api/shopcenter/outer/SalesOrgStructureMgrService');
const SmsCaptchaService = require('../../services/api/authority/SmsCaptchaService');
const StaffQueryService = require('../../services/api/staff/StaffQueryService');
const StaffQueryOpenService = require('../../services/api/staff/StaffQueryOpenService');
const AuthTokenService = require('../../services/api/authority/AuthTokenService');

const {
  SALES_UPGRADE_STATUS_MAP,
  STAFF_ACTION_MAP,
  IDENTITY_BIZ_TYPE,
  WSC_SAM_ROLE,
  RETAIL_ROLE,
  SHOP_MODE,
} = require('../../constants');

const {
  checkHqStore,
  checkRetailShop,
  checkUnifiedPartnerStore,
  checkUnifiedOnlineBranchStore,
  checkUnifiedOfflineBranchStore,
  checkPartnerStore,
} = require('@youzan/utils-shop');

class BaseChainStaffAndRoleController extends BaseController {
  constructor(ctx) {
    super(ctx);
    this.ctx.biz = '设置-连锁员工管理';
  }

  async getKeeperAdminId(kdtId, operatorId) {
    try {
      const rst = await new SingleManageService(this.ctx).getShopKeeper({
        kdtId,
        operatorId,
      });
      return rst.adminId;
    } catch (err) {
      this.ctx.logger.warn(`店铺负责人基础信息获取失败：${err.message}`, err);
      return '';
    }
  }

  getBizCode(shopInfo) {
    if (checkHqStore(shopInfo)) {
      return 'retail_hq';
    }
    if (checkUnifiedPartnerStore(shopInfo)) {
      return 'retail_partner';
    }
    if (checkUnifiedOnlineBranchStore(shopInfo)) {
      return 'retail_online';
    }
    if (checkUnifiedOfflineBranchStore(shopInfo)) {
      return 'retail_store';
    }
    // 前置仓
    if (shopInfo.shopRole === 7) {
      return 'retail_front_warehouse';
    }
  }

  async getSalesUpgradeStatus(ctx, params) {
    const { adminId, retailSource, hqKdtId, kdtId } = params;
    const shopInfo = ctx.getState('shopInfo');

    if (!checkRetailShop(shopInfo)) {
      return SALES_UPGRADE_STATUS_MAP.NONE;
    }

    const rst = await new SalesmanWhiteOrGrayApiService(ctx).inWhiteList(
      'chain_salesman_merge',
      kdtId
    );

    if (rst) {
      return SALES_UPGRADE_STATUS_MAP.DONE;
    }

    const res = await new SalesmanUpgradeFrontService(ctx).getShopUpgradeStatus({
      adminId,
      kdtId,
      hqKdtId,
      retailSource,
    });

    return res.inUpgrade || res.isAccountSetting
      ? SALES_UPGRADE_STATUS_MAP.INPROCESS
      : SALES_UPGRADE_STATUS_MAP.NONE;
  }

  async isTargetShopCreator(ctx, adminId, targetKdtId) {
    try {
      const isCreator = await new StaffQueryService(ctx).isShopKeeper({
        adminId,
        kdtId: targetKdtId,
      });
      return isCreator;
    } catch (e) {
      return false;
    }
  }

  async getIsCurrentKdtIdCreator(ctx) {
    const { kdtId, userId } = ctx;
    return this.isTargetShopCreator(ctx, userId, kdtId);
  }

  // 判断当前店铺是否在区域组织灰名单内
  async isInRegionalGrayList(ctx) {
    const { kdtId } = ctx;
    const result = await this.grayRelease('org_mode_upgrade', kdtId);
    return ctx.json(0, 'OK', result);
  }

  async searchOrgList(params) {
    const { ctx } = this;
    const res = await new SalesOrgComponentService(ctx).searchOrgWithStart(params);
    const treeData = res || [];
    if (treeData.length > 0) {
      const data = await this.searchOrgList({
        ...params,
        startOrgId: treeData[treeData.length - 1].orgId,
      });
      return treeData.concat(data);
    } else {
      return treeData;
    }
  }

  // 获取区域组织列表
  async getStaffOrgList(ctx) {
    const { orgId, orgTypes } = ctx.query;
    const params = {
      orgId,
      startOrgId: 0,
      recursive: true,
      orgTypes: orgTypes ? orgTypes.split(',') : [],
    };
    const [rootOrg, childOrgs] = await Promise.all([
      new SalesOrgStructureMgrService(ctx).get(orgId), // 获取根节点数据
      this.searchOrgList(params), // 获取子组织数据
    ]);
    const res = [{ ...rootOrg }, ...childOrgs];
    return ctx.json(0, 'ok', res);
  }

  // 获取员工详情
  async getStaffDetailJSON(ctx) {
    const { kdtId, userId } = ctx;
    const { targetUserId, pageSize, pageNo, mode, orgId } = ctx.query;
    const rst = await new ChainManageService(ctx).getStaffDetailInfo({
      adminId: targetUserId,
      kdtId,
      operatorId: userId,
      pageSize,
      pageNo,
      mode,
      orgId,
    });
    return ctx.json(0, 'ok', rst);
  }

  /**
   * 获取当前店铺的类型标识
   * @returns {string}
   * @private
   */
  _getMode() {
    const { ctx } = this;
    const shopInfo = ctx.getState('shopInfo');
    if (checkPartnerStore(shopInfo)) {
      return SHOP_MODE.PARTNER_SHOP;
    }
    if (checkHqStore(shopInfo)) {
      return SHOP_MODE.CHAIN_SHOP;
    }
    return SHOP_MODE.SELF_SHOP;
  }

  async getStaffListJSON(ctx) {
    const { userId, kdtId } = ctx;
    const { targetKdtId, pageNo, pageSize, roleId, depId, keyword, status } = ctx.query;

    const rst = await new ChainManageService(ctx).findForManage({
      kdtId: targetKdtId ? targetKdtId : kdtId,
      operatorId: userId,
      keywordMatchFieldsMap: {
        NAME: keyword || '',
        ACCOUNT: keyword || '',
      },
      mode: targetKdtId ? SHOP_MODE.SELF_SHOP : this._getMode(),
      pageNo,
      pageSize,
      roleIds: roleId ? [roleId] : undefined,
      depId: depId ? depId : undefined,
      statusList: status ? [+status] : undefined,
    });
    return ctx.json(0, 'ok', rst);
  }

  // 变更连锁店铺负责人
  async changeChainShopKeeper(ctx) {
    const {
      request: {
        body: { token, mobile, smsCaptcha, targetKdtId, bizType, ...modifyDTO },
      },
      kdtId,
    } = ctx;

    const result = await this.tokenCheck(
      token,
      bizType ? Number(bizType) : IDENTITY_BIZ_TYPE.CHANGE_SHOP_OWNER
    );
    if (!result) return ctx.fail(-1, '身份验证失败');

    const getparams = {
      biz: 'check_shop_owner',
      smsCaptcha,
      mobile,
    };
    const smsCheckResult = await new SmsCaptchaService(ctx).validSmsCaptcha(getparams);
    if (!smsCheckResult.success) return ctx.fail(10201, '授权验证码错误！');

    // 获取授权人信息
    const authVerificationInfo = await new AuthTokenService(ctx).getAuthVerificationInfo({
      token,
    });
    const authMobile = authVerificationInfo?.mobile;

    const res = await new ChainStaffService(ctx).changeShopKeeperAsync({
      ...modifyDTO,
      targetKdtId,
      kdtId,
      verifyMobile: authMobile,
    });
    ctx.success(res);
  }

  // 添加员工
  async create(ctx) {
    const {
      request: {
        body: { token, ...createDTO },
      },
      kdtId,
      userId: operatorId,
    } = ctx;

    const needTokenCheck = await this.checkNeedTokenCheck(STAFF_ACTION_MAP.CREATE);
    if (needTokenCheck) {
      this.validator.required(token, '核实身份 token 不能为空');
      const result = await this.tokenCheck(token, IDENTITY_BIZ_TYPE.CREATE_OR_UPDATE);
      if (!result) return ctx.fail(-1, '身份验证失败');
    }

    const params = {
      ...createDTO,
      kdtId,
      operatorId,
      isValidCaptcha: false,
    };
    const res = await new ChainStaffService(ctx).createAsync(params);
    ctx.success(res);
  }

  // 更新员工
  async update(ctx) {
    const {
      request: {
        body: { token, ...updateDTO },
      },
      kdtId,
      userId: operatorId,
    } = ctx;

    const needTokenCheck = await this.checkNeedTokenCheck(STAFF_ACTION_MAP.UPDATE);
    if (needTokenCheck) {
      this.validator.required(token, '核实身份 token 不能为空');
      const result = await this.tokenCheck(token, IDENTITY_BIZ_TYPE.CREATE_OR_UPDATE);
      if (!result) return ctx.fail(-1, '身份验证失败');
    }

    const params = {
      ...updateDTO,
      kdtId,
      operatorId,
      isValidCaptcha: false,
    };
    const res = await new ChainStaffService(ctx).updateAsync(params);
    ctx.success(res);
  }

  // 删除连锁员工
  async delete(ctx) {
    const {
      request: { body },
      kdtId,
      userId: operatorId,
    } = ctx;

    const needTokenCheck = await this.checkNeedTokenCheck(STAFF_ACTION_MAP.DELETE);
    if (needTokenCheck) {
      this.validator.required(body.token, '核实身份 token 不能为空');
      const result = await this.tokenCheck(body.token, IDENTITY_BIZ_TYPE.DELETE);
      if (!result) return ctx.fail(-1, '身份验证失败');
    }

    const params = {
      ...body,
      kdtId,
      operatorId,
      isValidCaptcha: false,
    };
    const res = await new ChainStaffService(ctx).delete(params);
    ctx.success(res);
  }

  /**
   * 从格式化字符串中解析包含某个重要角色的角色列表
   * @param chainStaffDepRolesStr 例：58824936-1 58824737-1,5 表示 kdtId-高管,普通管理员 空格分割多个店铺
   * @param importantRoleId
   * @returns {[]}
   * @private
   */
  _getImportantRolesByRoleStr(chainStaffDepRolesStr, importantRoleId) {
    const result = [];
    chainStaffDepRolesStr.split(' ').forEach((shopItem) => {
      const [kdtIdAndDep, rolesStr = ''] = shopItem.split('-');
      const roles = rolesStr
        .split(',')
        .filter((roleId) => importantRoleId.indexOf(Number(roleId)) !== -1);
      if (roles.length) {
        result.push({
          kdtIdAndDep,
          roles,
        });
      }
    });
    return result;
  }

  /**
   * 角色列表格式化为字符串
   * @param roles
   * @returns {*}
   * @private
   */
  _formatRolesToStr(roles) {
    return roles
      .sort((a, b) => Number(a.kdtIdAndDep.split(',')[0]) - Number(b.kdtIdAndDep.split(',')[0]))
      .reduce((pre, item) => {
        return `${pre} ${item.kdtIdAndDep}-${item.roles.sort((a, b) => a - b).join(',')}`;
      }, '');
  }

  /**
   * 判断是否需要扫码核身，前端保持一致
   * 1、新设置高管或财务的 需要核验
   * 2、原来是高管或财务的 需要核验
   * 3、高管或财务权限无变化，不需要核验
   * 4、当前登录的是创始人，操作的是高管或财务，除了启用、停用、删除、编辑，其它都不需要核验
   * @param type 操作类型：create、update、delete、enable、disable
   * @returns {Promise<boolean>}
   */
  async checkNeedTokenCheck(type) {
    const { ctx } = this;
    const {
      kdtId,
      request: {
        body: { token },
      },
    } = ctx;

    const oldRoles = [
      // 示例数据
      // {
      //   kdtId: 58824936,
      //   roles: [1,3,4]
      // }
    ];
    let newRoles = [];

    const isRetail = ctx.shopType === 7;
    const importantRoleId = [
      WSC_SAM_ROLE.SUPER_ADMIN,
      isRetail ? RETAIL_ROLE.FINANCE_ADMIN : WSC_SAM_ROLE.FINANCE_ADMIN,
    ];

    // 收集 newRoles，创建、保存 有新的角色添加进来
    if ([STAFF_ACTION_MAP.CREATE, STAFF_ACTION_MAP.UPDATE].indexOf(type) !== -1) {
      const { chainStaffDepRolesStr = '' } = ctx.request.body;
      newRoles = this._getImportantRolesByRoleStr(chainStaffDepRolesStr, importantRoleId);
    }

    // 收集 adminId、oldRoles，启用、停用、保存、删除 需要获取原有的角色
    let adminId = '';
    if (
      [
        STAFF_ACTION_MAP.ENABLE,
        STAFF_ACTION_MAP.DISABLE,
        STAFF_ACTION_MAP.UPDATE,
        STAFF_ACTION_MAP.DELETE,
      ].indexOf(type) !== -1
    ) {
      adminId = ctx.request.body.adminId;

      const maxCheckCount = 200;
      const params = {
        adminId,
        kdtId,
        operatorId: ctx.userId,
        mode: this._getMode(),
        pageNo: 1,
        pageSize: maxCheckCount,
      };

      // 启用、停用 只对当前店铺生效
      if ([STAFF_ACTION_MAP.ENABLE, STAFF_ACTION_MAP.DISABLE].indexOf(type) !== -1) {
        params.mode = SHOP_MODE.SELF_SHOP;
        params.kdtId = +ctx.request.body.targetKdtId;
      }

      const { staffDetailShopInfoDTOS, total } = await new ChainManageService(
        ctx
      ).getStaffDetailInfo(params);
      // TODO 超过 200 条时，因为 Dubbo 会超时原因，安全优先暂先 浏览器 + Node 强制扫码。
      if (
        type === STAFF_ACTION_MAP.UPDATE &&
        (total > maxCheckCount || newRoles.length > maxCheckCount)
      )
        return true;
      staffDetailShopInfoDTOS.forEach((item) => {
        const roles = item.staffOrgDepRoleDetailDTOS
          .map((item) => item.roleId)
          .filter((roleId) => importantRoleId.indexOf(Number(roleId)) !== -1);
        if (roles.length) {
          const depId = item?.staffOrgDepRoleDetailDTOS[0]?.depId;
          oldRoles.push({
            kdtIdAndDep: depId ? `${item.kdtId},${depId}` : `${item.kdtId}`,
            roles,
          });
        }
      });
    }

    let hasImportantRole = false;
    if (type === STAFF_ACTION_MAP.CREATE) {
      hasImportantRole = newRoles.length;
    } else if (type === STAFF_ACTION_MAP.UPDATE) {
      // 统一格式化成字符串来比较，这样更直观点
      hasImportantRole = this._formatRolesToStr(newRoles) !== this._formatRolesToStr(oldRoles);
    } else {
      hasImportantRole = oldRoles.length;
    }

    let shouldCheck = hasImportantRole;
    // 判断当前登录的是否是创始人
    const isCurrentKdtIdCreator = await this.getIsCurrentKdtIdCreator(ctx);
    // 创始人，除了启用、停用、删除、编辑，其它都不需要核验。编辑需要校验的原因是防止以下漏洞：
    // 比如正常情况下删除高级管理员会先验证店铺创建者的身份，但先将其编辑为管理员或者其他身份，然后就可以删除
    if (hasImportantRole && isCurrentKdtIdCreator) {
      shouldCheck =
        [
          STAFF_ACTION_MAP.ENABLE,
          STAFF_ACTION_MAP.DISABLE,
          STAFF_ACTION_MAP.DELETE,
          STAFF_ACTION_MAP.UPDATE,
        ].indexOf(type) !== -1;
    }
    if (shouldCheck && !token) {
      ctx.logger.error('扫码核身token为空', null, {
        type,
        isCurrentKdtIdCreator,
        hasImportantRole,
        newRoles,
        oldRoles,
        newRolesStr: this._formatRolesToStr(newRoles),
        oldRolesStr: this._formatRolesToStr(oldRoles),
      });
    }

    return shouldCheck;
  }

  async addRole(ctx) {
    const params = ctx.request.body;
    params.kdtId = ctx.kdtId;
    params.roleTypeValue = +params.roleType;
    const res = await new RoleService(ctx).add(params);
    ctx.json(0, 'ok', res);
  }

  async deleteRole(ctx) {
    const params = ctx.request.body;
    params.kdtId = ctx.kdtId;
    params.roleTypeValue = +params.roleType;
    const res = await new RoleService(ctx).delete(params);
    ctx.json(0, 'ok', res);
  }

  async updateRole(ctx) {
    const params = ctx.request.body;
    params.kdtId = ctx.kdtId;
    params.roleTypeValue = +params.roleType;
    const res = await new RoleService(ctx).update(params);
    ctx.json(0, 'ok', res);
  }

  async retailSaveRole(ctx) {
    const params = ctx.request.body;
    params.kdtId = ctx.kdtId;
    const res = await new RetailRoleService(ctx).addCustomizeRole(params);
    ctx.json(0, 'ok', res);
  }

  async retailDeleteRole(ctx) {
    const params = ctx.request.body;
    params.kdtId = ctx.kdtId;
    const res = await new RetailRoleService(ctx).deleteCustomRole(params);
    ctx.json(0, 'ok', res);
  }

  async queryDescendentShopUsage(ctx) {
    const params = ctx.request.body;
    params.kdtId = ctx.kdtId;
    const res = await new DescendentShopUsageStatisticsService(
      ctx
    ).queryDescendentShopUsageStatistics(params);
    ctx.json(0, 'ok', res);
  }

  // 查询门店的店铺能力
  async queryShopAbilityInfo(ctx) {
    const { targetKdtId, ability } = ctx.validator(
      ctx.joi.object({
        targetKdtId: ctx.joi.number(),
        ability: ctx.joi.string(),
      })
    );

    const res = await new AbilityReadService(ctx).queryShopAbilityInfo(targetKdtId, ability);
    ctx.json(0, 'ok', res);
  }

  /**
   * 分页查询店铺员工
   * 目前用于操作人组件获取数据
   */
  async queryStaffInfoByKdtId(ctx) {
    const { kdtId, userId } = ctx;
    const { keyword, pageSize, pageNo } = ctx.query;
    const res = await new StaffQueryOpenService(ctx).pageQueryStaffInfoByKdtId({
      kdtId,
      operatorId: userId,
      keyword,
      pageSize,
      pageNo,
    });
    ctx.json(0, 'ok', res);
  }
}

module.exports = BaseChainStaffAndRoleController;
