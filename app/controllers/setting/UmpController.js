const get = require('lodash/get');
const _isObject = require('lodash/isObject');
const BaseController = require('../base/BaseController');
const ActivityRemindService = require('../../services/api/ump/ActivityRemindService');
const StaffServiceV2 = require('../../services/api/sam/StaffServiceV2');
const RoleService = require('../../services/api/sam/RoleService');

class UmpController extends BaseController {
  constructor(ctx) {
    super(ctx);
    this.ctx.biz = '设置-应用设置';
  }

  async getIndexHtml(ctx) {
    await ctx.render('setting/ump.html');
  }

  // 获取活动列表
  async getActivityListJson(ctx) {
    const { kdtId } = ctx;

    const result = await new ActivityRemindService(ctx).getAppTypes(kdtId);
    ctx.json(0, 'ok', result);
  }

  // 获取员工列表
  async getStaffListJson(ctx) {
    const {
      kdtId,
      isSuperStore,
      query: { keyWord = '', pageNo = 1, pageSize = 8, roleId = '' },
    } = ctx;

    const params = {
      kdtId,
      pageNo,
      pageSize,
      keyWord,
      biz: isSuperStore ? 'retail' : 'wsc',
    };

    roleId !== '' && (params.roleId = roleId);

    const result = await new StaffServiceV2(ctx).findStaffsPages(params);

    ctx.json(0, 'ok', result);
  }

  // 获取角色种类信息
  async getStaffRolesJson(ctx) {
    const { kdtId, isSuperStore } = ctx;

    const result = await new RoleService(ctx).find({
      biz: isSuperStore ? 'retail' : 'wsc',
      kdtId,
      pageNo: 1,
      pageSize: 300,
    });
    ctx.json(0, 'ok', result);
  }

  // 获取提醒信息
  async getRemindJson(ctx) {
    const { kdtId, isSuperStore } = ctx;

    const result = await new ActivityRemindService(ctx).getRemind(kdtId);
    const employeeIds = get(result, 'employeeIds', []);
    let employeeList = [];
    if (employeeIds.length > 0) {
      const param = {
        kdtId,
        adminIds: employeeIds,
        withRole: true,
        bizs: [isSuperStore ? 'retail' : 'wsc'],
      };
      employeeList = await new StaffServiceV2(ctx).findByKdtIdAndAdminIds(param);
    }
    if (_isObject(result)) result.employeeList = employeeList;
    ctx.json(0, 'ok', result);
  }

  // 创建提醒
  async createRemindJson(ctx) {
    const {
      kdtId,
      request: { body },
    } = ctx;
    body.shopId = kdtId;

    this.validator
      .required(body.mode, '提醒方式字段缺失')
      .required(body.appTypes, '活动选择字段缺失')
      .required(body.remindTimeUnit, '提醒时间字段缺失')
      .required(body.employeeIds, '员工信息缺失');

    const result = await new ActivityRemindService(ctx).createRemind(body);
    ctx.json(0, 'ok', result);
  }

  // 编辑提醒
  async editRemindJson(ctx) {
    const {
      kdtId,
      request: { body },
    } = ctx;
    body.shopId = kdtId;

    this.validator
      .required(body.mode, '提醒方式字段缺失')
      .required(body.appTypes, '活动选择字段缺失')
      .required(body.remindTimeUnit, '提醒时间字段缺失')
      .required(body.employeeIds, '员工信息缺失');

    const result = await new ActivityRemindService(ctx).editRemind(body);
    ctx.json(0, 'ok', result);
  }
}

module.exports = UmpController;
