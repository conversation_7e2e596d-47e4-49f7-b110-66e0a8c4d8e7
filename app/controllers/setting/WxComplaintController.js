const BaseController = require('../base/BaseController');
const WechatPayComplaintRecordService = require('../../services/api/wx-complaint/WechatPayComplaintRecordService');

class WxComplaintController extends BaseController {
  // 2022.08.18
  // v4商家后台查询微信支付申诉记录
  async listForComplaint(ctx) {
    const { kdtId, pageNum = 1, pageSize = 10 } = ctx;
    const res = await new WechatPayComplaintRecordService(ctx).query({
      kdtId,
      pageNum,
      pageSize,
      ...ctx.query,
    });
    return ctx.json(0, 'OK', res);
  }
}

module.exports = WxComplaintController;
