const BaseController = require('../base/BaseController');
const MarketRemoteService = require('../../services/api/yop/MarketRemoteService');
const RightsConfigRemoteService = require('../../services/api/yop/RightsConfigRemoteService');
const CmsAdService = require('../../services/api/cms/CmsAdService');
const OrderRemoteService = require('../../services/api/yop/OrderRemoteService');
class ServiceCenterController extends BaseController {
  constructor(ctx) {
    super(ctx);
    this.ctx.biz = '设置-服务中心';
  }

  async getIndexHtml(ctx) {
    const prodVersion = await this.getShopProdVersion(ctx);
    ctx.setGlobal('prodVersion', prodVersion);

    if (!ctx.isYZEdu) {
      return ctx.redirect('/v4/subscribe/service-center');
    }

    await ctx.render('setting/service_center.html');
  }

  async getShopInfo(ctx) {
    const data = await new MarketRemoteService(ctx).getServerInfo(ctx.kdtId);
    return ctx.json(0, '', data);
  }

  async queryRightsConfigInfo(ctx) {
    const { isSuperStore } = ctx.state;
    // 零售 or 微商城
    const appId = isSuperStore ? 6075 : 873;
    const data = await new RightsConfigRemoteService(ctx).queryRightsConfigInfo({
      appId,
    });
    return ctx.json(0, '', data);
  }

  async getBannerData(ctx) {
    const { materialGroupId } = ctx.query;
    const data = await new CmsAdService(ctx).search({
      materialGroupId,
    });
    return ctx.json(0, '', data);
  }

  async simpleTradeCheckForItemList(ctx) {
    const { kdtId } = ctx;
    const { orderWay = 'NORMAL', tradeCheckItemFormList = [] } = ctx.request.body || {};

    const form = {
      kdtId,
      orderWay,
      operatorId: '' + ctx.getLocalSession('userInfo').id,
      tradeCheckItemFormList,
    };
    const data = await new OrderRemoteService(ctx).simpleTradeCheckForItemList(form);
    return ctx.json(0, '', data);
  }
}

module.exports = ServiceCenterController;
