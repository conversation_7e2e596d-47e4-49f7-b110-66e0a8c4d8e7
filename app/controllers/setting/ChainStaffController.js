const BusinessException = require('@youzan/wsc-pc-base/app/exceptions/BusinessException');
const BaseChainStaffAndRoleController = require('./BaseChainStaffAndRoleController');
const HQStoreSearchService = require('../../services/api/shop/HQStoreSearchService');
const StaffServiceV2 = require('../../services/api/sam/StaffServiceV2');
const getChainStaffText = require('./text/chain-staff');
const GrayService = require('../../services/api/rig/GrayService');
const StaffQueryOuterService = require('../../services/api/staff/StaffQueryOuterService');
const ShopChainPageQueryOuterService = require('../../services/api/staff/ShopChainPageQueryOuterService');
const ShopManageMiscService = require('../../services/api/shop/ShopManageMiscService');

const { retailSource } = require('../../constants');
const SUPER_ADMIN = 1;

const {
  checkUnifiedShop,
  checkEduChainStore,
  checkWscChainStore,
  checkRetailMinimalistShop,
} = require('@youzan/utils-shop');
const { get, isBoolean } = require('lodash');

class ChainStaffController extends BaseChainStaffAndRoleController {
  constructor(ctx) {
    super(ctx);
    this.ctx.biz = '设置-连锁员工管理';
  }

  // 获取店铺列表
  async getShopList(ctx) {
    const { kdtId, userId } = ctx;
    const { isHasPermission } = ctx.validator({
      isHasPermission: ctx.joi.boolean().required(),
    });
    await this.initUserInfo();
    const shopInfo = ctx.getState('shopInfo');
    const hqKdtId = shopInfo.rootKdtId;
    const shopRoleList = [2, 4, 6, 7];
    const params = {
      kdtId,
      hqKdtId,
      shopRoleList,
      adminId: userId,
      retailSource,
    };
    const shopList = await new HQStoreSearchService(ctx).getAllStores(params, isHasPermission);
    ctx.success(shopList);
  }

  // 获取店铺列表
  async searchShopList(ctx) {
    const { kdtId, userId } = ctx;

    const {
      storeNameOrAddress,
      shopRoleList,
      isPerm = false,
      isOfflineOpen,
      isOnlineOpen,
      orgCategory,
    } = ctx.validator({
      storeNameOrAddress: ctx.joi.string(),
      shopRoleList: ctx.joi.array().items(ctx.joi.number().integer()),
      isPerm: ctx.joi.boolean(),
      isOfflineOpen: ctx.joi.boolean(),
      isOnlineOpen: ctx.joi.boolean(),
      orgCategory: ctx.joi.string(),
    });

    const shopInfo = ctx.getState('shopInfo');
    const hqKdtId = shopInfo.rootKdtId;

    const params = {
      kdtId,
      hqKdtId,
      shopRoleList,
      adminId: userId,
      retailSource,
      storeNameOrAddress,
      orgCategory,
    };

    if (isBoolean(isOfflineOpen)) {
      params.isOfflineOpen = isOfflineOpen;
    }
    if (isBoolean(isOnlineOpen)) {
      params.isOnlineOpen = isOnlineOpen;
    }

    if (orgCategory) {
      params.orgCategorys = [orgCategory];
    }

    // 目前采购一次全部加载数据
    const shopList = await new HQStoreSearchService(ctx).getAllStores(params, isPerm);
    ctx.success(shopList);
  }

  // 获取角色列表
  async getShopRoleList(ctx) {
    const { kdtId } = ctx;
    // 目前采购一次全部加载数据
    const shopList = await new ShopManageMiscService(ctx).listDescendentOrgCategories(kdtId);
    ctx.success(shopList);
  }

  /**
   * 连锁员工管理页面渲染
   * @param {*} ctx
   * @memberof ChainStaffController
   */
  async getIndexHtml(ctx) {
    const { kdtId, userId } = ctx;
    await this.initUserInfo();
    const shopInfo = ctx.getState('shopInfo');
    const userInfo = ctx.getState('userInfo');

    const hqKdtId = shopInfo.rootKdtId;

    if (!hqKdtId) {
      throw new BusinessException(403, '当前店铺类型不支持访问此页面');
    }

    const currentShopKeeperIdPromise = this.getKeeperAdminId(kdtId, userId);
    const hqShopKeeperIdPromise = this.getKeeperAdminId(hqKdtId, userId);

    const isRetailShopV3 = checkUnifiedShop(shopInfo) || checkRetailMinimalistShop(shopInfo);
    const isEduChainShop = checkEduChainStore(shopInfo);
    const isWscChainStore = checkWscChainStore(shopInfo);
    const isInNewSamWhitelistPromise =
      isRetailShopV3 || isEduChainShop || isWscChainStore
        ? new GrayService(ctx).isInGrayRelease({
            namespace: 'np_yz_shop',
            thirdUserId: `${userId}`,
            thirdTenantId: `${kdtId}`,
          })
        : Promise.resolve(false);

    const isDataAuthServicePromise = new GrayService(ctx).getWhitelist(ctx);
    const rawStaffInfoPromise = new StaffServiceV2(ctx).getStaff({
      kdtId,
      adminId: userId,
    });

    const isCurrentKdtIdCreatorPromise = this.getIsCurrentKdtIdCreator(ctx);

    const salesUpgradeStatusPromise = this.getSalesUpgradeStatus(ctx, {
      retailSource,
      adminId: userId,
      kdtId,
      hqKdtId,
    });

    const text = getChainStaffText(shopInfo);

    this.initSupportMultiChannelData(ctx);
    this.initOrgInfo(ctx);

    const [
      rawStaffInfo,
      isCurrentKdtIdCreator,
      isInNewSamWhitelist,
      salesUpgradeStatus,
      currentShopKeeperId,
      hqShopKeeperId,
      isDataAuthService,
      inStaffBatchCreateWhiteList,
      isInRegionalGrayList,
    ] = await Promise.all([
      rawStaffInfoPromise,
      isCurrentKdtIdCreatorPromise,
      isInNewSamWhitelistPromise,
      salesUpgradeStatusPromise,
      currentShopKeeperIdPromise,
      hqShopKeeperIdPromise,
      isDataAuthServicePromise,
      this.grayRelease('staff_batch_create', hqKdtId),
      this.grayRelease('org_mode_upgrade', kdtId),
    ]);

    let staffInfo = {};
    /**
     * 员工详细信息获取接口迁移
     */
    let isSuperAdmin = false;
    try {
      staffInfo = await new StaffQueryOuterService(ctx).getStaffDetailInfo({
        adminId: ctx.userId,
        kdtId,
      });
      const roleList = get(staffInfo, 'staffShopInfoDTOS[0].staffOrgDepRoleDTOS', []);
      isSuperAdmin = roleList.some(role => role.roleId === SUPER_ADMIN);
    } catch (err) {
      ctx.logger.warn(`员工详细信息获取失败：${err.message}`, err);
    }

    ctx.setGlobal('isSuperAdmin', isSuperAdmin);
    ctx.setGlobal('hqShopKeeperId', hqShopKeeperId);
    ctx.setGlobal('currentShopKeeperId', currentShopKeeperId);
    ctx.setGlobal('isInNewSamWhitelist', isInNewSamWhitelist);
    ctx.setGlobal('inStaffBatchCreateWhiteList', inStaffBatchCreateWhiteList);
    ctx.setGlobal('isInRegionalGrayList', isInRegionalGrayList);

    if (isInNewSamWhitelist) {
      await this.initPermissionFilter(ctx);
    }
    ctx.setGlobal('isDataAuthService', isDataAuthService);
    ctx.setGlobal('staffName', rawStaffInfo.name || userInfo.nickName);
    ctx.setGlobal('pageText', text);
    ctx.setGlobal('isCurrentKdtIdCreator', isCurrentKdtIdCreator);
    ctx.setGlobal('salesUpgradeStatus', salesUpgradeStatus);

    const templateLinks = ctx.apolloClient.getConfig({
      appId: 'wsc-pc-v4',
      namespace: 'wsc-pc-v4.template_links',
    });
    ctx.setGlobal('templateLinks', templateLinks);

    ctx.setGlobal('navtype', 'chainstaff');

    const partnerShopNodes = await new ShopChainPageQueryOuterService(ctx).queryDescendentShopNodes(
      {
        pageSize: 10,
        pageNum: 1,
        shopRoles: [4],
        kdtId: ctx.kdtId,
      }
    );
    ctx.setGlobal('partnerShopNodes', partnerShopNodes.data);

    await ctx.render('setting/chain_staff.html');
  }
}

module.exports = ChainStaffController;
