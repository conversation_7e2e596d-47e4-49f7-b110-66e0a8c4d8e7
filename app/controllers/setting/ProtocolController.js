const { get } = require('lodash');
const BaseController = require('../base/BaseController');
const ProtocolsRemoteService = require('../../services/api/yop/ProtocolsRemoteService');
const StaffQueryOuterService = require('../../services/api/staff/StaffQueryOuterService');

class ProtocolController extends BaseController {
  constructor(ctx) {
    super(ctx);
    this.ctx.biz = '设置-服务协议';
  }

  async getIndexHtml(ctx) {
    const { status = 'all' } = ctx.params;
    await this.ctx.redirect(`/v4/subscribe/protocol/list/${status}`);
  }

  async listProtocols(ctx) {
    const { kdtId } = ctx;
    const data = await new ProtocolsRemoteService(ctx).listProtocols({
      ...ctx.query,
      kdtId,
    });

    return ctx.json(0, 'ok', data);
  }

  async getProtocolDetail(ctx) {
    const { kdtId } = ctx;
    const { orderNo, protocolId } = ctx.query;
    const data = await new ProtocolsRemoteService(ctx).getProtocolDetail(
      +kdtId,
      orderNo,
      +protocolId
    );
    return ctx.json(0, 'ok', data);
  }

  async getPrintHtml(ctx) {
    const { kdtId } = ctx;
    const { id } = ctx.query;
    this.validator.isNumeric(id, '协议id不合法');
    const data = await new ProtocolsRemoteService(ctx).getContext(kdtId, id);
    const { protocolName, detailContext } = data || {};
    try {
      // 记录协议打印记录，类似于埋点，不应该影响页面加载
      const { id: adminId } = ctx.getLocalSession('userInfo');
      new ProtocolsRemoteService(ctx).recordProtocolDownload({
        protocolName,
        kdtId,
        adminId,
        protocolId: id,
      });
    } catch (error) { } // eslint-disable-line

    await ctx.render('setting/protocol/print.html', {
      detailContext,
    });
  }

  // 判断是否需要高级管理员登录后台阅读并签署协议(组件用)
  async needAdminSign(ctx) {
    const { kdtId } = ctx;
    const { appId } = ctx.query;
    this.validator.isNumeric(appId, '应用id不合法');
    let staffInfo = {};

    /**
     * 判断是否是高级管理员,员工详细信息获取接口迁移@马仔
     */
    let isSuperAdmin = false;
    try {
      staffInfo = await new StaffQueryOuterService(ctx).getStaffDetailInfo({
        adminId: ctx.userId,
        kdtId,
      });
      const roleList = get(staffInfo, 'staffShopInfoDTOS[0].staffOrgDepRoleDTOS', []);
      isSuperAdmin = roleList.some(role => role.roleId === 1);
    } catch (err) {
      ctx.logger.warn(`员工详细信息获取失败：${err.message}`, err);
    }

    const data = isSuperAdmin
      ? await new ProtocolsRemoteService(ctx).needAdminSign(kdtId, appId)
      : {
          needAdminSign: false,
          protocolName: '',
          protocolContext: '',
        };
    return ctx.json(0, 'ok', data);
  }

  // 签署协议
  async updateAdminSignByKdtId(ctx) {
    const { kdtId } = ctx;
    const { appId } = ctx.request.body;
    this.validator.isNumeric(appId, '应用id不合法');
    const data = await new ProtocolsRemoteService(ctx).updateAdminSignByKdtId(kdtId, appId);
    return ctx.json(0, 'ok', data);
  }
}

module.exports = ProtocolController;
