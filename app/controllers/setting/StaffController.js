const BaseController = require('./BaseStaffController');
const ShopTypeModifyService = require('../../services/api/staff/ShopTypeModifyService');
const StaffQueryOuterService = require('../../services/api/staff/StaffQueryOuterService');
const ChainRoleService = require('../../services/api/staff/ChainRoleService');
const SingleStaffService = require('../../services/api/staff/SingleStaffService');
const StaffServiceV2 = require('../../services/api/staff/StaffServiceV2');
const StaffCommonService = require('../../services/api/staff/StaffCommonService');
const GrayService = require('../../services/api/rig/GrayService');
const SalesmanWhiteOrGrayApiService = require('../../services/api/salesman/SalesmanWhiteOrGrayApiService');
const SalesmanUpgradeFrontService = require('../../services/api/salesman/SalesmanUpgradeFrontService');
const AsyncTaskService = require('../../services/api/sam/AsyncTaskService');
const StaffBatchOperateController = require('./StaffBatchOperateController');
const {
  checkEduShop,
  checkRetailShop,
  checkWscShop,
  checkRetailSingleStore,
} = require('@youzan/utils-shop');
const { pickBy, identity, get } = require('lodash');
const {
  SALES_UPGRADE_STATUS_MAP,
  retailSource,
  STAFF_ACTION_MAP,
  IDENTITY_BIZ_TYPE,
  RETAIL_ROLE,
  WSC_SAM_ROLE,
  SHOP_MOVE_KEY,
  ABILITY_STATUS,
} = require('../../constants');

const SUPER_ADMIN = 1;

// biz枚举 http://doc.qima-inc.com/pages/viewpage.action?pageId=12612833
const WSC = 'wsc';
const CASHIER = 'cashier'; // 收银
const RETAIL = 'retail'; // 超级门店
const BEAUTY = 'beauty'; // 美业
const CANYAN = 'cy'; // 餐饮
const RETAILHQ = 'retailhq'; // 零售集团版
const SUPPLIER = 'supplier'; // 供货

const ShopTypeBizMap = {
  0: WSC,
  5: CANYAN,
  6: BEAUTY,
  7: RETAIL,
  8: CASHIER,
  9: WSC,
  10: RETAILHQ,
  14: SUPPLIER,
};

class StaffController extends BaseController {
  constructor(ctx) {
    super(ctx);
    this.ctx.biz = '设置-员工管理';
  }

  async init() {
    await super.init();
    if (!this.ctx.acceptJSON) {
      await this.ctx.getAbilitiesByMultiNamespace(this.ctx, [
        // 角色管理
        {
          namespaceId: 'role',
          businessId: 'wsc-pc-v4',
        },
        // 员工管理
        {
          namespaceId: 'staff',
          businessId: 'wsc-pc-v4',
        },
      ]);
    }
  }

  /**
   * 员工管理页面渲染
   * @param {*} ctx
   * @memberof StaffController
   */
  async getIndexHtml(ctx) {
    const isInShopStaffMove = await this.grayRelease(SHOP_MOVE_KEY.SHOP_STAFF, ctx.kdtId);
    if (isInShopStaffMove) {
      return ctx.redirect(`https://www.youzan.com/v4/shop/setting/staff`);
    }
    const shopInfo = ctx.getState('shopInfo');
    if (+shopInfo.shopRole !== 0) {
      // 微商城教育连锁的跳到chainstaff
      if (+shopInfo.shopType === 0)
        return ctx.redirect(`https://www.youzan.com/v4/setting/chainstaff`);
      // 零售连锁连锁跳首页
      if (+shopInfo.shopType === 7)
        return ctx.redirect(`https://store.youzan.com/v2/dashboard/index`);
    }

    // 页面访问白名单控制
    await this.getHtml(ctx);
  }

  /**
   * 角色管理页面渲染
   * @param {*} ctx
   * @memberof StaffController
   */
  async getRoleHtml(ctx) {
    const kdtId = +ctx.kdtId;
    // 页面访问白名单控制
    const useNewPage = await this.callService(
      'wsc-pc-base/common.GrayReleaseService',
      'isInGrayReleaseByKdtId',
      'wsc_v4_staff',
      kdtId
    );

    if (!useNewPage) {
      ctx.redirect(`https:${ctx.getState('url').www}/staff/index/role`);
    }

    const isInShopRoleMove = await this.grayRelease(SHOP_MOVE_KEY.SHOP_ROLE, ctx.kdtId);

    if (isInShopRoleMove) {
      return ctx.redirect('https://www.youzan.com/v4/shop/setting/role');
    }

    await this.getHtml(ctx);
  }

  /**
   * 页面渲染
   * @param {*} ctx
   * @memberof StaffController
   */
  async getHtml(ctx) {
    const kdtId = +ctx.kdtId;
    const shopInfo = ctx.getState('shopInfo');

    let isInNewSamWhitelist = false;
    if (checkRetailSingleStore(shopInfo)) {
      isInNewSamWhitelist = true;
    } else {
      isInNewSamWhitelist =
        checkEduShop(shopInfo) || checkWscShop(shopInfo)
          ? await new GrayService(ctx).isInGrayRelease({
              namespace: 'np_yz_shop',
              thirdUserId: `${ctx.userId}`,
              thirdTenantId: `${kdtId}`,
            })
          : false;
    }
    ctx.setGlobal('isInNewSamWhitelist', isInNewSamWhitelist);
    if (isInNewSamWhitelist) {
      await this.initPermissionFilter(ctx);
    }
    let staffInfo = {};

    /**
     * 员工详细信息获取接口迁移@马仔
     */
    let isSuperAdmin = false;
    try {
      staffInfo = await new StaffQueryOuterService(ctx).getStaffDetailInfo({
        adminId: ctx.userId,
        kdtId,
      });
      const roleList = get(staffInfo, 'staffShopInfoDTOS[0].staffOrgDepRoleDTOS', []);
      isSuperAdmin = roleList.some(role => role.roleId === SUPER_ADMIN);
    } catch (err) {
      ctx.logger.warn(`员工详细信息获取失败：${err.message}`, err);
    }

    ctx.setGlobal('isSuperAdmin', isSuperAdmin);

    let isUpgradedFromWsc = false;
    if (ctx.isSuperStore) {
      const ret = await new ShopTypeModifyService(ctx).queryLatestModifiedTime4Retail(kdtId);
      isUpgradedFromWsc = !!ret;
    }

    try {
      const currentStaffData = await new SingleStaffService(ctx).get({
        adminId: ctx.userId,
        kdtId,
      });
      ctx.setGlobal('isCreator', currentStaffData.identity === 'CREATOR');
    } catch (e) {
      ctx.setGlobal('isCreator', false);
    }

    ctx.setGlobal('isUpgradedFromWsc', isUpgradedFromWsc);

    ctx.setGlobal('customRolePermit', !ctx.isSuperStore);

    const isInSalesWhiteListPromise = this.callService(
      'wsc-pc-base/common.GrayReleaseService',
      'isInGrayReleaseByKdtId',
      'sales_entry',
      kdtId
    );

    const salesUpgradeStatusPromise = this.getSalesUpgradeStatus(ctx, {
      retailSource,
      adminId: ctx.userId,
      kdtId,
    });

    const [isInSalesWhiteList, salesUpgradeStatus] = await Promise.all([
      isInSalesWhiteListPromise,
      salesUpgradeStatusPromise,
    ]);
    ctx.setGlobal('isInSalesWhiteList', isInSalesWhiteList);
    ctx.setGlobal('salesUpgradeStatus', salesUpgradeStatus);

    const templateLinks = ctx.apolloClient.getConfig({
      appId: 'wsc-pc-v4',
      namespace: 'wsc-pc-v4.template_links',
    });
    ctx.setGlobal('templateLinks', templateLinks);

    const inStaffBatchCreateWhiteList = await this.grayRelease('staff_batch_create', kdtId);
    // 微商城购买过导购助手插件 则支持复合角色
    const guideShopAbility = await this.getGuideShopAbility();
    const wscSupportRoles = guideShopAbility.abilityStatus !== ABILITY_STATUS.NO_PURCHASE;
    ctx.setGlobal('inStaffBatchCreateWhiteList', inStaffBatchCreateWhiteList);
    ctx.setGlobal('wscSupportRoles', wscSupportRoles);

    this.initTeamStatus();
    await this.initShopProd();

    await ctx.render('setting/staff.html');
  }

  async getSalesUpgradeStatus(ctx, params) {
    const { adminId, retailSource, kdtId } = params;
    const shopInfo = ctx.getState('shopInfo');

    if (!checkRetailShop(shopInfo)) {
      return SALES_UPGRADE_STATUS_MAP.NONE;
    }

    const rst = await new SalesmanWhiteOrGrayApiService(ctx).inWhiteList(
      'chain_salesman_merge',
      kdtId
    );

    if (rst) {
      return SALES_UPGRADE_STATUS_MAP.DONE;
    }

    const res = await new SalesmanUpgradeFrontService(ctx).getShopUpgradeStatus({
      adminId,
      kdtId,
      retailSource,
    });
    return res.inUpgrade || res.isAccountSetting
      ? SALES_UPGRADE_STATUS_MAP.INPROCESS
      : SALES_UPGRADE_STATUS_MAP.NONE;
  }

  getBiz(ctx) {
    const kdtId = +ctx.kdtId;
    const shopInfo = ctx.getState('shopInfo');
    return this.getBizByShopType(kdtId <= 0 ? -1 : shopInfo.shopType);
  }

  getBizByShopType(shopType) {
    return ShopTypeBizMap[shopType] || WSC;
  }

  async getRoleList(ctx) {
    const query = ctx.validator({
      page: ctx.joi.number().integer(),
      withStaffCount: ctx.joi.boolean(),
      pageSize: ctx.joi.number().integer(),
    });
    const param = {
      ...query,
      kdtId: +ctx.kdtId,
    };
    const result = await new ChainRoleService(ctx).getRoleList(param);
    ctx.success(result);
  }

  async findStaff(ctx) {
    const { page, roleId, needExtra, ...rest } = ctx.validator(
      {
        keyword: ctx.joi.string(),
        status: ctx.joi.string(),
        roleId: ctx.joi.number().integer(),
        needExtra: ctx.joi.string(),
        // eslint-disable-next-line camelcase
        csrf_token: ctx.joi.strip(),
      },
      {
        pagination: true,
      }
    );
    const param = {
      pageNo: page,
      ...rest,
      kdtId: +ctx.kdtId,
    };
    if (needExtra) {
      param.needExtra = JSON.parse(needExtra);
    }
    if (roleId) {
      param.roleIds = [roleId];
    }
    const result = await new SingleStaffService(ctx).find(param);
    ctx.success(result);
  }

  async getFreeRoles(ctx) {
    const res = await new SingleStaffService(ctx).getFreeRoles({
      kdtId: +ctx.kdtId,
    });
    ctx.success(res);
  }

  async isShopDisabledAllStaffsForExpire(ctx) {
    const res = await new StaffServiceV2(ctx).isShopDisabledAllStaffsForExpire({
      kdtId: +ctx.kdtId,
    });
    ctx.success(res);
  }

  async clearShopDisabledAllStaffsForExpireRecord(ctx) {
    const res = await new StaffServiceV2(ctx).clearShopDisabledAllStaffsForExpireRecord({
      kdtId: +ctx.kdtId,
    });
    ctx.success(res);
  }

  getSettingStaffParams(ctx) {
    const { adminIds } = ctx.getPostData();
    const param = {
      kdtId: +ctx.kdtId,
      adminIds: adminIds.map(item => +item),
      operatorId: ctx.userId,
    };
    return { ...pickBy(param, identity), isValidCaptcha: false };
  }

  async enable(ctx) {
    const {
      request: {
        body: { token },
      },
    } = ctx;
    const { adminIds } = ctx.validator({
      adminIds: ctx.joi.array().items(ctx.joi.number()),
    });
    const needTokenCheck = await new StaffBatchOperateController(ctx).checkNeedTokenCheckForBatch(
      STAFF_ACTION_MAP.ENABLE,
      adminIds
    );
    if (needTokenCheck) {
      this.validator.required(token, '核实身份 token 不能为空');
      const result = await this.tokenCheck(token, IDENTITY_BIZ_TYPE.ENABLE_OR_DISABLE);
      if (!result) return ctx.fail(-1, '身份验证失败');
    }

    const param = this.getSettingStaffParams(ctx);
    const res = await new SingleStaffService(ctx).enable(param);
    ctx.success(res);
  }

  async disable(ctx) {
    const {
      request: {
        body: { token },
      },
    } = ctx;
    const { adminIds } = ctx.validator({
      adminIds: ctx.joi.array().items(ctx.joi.number()),
    });
    const needTokenCheck = await new StaffBatchOperateController(ctx).checkNeedTokenCheckForBatch(
      STAFF_ACTION_MAP.DISABLE,
      adminIds
    );
    if (needTokenCheck) {
      this.validator.required(token, '核实身份 token 不能为空');
      const result = await this.tokenCheck(token, IDENTITY_BIZ_TYPE.ENABLE_OR_DISABLE);
      if (!result) return ctx.fail(-1, '身份验证失败');
    }
    const param = this.getSettingStaffParams(ctx);
    const res = await new SingleStaffService(ctx).disable(param);
    ctx.success(res);
  }

  // 删除员工
  async delete(ctx) {
    const {
      request: {
        body: { token },
      },
    } = ctx;

    const needTokenCheck = await this.checkNeedTokenCheck(STAFF_ACTION_MAP.DELETE);
    if (needTokenCheck) {
      this.validator.required(token, '核实身份 token 不能为空');
      const result = await this.tokenCheck(token, IDENTITY_BIZ_TYPE.DELETE);
      if (!result) return ctx.fail(-1, '身份验证失败');
    }

    const query = ctx.validator({
      adminId: ctx.joi.number().integer(),
      account: ctx.joi.string(),
      // eslint-disable-next-line camelcase
      csrf_token: ctx.joi.strip(),
    });
    const param = {
      ...query,
      kdtId: +ctx.kdtId,
      operatorId: ctx.userId,
      isValidCaptcha: false,
    };
    const res = await new SingleStaffService(ctx).delete(param);
    ctx.success(res);
  }

  async findStaffDeleteHooks(ctx) {
    const { adminId } = ctx.validator({
      adminId: ctx.joi.number().integer(),
    });
    const param = {
      kdtId: +ctx.kdtId,
      adminId,
    };
    const res = await new SingleStaffService(ctx).findStaffDeleteHooks(param);
    ctx.success(res);
  }

  // 变更店铺负责人
  async changeShopKeeper(ctx) {
    const {
      request: {
        body: { smsCaptcha, ...paramsDTO },
      },
      kdtId,
      userId: operatorId,
    } = ctx;
    const CAPTCHA_BIZ = 'kdt_add_admin';
    const result = await this.smsCaptchaCheck(ctx, kdtId, smsCaptcha, CAPTCHA_BIZ);
    if (!result) return;

    const params = {
      ...paramsDTO,
      operatorId,
      isValidCaptcha: false,
      kdtId,
    };
    const res = await new SingleStaffService(ctx).changeShopKeeper(params);
    ctx.success(res);
  }

  async getRolePerm(ctx) {
    const query = ctx.validator({
      roleId: ctx.joi.number().integer(),
      roleType: ctx.joi.number().integer(),
      shopRole: ctx.joi.number().integer(),
    });
    const param = {
      ...query,
      kdtId: +ctx.kdtId,
    };
    const res = await new ChainRoleService(ctx).getRolePerm(param);
    ctx.success(res);
  }

  async getStaffAbility(ctx) {
    const param = {
      kdtId: +ctx.kdtId,
    };
    const res = await new StaffCommonService(ctx).getStaffAbility(param);
    ctx.success(res);
  }

  async get(ctx) {
    const { needExtra, ...rest } = ctx.validator({
      adminId: ctx.joi.number().integer(),
      account: ctx.joi.string(),
      staffExtType: ctx.joi.string(),
      needExtra: ctx.joi.string(),
    });
    const param = {
      ...rest,
      kdtId: +ctx.kdtId,
    };
    if (needExtra) {
      param.needExtra = JSON.parse(needExtra);
    }
    const res = await new SingleStaffService(ctx).get(param);
    ctx.success(res);
  }

  getParam(ctx) {
    const query = ctx.validator({
      name: ctx.joi.string().required(),
      account: ctx.joi.string().required(),
      staffNo: ctx.joi.string(),
      linkPhone: ctx.joi.string(),
      roles: ctx.joi.array().required(),
      extraInfo: ctx.joi.object(),
      staffExtType: ctx.joi.string(),
      teacherInfo: ctx.joi.object(),
      smsPhone: ctx.joi.string(),
      smsCaptcha: ctx.joi.string(),
    });
    const param = {
      ...query,
      kdtId: +ctx.kdtId,
      operatorId: ctx.userId,
      isValidCaptcha: false,
    };
    return param;
  }

  // 添加员工
  async create(ctx) {
    const {
      request: {
        body: { token },
      },
    } = ctx;
    const needTokenCheck = await this.checkNeedTokenCheck(STAFF_ACTION_MAP.CREATE);
    if (needTokenCheck) {
      this.validator.required(token, '核实身份 token 不能为空');
      const result = await this.tokenCheck(token, IDENTITY_BIZ_TYPE.CREATE_OR_UPDATE);
      if (!result) return ctx.fail(-1, '身份验证失败');
    }

    const res = await new SingleStaffService(ctx).create(this.getParam(ctx));
    ctx.success(res);
  }

  // 编辑员工
  async update(ctx) {
    const {
      request: {
        body: { token },
      },
    } = ctx;
    const needTokenCheck = await this.checkNeedTokenCheck(STAFF_ACTION_MAP.UPDATE);
    if (needTokenCheck) {
      this.validator.required('token', '核实身份 token 不能为空');
      const result = await this.tokenCheck(token, IDENTITY_BIZ_TYPE.CREATE_OR_UPDATE);
      if (!result) return ctx.fail(-1, '身份验证失败');
    }

    const res = await new SingleStaffService(ctx).update(this.getParam(ctx));
    ctx.success(res);
  }

  /**
   * 判断是否需要扫码核身，前端保持一致
   * 1、当前登录的是创始人，不需要核验
   * 2、新设置高管或财务的 需要核验
   * 3、原来是高管或财务的 需要核验
   * @param type 操作类型：create、update、delete
   * @returns {Promise<boolean>}
   */
  async checkNeedTokenCheck(type) {
    const { ctx } = this;
    const {
      kdtId,
      request: {
        body: { token },
      },
    } = ctx;

    let oldRoles = [];
    let newRoles = [];
    let params = {};

    // 创建、保存 有新的角色添加进来
    if ([STAFF_ACTION_MAP.CREATE, STAFF_ACTION_MAP.UPDATE].indexOf(type) !== -1) {
      params = this.getParam(ctx);
      newRoles = params.roles;
    }

    let adminId = '';
    if (type === STAFF_ACTION_MAP.UPDATE) {
      adminId = params.adminId;
    } else if (type === STAFF_ACTION_MAP.DELETE) {
      adminId = ctx.request.body.adminId;
    }

    // 保存、删除 需要获取原有的角色
    if ([STAFF_ACTION_MAP.UPDATE, STAFF_ACTION_MAP.DELETE].indexOf(type) !== -1) {
      const { roles = [] } = await new SingleStaffService(ctx).get({
        adminId,
        kdtId,
      });
      oldRoles = roles;
    }

    const isRetail = ctx.shopType === 7;
    // 新老角色合并，统一判断是否有 高管或财务，有就需要授权。
    const hasImportantRole = [...oldRoles, ...newRoles].some(
      role =>
        [
          WSC_SAM_ROLE.SUPER_ADMIN,
          isRetail ? RETAIL_ROLE.FINANCE_ADMIN : WSC_SAM_ROLE.FINANCE_ADMIN,
        ].indexOf(Number(role.roleId)) !== -1
    );

    // 判断当前登录的是否是创始人
    const { identity } = await new SingleStaffService(ctx).get({
      adminId: ctx.userId,
      kdtId,
    });

    // 创始人，除了删除、编辑，其它都不需要核验。编辑需要校验的原因是防止以下漏洞：
    // 比如正常情况下删除高级管理员会先验证店铺创建者的身份，但先将其编辑为管理员或者其他身份，然后就可以删除
    let shouldCheck = hasImportantRole;
    if (hasImportantRole && identity === 'CREATOR') {
      shouldCheck = [STAFF_ACTION_MAP.DELETE, STAFF_ACTION_MAP.UPDATE].indexOf(type) !== -1;
    }
    if (shouldCheck && !token) {
      ctx.logger.error('扫码核身token为空', null, { type, ...ctx.request.body });
    }
    return shouldCheck;
  }

  // 异步任务结果查询
  async queryTaskStatusById(ctx) {
    const {
      request: {
        body: { taskId },
      },
      kdtId,
      userId: adminId,
    } = ctx;
    const params = {
      taskId,
      kdtId,
      adminId,
    };
    const res = await new AsyncTaskService(ctx).queryTaskStatusById(params);
    ctx.success(res);
  }

  // 新增、更新自定义角色
  async addCustomizeRole(ctx) {
    const query = ctx.validator({
      adminId: ctx.joi.number().integer(),
      menuItemPermIds: ctx.joi.array(),
      name: ctx.joi.string(),
      operator: ctx.joi.string(),
      onlineShopOpen: ctx.joi.boolean(),
      remark: ctx.joi.string(),
      roleType: ctx.joi.number().integer(),
      roleId: ctx.joi.number().integer(),
      shopRole: ctx.joi.number().integer(),
    });
    const param = {
      ...query,
      kdtId: +ctx.kdtId,
      operatorId: ctx.userId,
    };
    const res = await new ChainRoleService(ctx).addCustomizeRole(param);
    ctx.success(res);
  }

  // 删除自定义角色
  async deleteCustomRole(ctx) {
    const query = ctx.validator({
      roleId: ctx.joi.number().integer(),
    });
    const param = {
      ...query,
      kdtId: +ctx.kdtId,
      operatorId: ctx.userId,
    };
    const res = await new ChainRoleService(ctx).deleteCustomRole(param);
    ctx.success(res);
  }
}

module.exports = StaffController;
