const BaseController = require('../base/BaseController');
const AuthService = require('../../services/api/authority/AuthService');
const AppstoreAuthService = require('../../services/api/cloud/AppstoreAuthService');
const ChainShopService = require('../../services/api/authority/ChainShopService');
const ShopDeviceConfigService = require('../../services/api/authority/ShopDeviceConfigService');
const DeviceApiService = require('../../services/api/cloud/DeviceApiService');
const CommonApiService = require('../../services/api/cloud/CommonApiService');
const RuleCheckService = require('../../services/api/cloud/RuleCheckService');
const ShopAuthQueryProxyService = require('../../services/api/authority/ShopAuthQueryProxyService');
const AuthProxyService = require('../../services/api/authority/AuthProxyService');
const BillService = require('../../services/api/cloud/BillService');

class DiyController extends BaseController {
  constructor(ctx) {
    super(ctx);
    this.ctx.biz = '设置-定制服务';
  }

  /**
   * @param {AstroboyContext} ctx
   */
  async getIndexHtml(ctx) {
    const iotEnum = await new CommonApiService(ctx).enums(['platform']);
    ctx.setGlobal('iotEnum', iotEnum);
    await ctx.render('setting/diy.html');
  }

  async checkConflict(ctx) {
    const { kdtId } = ctx;
    const data = ctx.getRequestData();
    // 冲突检测
    const conflictTest = await new RuleCheckService(ctx).checkRuleConflict({
      ...data,
      kdtId,
      // 保持一致, 见 https://gitlab.qima-inc.com/fe-ecloud/app-web/commit/4749ad8657fcddcacb622b1bd4da980f30e4669d
      env: 'prod',
    });

    ctx.json(0, 'ok', conflictTest);
  }

  /**
   * 批量开启或者是关闭应用授权状 态
   */
  async batchTurn(ctx) {
    const { kdtId } = ctx;
    const params = ctx.getRequestData();
    const { closeApps = [] } = params;

    const kdtIdStr = String(kdtId);
    const userId = String(ctx.getLocalSession('userInfo').userId);

    const result = closeApps.map(item => {
      return {
        appId: item.appId,
        grantId: kdtIdStr,
        identityId: userId,
        isValid: 0,
        grantType: 0,
        identitySource: 0,
        // 为了保持和其他接口的一致 https://gitlab.qima-inc.com/fe-ecloud/wsc-pc-apps/commit/a469c3d92d2ede92d847f4fb1b84e47725ab92b4#3e8208883819b0e0028f362967b7c9f6bf726614_68_98
        env: process.env.NODE_ENV === 'qa' ? 0 : 1,
      };
    });

    const data = await new AppstoreAuthService(ctx).batchAuthSwitch(result);
    ctx.json(0, 'ok', data);
  }

  /**
   * 获取授权应用列表
   * @param {*} ctx
   * @returns
   * @memberof OpenApplicationController
   */
  async getAuthorizationList(ctx) {
    const data = ctx.getRequestData();
    const { userId } = ctx;
    const result = await new ShopAuthQueryProxyService(ctx).queryAuthorizationList({
      ...data,
      userId,
    });
    return ctx.json(0, 'ok', result);
  }

  /**
   * 获取授权设备列表
   * @param {*} ctx
   * @returns
   * @memberof OpenApplicationController
   */
  async getDeviceAuthorizationList(ctx) {
    const param = ctx.getRequestData();
    const { userId } = ctx;
    const data = await new DeviceApiService(ctx).shopPageQuery({ ...param, userId });
    return ctx.json(0, 'ok', data);
  }

  /**
   * 授权有容器应用
   * @param {*} ctx
   * @returns
   * @memberof OpenApplicationController
   */
  async authorizeCloud(ctx) {
    const param = ctx.getRequestData();
    const data = await new AuthService(ctx).authorize({
      ...param,
      identitySource: 0,
      identityId: String(ctx.getLocalSession('userInfo').userId),
    });
    return ctx.json(0, 'ok', data);
  }

  /**
   * 取消授权有容器应用
   * @param {*} ctx
   * @returns
   * @memberof OpenApplicationController
   */
  async unAuthorizeCloud(ctx) {
    const param = ctx.getRequestData();
    const { userId } = ctx;
    const data = await new AuthProxyService(ctx).unAuthorize({
      ...param,
      identitySource: 0,
      identityId: String(ctx.getLocalSession('userInfo').userId),
      userId,
    });
    return ctx.json(0, 'ok', data);
  }

  /**
   * 授权无容器应用
   * @param {*} ctx
   * @returns
   * @memberof OpenApplicationController
   */
  async authorize(ctx) {
    const param = ctx.getRequestData();
    const data = await new AuthService(ctx).authorize({
      ...param,
      identitySource: 0,
      identityId: String(ctx.getLocalSession('userInfo').userId),
    });
    return ctx.json(0, 'ok', data);
  }

  /**
   * 取消授权无容器应用
   * @param {*} ctx
   * @returns
   * @memberof OpenApplicationController
   */
  async unAuthorize(ctx) {
    const param = ctx.getRequestData();
    const { userId } = ctx;
    const data = await new AuthProxyService(ctx).unAuthorize({
      ...param,
      identitySource: 0,
      identityId: String(ctx.getLocalSession('userInfo').userId),
      userId,
    });
    return ctx.json(0, 'ok', data);
  }

  async getSlientAuthStatus(ctx) {
    const param = ctx.query;
    const data = await new ChainShopService(ctx).getSlientAuthStatus([
      {
        rootKdtId: ctx.kdtId,
        operatorId: ctx.userId,
        appId: +param.appId,
        env: param.env,
      },
    ]);
    return ctx.json(0, 'ok', data);
  }

  async enableSlientAuth(ctx) {
    const param = ctx.getRequestData();
    const data = await new ChainShopService(ctx).enableSlientAuth([
      {
        rootKdtId: ctx.kdtId,
        operatorId: ctx.userId,
        appId: +param.appId,
        env: param.env,
      },
    ]);
    return ctx.json(0, 'ok', data);
  }

  async disableSlientAuth(ctx) {
    const param = ctx.getRequestData();
    const data = await new ChainShopService(ctx).disableSlientAuth([
      {
        rootKdtId: ctx.kdtId,
        operatorId: ctx.userId,
        appId: +param.appId,
        env: param.env,
      },
    ]);
    return ctx.json(0, 'ok', data);
  }

  async addDeviceAuth(ctx) {
    const param = ctx.getRequestData();
    param.bizLine = 0;
    param.operator = ctx.userId;
    param.kdtId = ctx.kdtId;
    if (!param.appId) {
      delete param.appId;
    }
    const data = await new DeviceApiService(ctx).shopRegister(param);
    return ctx.json(0, 'ok', data);
  }

  async removeDeviceAuth(ctx) {
    const param = ctx.getRequestData();
    const { kdtId, userId } = ctx;
    const data = await new DeviceApiService(ctx).shopDelete({ ...param, kdtId, userId });
    return ctx.json(0, 'ok', data);
  }

  async manageDeviceAuth(ctx) {
    const param = ctx.getRequestData();
    param.identitySource = (0).toString();
    param.identityId = ctx.userId.toString();
    param.kdtId = ctx.kdtId.toString();
    const data = await new ShopDeviceConfigService(ctx).manageDeviceAndApp(param);
    return ctx.json(0, 'ok', data);
  }

  async getDeviceDetailById(ctx) {
    const param = ctx.getRequestData();
    const data = await new DeviceApiService(ctx).info(param);
    return ctx.json(0, 'ok', data);
  }

  async getDeviceCreatorDynamicForm(ctx) {
    const param = ctx.getRequestData();
    param.bizLine = 0;
    const data = await new CommonApiService(ctx).platformDeviceRequiredParams(param);
    return ctx.json(0, 'ok', data);
  }

  async arrearsAppList(ctx) {
    const data = await new BillService(ctx).arrearsAppList({ kdtId: ctx.kdtId });
    return ctx.json(0, 'ok', data);
  }
}

module.exports = DiyController;
