const BaseController = require('../base/BaseController');
const TrustService = require('../../services/api/trust/IndexService');
const { createTotalCheck } = require('@youzan/utils-shop');

const getAppId = _shop => {
  return 873;
};

const getShopType = shop => {
  const check = createTotalCheck(shop);
  if (check.isRetailMinimalistShop) {
    return 1;
  }
  if (check.isWscShop) {
    return 1;
  }
  if (check.isRetailShop) {
    return 4;
  }
  if (check.isBeautyShop) {
    return 2;
  }
  return null;
};

const getShopTypeCode = shop => {
  const check = createTotalCheck(shop);
  if (check.isRetailMinimalistShop) {
    return 'weishangcheng';
  }
  if (check.isWscShop) {
    return 'weishangcheng';
  }
  if (check.isRetailShop) {
    return 'lingshou';
  }
  if (check.isBeautyShop) {
    return 'meiye';
  }
  return null;
};

class HuhangController extends BaseController {
  constructor(ctx) {
    super(ctx);
    this.ctx.biz = '设置-有赞护航';
  }

  async getIndexHtml(ctx) {
    const recentAction = await new TrustService(ctx).getRecentAction({
      bizTypes: getShopType(ctx.getState().shopInfo),
    });
    ctx.setGlobal({
      recentAction,
      shopTypeCode: getShopTypeCode(ctx.getState().shopInfo),
    });
    await ctx.render('setting/huhang.html');
  }

  async getHistoryEvents(ctx) {
    const { kdtId } = ctx;
    const { month } = ctx.query;
    const rlt = await new TrustService(ctx).getHistoryEvents({
      appId: getAppId(ctx.getState().shopInfo),
      kdtId,
      month,
      bizType: getShopType(ctx.getState().shopInfo),
    });

    return ctx.json(0, 'ok', rlt);
  }

  async getCompensateRecords(ctx) {
    const { kdtId } = ctx;
    const { year } = ctx.query;
    const rlt = await new TrustService(ctx).getCompensateRecords({
      appId: getAppId(ctx.getState().shopInfo),
      kdtId,
      year,
    });

    return ctx.json(0, 'ok', rlt);
  }
}

module.exports = HuhangController;
