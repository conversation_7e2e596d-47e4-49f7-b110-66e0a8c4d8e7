const BaseController = require('../base/BaseController');
const DataPermissionService = require('../../services/api/staff/DataPermissionService');

class DataPermissionController extends BaseController {
  constructor(ctx) {
    super(ctx);
  }

  async updateScopeOfDataPermission(ctx) {
    const { userId, kdtId } = ctx;
    const query = ctx.getRequestData();
    query.namespace = 'np_yz_shop';
    query.thirdTenantId = kdtId.toString();
    query.thirdUserId = userId.toString();

    const res = await new DataPermissionService(ctx).updateScopeOfDataPermission(query);

    ctx.json(0, 'ok', res);
  }

  // List<Long> roleIds
  // private String thirdUserId;  (用户userid)
  // private String thirdTenantId;  （店铺id）
  // private String bizCode; （写死retail_verify_record）
  // private String namespace  （还是传np_yz_shop）

  async getScopeOfDataPermissionV2(ctx) {
    const { kdtId, userId } = ctx;

    const query = ctx.getRequestData();
    query.namespace = 'np_yz_shop';
    query.thirdTenantId = kdtId.toString();
    query.thirdUserId = userId.toString();
    query.bizCode = 'retail_verify_record';
    query.roleIds = [Number(query.roleId)];
    delete query.roleId;
    const res = await new DataPermissionService(ctx).getScopeOfDataPermissionV2(query);

    ctx.json(0, 'ok', res);
  }
}

module.exports = DataPermissionController;
