const BaseConstroller = require('../base/BaseController');
const { ErrorCode } = require('@youzan/wsc-pc-base/app/constants/error');
const { PCBaseBusinessException } = require('@youzan/wsc-pc-base');
const ShopOperateLogService = require('../../services/api/shop-center/ShopOperateLogService');
const { checkRetailShop, checkEduShop, checkRetailMinimalistShop } = require('@youzan/utils-shop');

class NewOperateController extends BaseConstroller {
  constructor(ctx) {
    super(ctx);
    this.ctx.biz = '设置-新操作记录';
  }

  async init() {
    await super.init();
    if (!this.ctx.acceptJSON) {
      await this.ctx.getAbilitiesByNamespace(this.ctx, {
        namespaceId: 'eduAdmin',
        businessId: 'wsc-pc-vis',
      });
    }
  }

  async getIndexHtml(ctx) {
    ctx.redirect(`/v4/shop/setting/newoperate`);
  }

  async getModules(ctx) {
    const shopInfo = ctx.getState('shopInfo');
    let type = 'wsc'; // 默认wsc
    if (checkEduShop(shopInfo)) {
      type = 'edu.wsc';
    }
    if (checkRetailShop(shopInfo)) {
      type = 'retail';
    }
    if (checkRetailMinimalistShop(shopInfo)) {
      type = 'retail.chain.online';
    }
    const result = await new ShopOperateLogService(ctx).getBizModuleList(type);
    return result;
  }

  async queryLogListJSON(ctx) {
    const shopInfo = ctx.getState('shopInfo');
    const params = {
      ...ctx.query,
      kdtId: +ctx.query.useRootKdtId ? shopInfo.rootKdtId : shopInfo.kdtId,
      page: +ctx.query.page,
      pageSize: +ctx.query.pageSize,
    };
    if (params.page * params.pageSize > 3300) {
      throw new PCBaseBusinessException(ErrorCode.DEFAULT, '超过最大分页限制');
    }
    const result = await new ShopOperateLogService(ctx).queryLogList(params);
    ctx.json(0, 'ok', result);
  }
}

module.exports = NewOperateController;
