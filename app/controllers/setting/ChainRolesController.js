// const BaseController = require('./BaseStaffController');
const BaseChainStaffAndRoleController = require('./BaseChainStaffAndRoleController');
const HQStoreSearchService = require('../../services/api/shop/HQStoreSearchService');
const StaffServiceV2 = require('../../services/api/sam/StaffServiceV2');
const getChainStaffText = require('./text/chain-staff');
const GrayService = require('../../services/api/rig/GrayService');
const StaffQueryOuterService = require('../../services/api/staff/StaffQueryOuterService');
const ShopChainPageQueryOuterService = require('../../services/api/staff/ShopChainPageQueryOuterService');

const { retailSource } = require('../../constants');
const SUPER_ADMIN = 1;

const {
  checkUnifiedShop,
  checkEduChainStore,
  checkWscChainStore,
  checkRetailMinimalistShop,
} = require('@youzan/utils-shop');
const { get } = require('lodash');

class ChainRolesController extends BaseChainStaffAndRoleController {
  constructor(ctx) {
    super(ctx);
    this.ctx.biz = '设置-连锁员工管理';
  }

  /**
   * 连锁员工管理页面渲染
   * @param {*} ctx
   * @memberof ChainStaffController
   */
  async getIndexHtml(ctx) {
    const { kdtId, userId } = ctx;
    await this.initUserInfo();
    const shopInfo = ctx.getState('shopInfo');
    const userInfo = ctx.getState('userInfo');
    const hqKdtId = shopInfo.rootKdtId;
    const shopRoleList = [2, 4, 6, 7];
    const params = {
      kdtId,
      hqKdtId,
      shopRoleList,
      adminId: userId,
      retailSource,
    };

    const currentShopKeeperIdPromise = this.getKeeperAdminId(kdtId, userId);
    const hqShopKeeperIdPromise = this.getKeeperAdminId(hqKdtId, userId);

    const isRetailShopV3 = checkUnifiedShop(shopInfo) || checkRetailMinimalistShop(shopInfo);
    const isEduChainShop = checkEduChainStore(shopInfo);
    const isWscChainStore = checkWscChainStore(shopInfo);
    const isInNewSamWhitelistPromise =
      isRetailShopV3 || isEduChainShop || isWscChainStore
        ? new GrayService(ctx).isInGrayRelease({
            namespace: 'np_yz_shop',
            thirdUserId: `${userId}`,
            thirdTenantId: `${kdtId}`,
          })
        : Promise.resolve(false);

    const isDataAuthServicePromise = new GrayService(ctx).getWhitelist(ctx);
    const rawStaffInfoPromise = new StaffServiceV2(ctx).getStaff({
      kdtId,
      adminId: userId,
    });
    const hqStoreSearchService = new HQStoreSearchService(ctx);
    const shopListWithoutPermPromise = hqStoreSearchService.getAllStores(params, false);
    const shopListWithPermPromise = hqStoreSearchService.getAllStores(params, true);

    const isCurrentKdtIdCreatorPromise = this.getIsCurrentKdtIdCreator(ctx);

    const salesUpgradeStatusPromise = this.getSalesUpgradeStatus(ctx, {
      retailSource,
      adminId: userId,
      kdtId,
      hqKdtId,
    });

    const text = getChainStaffText(shopInfo);

    this.initSupportMultiChannelData(ctx);
    this.initOrgInfo(ctx);

    const [
      rawStaffInfo,
      shopListWithoutPerm,
      shopListWithPerm,
      isCurrentKdtIdCreator,
      isInNewSamWhitelist,
      salesUpgradeStatus,
      currentShopKeeperId,
      hqShopKeeperId,
      isDataAuthService,
      inStaffBatchCreateWhiteList,
    ] = await Promise.all([
      rawStaffInfoPromise,
      shopListWithoutPermPromise,
      shopListWithPermPromise,
      isCurrentKdtIdCreatorPromise,
      isInNewSamWhitelistPromise,
      salesUpgradeStatusPromise,
      currentShopKeeperIdPromise,
      hqShopKeeperIdPromise,
      isDataAuthServicePromise,
      this.grayRelease('staff_batch_create', hqKdtId),
    ]);

    let staffInfo = {};
    /**
     * 员工详细信息获取接口迁移
     */
    let isSuperAdmin = false;
    try {
      staffInfo = await new StaffQueryOuterService(ctx).getStaffDetailInfo({
        adminId: ctx.userId,
        kdtId,
      });
      const roleList = get(staffInfo, 'staffShopInfoDTOS[0].staffOrgDepRoleDTOS', []);
      isSuperAdmin = roleList.some(role => role.roleId === SUPER_ADMIN);
    } catch (err) {
      ctx.logger.warn(`员工详细信息获取失败：${err.message}`, err);
    }

    ctx.setGlobal('isSuperAdmin', isSuperAdmin);
    ctx.setGlobal('hqShopKeeperId', hqShopKeeperId);
    ctx.setGlobal('currentShopKeeperId', currentShopKeeperId);
    ctx.setGlobal('isInNewSamWhitelist', isInNewSamWhitelist);
    ctx.setGlobal('inStaffBatchCreateWhiteList', inStaffBatchCreateWhiteList);

    if (isInNewSamWhitelist) {
      await this.initPermissionFilter(ctx);
    }
    ctx.setGlobal('isDataAuthService', isDataAuthService);
    if (shopListWithoutPerm) {
      ctx.setGlobal('storeList', shopListWithoutPerm);
    }
    if (shopListWithPerm) {
      ctx.setGlobal('storeListPerm', shopListWithPerm);
    }
    ctx.setGlobal('staffName', rawStaffInfo.name || userInfo.nickName);
    ctx.setGlobal('pageText', text);
    ctx.setGlobal('isCurrentKdtIdCreator', isCurrentKdtIdCreator);
    ctx.setGlobal('salesUpgradeStatus', salesUpgradeStatus);

    const templateLinks = ctx.apolloClient.getConfig({
      appId: 'wsc-pc-v4',
      namespace: 'wsc-pc-v4.template_links',
    });
    ctx.setGlobal('templateLinks', templateLinks);

    ctx.setGlobal('navtype', 'chainroles');

    const partnerShopNodes = await new ShopChainPageQueryOuterService(ctx).queryDescendentShopNodes(
      {
        pageSize: 10,
        pageNum: 1,
        shopRoles: [4],
        kdtId: ctx.kdtId,
      }
    );
    ctx.setGlobal('partnerShopNodes', partnerShopNodes.data);

    await ctx.render('setting/chain_roles.html');
  }
}

module.exports = ChainRolesController;
