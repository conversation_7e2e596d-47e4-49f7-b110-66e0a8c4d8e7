const BaseController = require('../base/BaseController');
const YqbViolationRecordService = require('../../services/api/ctu-open/YqbViolationRecordService');

class YqbAppealController extends BaseController {
  // 查询申诉状态枚举
  async getAllAppealStatus(ctx) {
    const res = await new YqbViolationRecordService(ctx).queryAllAppealStatus();
    return ctx.json(0, 'OK', res);
  }

  // 查询风险状态枚举
  async getAllRiskType(ctx) {
    const res = await new YqbViolationRecordService(ctx).queryAllRiskType();
    return ctx.json(0, 'OK', res);
  }

  // 查询处置方式枚举
  async getAllDisposal(ctx) {
    const res = await new YqbViolationRecordService(ctx).queryAllDisposal();
    return ctx.json(0, 'OK', res);
  }

  // 查询平安付违规列表
  async getYqbViolationList(ctx) {
    const {
      pageNum,
      pageSize,
      orderBy,
      sortField,
      riskType,
      disposal,
      appealStatus,
      violationAtStart,
      violationAtEnd,
      yzMchNo,
    } = ctx.query;
    const { kdtId } = ctx;
    const params = {
      kdtId: Number(kdtId),
      pageNum: Number(pageNum),
      pageSize: Number(pageSize),
      orderBy,
      sortField,
      riskType,
      disposal,
      appealStatus: Number(appealStatus),
      violationAtStart,
      violationAtEnd,
      yzMchNo,
    };
    const res = await new YqbViolationRecordService(ctx).queryByCondition(params);
    return ctx.json(0, 'OK', res);
  }

  // 查询平安付违规详情
  async getYqbViolationDetail(ctx) {
    const { kdtId } = ctx;
    const res = await new YqbViolationRecordService(ctx).queryDetail(kdtId, ctx.query.id);
    return ctx.json(0, 'OK', res);
  }

  async yqbAppealCommit(ctx) {
    const { id, appealContent, yqbAttachmentInfoDTOList } = ctx.request.body;
    const params = {
      id: Number(id),
      appealContent,
      yqbAttachmentInfoDTOList,
    };
    const res = await new YqbViolationRecordService(ctx).appealCommit(params);
    return ctx.json(0, 'OK', res);
  }

  // 查询平安付历史申诉记录
  async getAppealLogs(ctx) {
    const { kdtId } = ctx;
    const { id } = ctx.query;
    const res = await new YqbViolationRecordService(ctx).queryLogs(kdtId, id);
    return ctx.json(0, 'OK', res);
  }

  // 查询申诉历史记录详情
  async getAppealLogDetail(ctx) {
    const { kdtId } = ctx;
    const { id } = ctx.query;
    const res = await new YqbViolationRecordService(ctx).queryLogDetail(kdtId, Number(id));
    return ctx.json(0, 'OK', res);
  }

  // 查询平安付巡检材料上报详情
  async getYqbViolationReportDetail(ctx) {
    const res = await new YqbViolationRecordService(ctx).queryInspectionDetail(
      ctx.kdtId,
      ctx.query.id
    );
    return ctx.json(0, 'OK', res);
  }

  // 提交平安付巡检材料
  async yqbReportCommit(ctx) {
    const { commitDetailDTO } = ctx.request.body;
    const res = await new YqbViolationRecordService(ctx).inspectionAppealCommit(commitDetailDTO);
    return ctx.json(0, 'OK', res);
  }

  // 查询巡检状态枚举
  async queryAllInspectionTypes(ctx) {
    const res = await new YqbViolationRecordService(ctx).queryAllInspectionTypes();
    return ctx.json(0, 'OK', res);
  }

  // 查询平安付巡检材料上报历史详情
  async getYqbViolationReportLogDetail(ctx) {
    const res = await new YqbViolationRecordService(ctx).queryInspectionLogDetail(
      ctx.kdtId,
      ctx.query.id
    );
    return ctx.json(0, 'OK', res);
  }

  // 查询平安付违规记录通知消息历史记录
  async queryYqbViolationMsgHistory(ctx) {
    const { kdtId } = ctx;
    const { id } = ctx.query;
    const res = await new YqbViolationRecordService(ctx).queryYqbViolationMsgHistory(kdtId, id);
    return ctx.json(0, 'OK', res);
  }
}

module.exports = YqbAppealController;
