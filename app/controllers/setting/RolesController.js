const BaseController = require('../base/BaseController');
const RoleService = require('../../services/api/rig/RoleService');
const PermService = require('../../services/api/rig/PermService');
const RetailRoleService = require('../../services/api/sam/RetailRoleService');

class RolesController extends BaseController {
  constructor(ctx) {
    super(ctx);
    this.ctx.biz = '设置-角色管理';
  }

  getCommonParams(ctx) {
    return {
      namespace: 'np_yz_shop',
      thirdTenantId: ctx.kdtId + '',
      thirdUserId: ctx.userId + '',
    };
  }

  // 创建
  async create(ctx) {
    const { bizCode, name, remark, roleType: roleTypeValue, permIds } = ctx.validator({
      bizCode: ctx.joi.string(),
      name: ctx.joi.string(),
      remark: ctx.joi.string(),
      roleType: ctx.joi.number().integer(),
      permIds: ctx.joi
        .array()
        .single()
        .items(ctx.joi.number()),
    });

    const params = {
      ...this.getCommonParams(ctx),
      bizCode,
      name,
      remark,
      roleTypeValue,
      permIds,
    };
    const res = await new RoleService(ctx).add(params);
    ctx.success(res);
  }

  // 编辑
  async update(ctx) {
    const { bizCode, name, remark, roleId, roleType: roleTypeValue, permIds } = ctx.validator({
      bizCode: ctx.joi.string(),
      name: ctx.joi.string(),
      remark: ctx.joi.string(),
      roleId: ctx.joi.number().integer(),
      roleType: ctx.joi.number().integer(),
      permIds: ctx.joi.array().items(ctx.joi.number()),
    });

    const params = {
      ...this.getCommonParams(ctx),
      bizCode,
      name,
      remark,
      roleId,
      roleTypeValue,
      permIds,
    };
    const res = await new RoleService(ctx).update(params);
    ctx.success(res);
  }

  // 删除
  async delete(ctx) {
    const { bizCode, roleId, roleType: roleTypeValue } = ctx.validator({
      bizCode: ctx.joi.string(),
      roleId: ctx.joi.number().integer(),
      roleTypeValue: ctx.joi.number().integer(),
    });

    const params = {
      ...this.getCommonParams(ctx),
      bizCode,
      roleId,
      roleTypeValue,
    };
    const res = await new RoleService(ctx).delete(params);
    ctx.success(res);
  }

  // 获取角色对应的权限集
  async getPermOfRole(ctx) {
    const { bizCode, roleId, platform } = ctx.validator({
      bizCode: ctx.joi.string(),
      roleId: ctx.joi.number().integer(),
      platform: ctx.joi.number().integer(),
    });

    const params = {
      ...this.getCommonParams(ctx),
      bizCode,
      roleId,
      platform,
    };
    const res = await new RoleService(ctx).getPermOfRole(params);
    ctx.success(res);
  }

  // 获取权限树
  async getPermTree(ctx) {
    const { bizCode, platform, roleIds } = ctx.validator({
      bizCode: ctx.joi.string(),
      platform: ctx.joi.number().integer(),
      roleIds: ctx.joi.array().items(ctx.joi.number()),
    });

    const params = {
      ...this.getCommonParams(ctx),
      bizCode,
      platform,
      roleIds,
    };
    const res = await new PermService(ctx).getPermTree(params);
    ctx.success(res);
  }

  // 获取角色列表
  async getRoleList(ctx) {
    const { biz_code: bizCode, filter } = ctx.validator({
      ['biz_code']: ctx.joi.string(),
      filter: ctx.joi.object({
        bizKeyGroup: ctx.joi.string(),
        needStaffCount: ctx.joi.boolean(),
      }),
    });
    const params = {
      ...this.getCommonParams(ctx),
      bizCode,
      filter,
    };

    const res = await new RoleService(ctx).getRoleList(params);
    ctx.success(res);
  }

  // 连锁角色列表查看
  async getRetailRoleList(ctx) {
    const { onlineShopOpen, page, pageSize, shopRole, withStaffCount } = ctx.validator({
      onlineShopOpen: ctx.joi.boolean(),
      page: ctx.joi.number(),
      pageSize: ctx.joi.number(),
      shopRole: ctx.joi.number(),
      withStaffCount: ctx.joi.boolean(),
    });

    const params = {
      kdtId: +ctx.kdtId,
      onlineShopOpen,
      page,
      pageSize,
      shopRole,
      withStaffCount,
    };
    const res = await new RetailRoleService(ctx).getRetailRoleList(params);
    ctx.success(res);
  }
}

module.exports = RolesController;
