const BaseController = require('../../base/BaseController');
const TemplateService = require('../../../services/api/delivery/TemplateService');
const DeliverySettingService = require('../../../services/api/delivery/DeliverySettingService');
const TradeSettingService = require('../../../services/api/trade/setting/TradeSettingService');

class DeliveryController extends BaseController {
  constructor(ctx) {
    super(ctx);
    this.ctx.biz = '设置-配送';
  }

  async getIndexHtml(ctx) {
    ctx.redirect('/v4/trade/express');
  }

  async listTemplates(ctx) {
    const { kdtId } = ctx;
    const data = await new TemplateService(ctx).listTemplates({
      ...ctx.query,
      kdtId,
      fromApp: 'wsc',
    });

    ctx.json(0, '', data);
  }

  async getSettingV3(ctx) {
    const { kdtId } = ctx;
    const data = await new DeliverySettingService(ctx).getSettingV3({
      ...ctx.query,
      kdtId,
      fromApp: 'wsc',
    });

    ctx.json(0, '', data);
  }

  async updateSetting(ctx) {
    const { kdtId } = ctx;
    const data = await new DeliverySettingService(ctx).updateSetting({
      ...ctx.request.body,
      kdtId,
    });

    ctx.json(0, '', data);
  }

  async getTemplateDetail(ctx) {
    const { kdtId } = ctx;
    const data = await new TemplateService(ctx).getTemplateDetail({
      ...ctx.query,
      fromApp: 'wsc',
      kdtId,
    });

    ctx.json(0, '', data);
  }

  async create(ctx) {
    const { kdtId } = ctx;
    const data = await new TemplateService(ctx).create({
      ...ctx.request.body,
      kdtId,
    });

    ctx.json(0, '', data);
  }

  async update(ctx) {
    const { kdtId } = ctx;
    const data = await new TemplateService(ctx).update({
      ...ctx.request.body,
      kdtId,
    });

    ctx.json(0, '', data);
  }

  async replicate(ctx) {
    const { kdtId } = ctx;
    const data = await new TemplateService(ctx).replicate({
      ...ctx.query,
      kdtId,
    });

    ctx.json(0, '', data);
  }

  async deleteTemp(ctx) {
    const { kdtId } = ctx;
    const data = await new TemplateService(ctx).deleteTemp({
      ...ctx.request.body,
      kdtId,
    });

    ctx.json(0, '', data);
  }

  async getTradeSetting(ctx) {
    const { kdtId } = ctx;
    const data = await new TradeSettingService(ctx).getSettingByKdtId({
      kdtId,
    });

    ctx.json(0, '', data);
  }
}

module.exports = DeliveryController;
