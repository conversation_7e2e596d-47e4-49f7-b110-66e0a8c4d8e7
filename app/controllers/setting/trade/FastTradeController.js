const TradeSettingService = require('../../../services/api/trade/business/TradeSettingService');
const BaseController = require('../../base/BaseController');

class FastTradeController extends BaseController {
  /**
   * @description 获取极速下单设置
   *  该接口主要是包装店铺配置的逻辑（提供给B端使用，C端如需使用请单独查询店铺配置接口）
   */
  async getGetFastTradeSettingJson() {
    const { ctx } = this;

    const result = await new TradeSettingService(ctx).getFastTradeSetting({ kdtId: ctx.kdtId });
    ctx.success(result);
  }

  /** @description 极速下单设置编辑接口 */
  async getEditFastTradeSettingJson() {
    const { ctx } = this;
    const { settingStatus, type } = ctx.getPostData();
    const dto = { type, settingStatus, kdtId: ctx.kdtId };

    const result = await new TradeSettingService(ctx).editFastTradeSetting(dto);
    ctx.success(result);
  }
}

module.exports = FastTradeController;
