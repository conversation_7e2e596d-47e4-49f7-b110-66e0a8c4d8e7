const { checkBranchStore, checkPureWscSingleStore } = require('@youzan/utils-shop');
const BaseController = require('../../base/BaseController');
const PackageReservationService = require('../../../services/api/trade/PackageReservationService');

class PackageReservationController extends BaseController {
  async getActivityDetails(ctx) {
    const shopInfo = ctx.getState('shopInfo');
    const { kdtId: shopId } = ctx;

    const res = await new PackageReservationService(ctx).getActivityDetails({
      shopId,
    });

    return ctx.successRes({
      data: res,
      isInitSetting: res && Object.keys(res).length > 0,
      isPureWscSingleStore: checkPureWscSingleStore(shopInfo),
      isBranchStore: checkBranchStore(shopInfo),
    });
  }

  getActivityParams(ctx) {
    const query = ctx.request.body || {};

    const {
      goodsJoinType,
      bookingType,
      liveCodeDTO,
      bootSettingDTO,
      goodsList,
      goodsCollectionIdList,
      miniProgramPath,
      appId,
      shortLink,
      id,
      status,
    } = query;

    const prams = {
      id,
      status,
      goodsJoinType,
      bookingType,
      liveCodeDTO,
      bootSettingDTO,
      goodsList,
      goodsCollectionIdList,
      miniProgramPath,
      appId,
      shortLink,
    };

    Object.keys(prams).forEach(key => {
      if (!prams[key] && prams[key] !== 0) {
        delete prams[key];
      }
    });

    return prams;
  }

  async edit(ctx) {
    const { kdtId: shopId } = ctx;
    const params = this.getActivityParams(ctx);

    const res = await new PackageReservationService(ctx).edit({
      shopId,
      ...params,
    });
    return ctx.successRes(res);
  }

  async create(ctx) {
    const { kdtId: shopId } = ctx;
    const params = this.getActivityParams(ctx);

    const res = await new PackageReservationService(ctx).create({
      shopId,
      ...params,
    });
    return ctx.successRes(res);
  }
}

module.exports = PackageReservationController;
