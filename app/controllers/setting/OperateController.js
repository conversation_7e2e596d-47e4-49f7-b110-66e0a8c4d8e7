const BaseConstroller = require('../base/BaseController');
const StaffService = require('../../services/api/sam/StaffServiceV2');
const RecordService = require('../../services/api/ebiz/mall/RecordService');
const ItemOperateService = require('../../services/api/mall-item/ItemOperateService');

class OperateController extends BaseConstroller {
  constructor(ctx) {
    super(ctx);
    this.ctx.biz = '设置-操作记录';
  }

  async getIndexHtml(ctx) {
    const newOperate = await this.callService(
      'wsc-pc-base/common.GrayReleaseService',
      'isInGrayReleaseByKdtId',
      'new-optlist-wsc',
      +ctx.kdtId
    );
    if (newOperate) {
      return ctx.redirect(`https://www.youzan.com/v4/setting/newoperate`);
    }
    let modelMap = [
      {
        name: '全部',
        value: '',
      },
      {
        name: '商品',
        value: 'IC',
      },
      {
        name: '订单',
        value: 'TRADE',
      },
      {
        name: '客户',
        value: 'SCRM',
      },
      {
        name: '营销',
        value: 'UMP',
      },
      {
        name: '店铺',
        value: 'SHOWCASE',
      },
      {
        name: '设置',
        value: 'SET',
      },
    ];
    if (ctx.isYZEdu) {
      const eduMap = [
        {
          name: '教务',
          value: 'EDU',
        },
        {
          name: '应用',
          value: 'APP',
        },
        {
          name: '员工',
          value: 'STAFF',
        },
        {
          name: '招生',
          value: 'ENROLL',
        },
      ];
      modelMap = modelMap.concat(eduMap);
    }
    ctx.setGlobal({
      moduleMap: modelMap,
    });
    await ctx.render('setting/operate.html');
  }

  async getStaffList(ctx) {
    const { query, kdtId } = ctx;
    const page = +(query.pageNo || 1);
    const pageSize = +(query.pageSize || 20);
    const result = await new StaffService(ctx).findStaffsPages({
      page,
      pageSize,
      kdtId,
    });
    return ctx.json(0, 'OK', result);
  }

  async getOperatelogList(ctx) {
    const { kdtId } = ctx;
    const params = ctx.query;
    params.kdtId = kdtId;
    const result = await new RecordService(ctx).listRecordsPage(params);
    return ctx.successRes(result);
  }

  async recoveryGoods(ctx) {
    const userInfo = ctx.getLocalSession('userInfo');
    let params = ctx.request.body;
    this.validator.required(params.alias, 'alias不能为空');
    params = {
      alias: params.alias,
      operator: {
        userId: userInfo.userId,
        nickName: userInfo.nickName,
        clientIp: ctx.firstXff,
        source: 'wsc-pc-v4',
      },
    };
    const result = await new ItemOperateService(ctx).recoverItem(params);
    return ctx.json(0, 'OK', result);
  }
}

module.exports = OperateController;
