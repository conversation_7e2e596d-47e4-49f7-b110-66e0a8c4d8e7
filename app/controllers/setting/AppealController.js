const BaseController = require('../base/BaseController');
const LocksDubboService = require('../../services/api/ctu-open/LocksDubboService');
const MerchantWhiteNameListService = require('../../services/api/ctu-open/MerchantWhiteNameListService');
const MicroMallDeductionService = require('../../services/api/ctu-open/MicroMallDeductionService');
const MicroMallAppealService = require('../../services/api/ctu-open/MicroMallAppealService');
const ViolationIntegralService = require('../../services/api/ctu-open/ViolationIntegralService');
const RiskControlReadService = require('../../services/api/material/materialcenter/RiskControlReadService');

class AppealController extends BaseController {
  constructor(ctx) {
    super(ctx);
    this.ctx.biz = '设置-违规申诉';
  }

  // 获取页面
  async getIndexHtml(ctx) {
    await ctx.render('setting/appeal.html');
  }

  // 获取违规扣分列表
  async getDeductList(ctx) {
    const { kdtId } = ctx;

    const data = await new MicroMallDeductionService(ctx).searchDeductionDetail({
      ...ctx.query,
      kdtId,
    });

    return ctx.json(0, 'ok', data);
  }

  // 撤回违规扣分申诉
  async cancelDeductAppeal(ctx) {
    const { kdtId, userId } = ctx;
    const { lockTransId } = ctx.getPostData();

    const data = await new MicroMallAppealService(ctx).cancel({
      kdtId,
      userId,
      lockTransId,
    });

    return ctx.json(0, 'ok', data);
  }

  // 获取违规扣分分数
  async getDeductScore(ctx) {
    const { kdtId } = ctx;

    const data = await new MicroMallDeductionService(ctx).getKdtDeduction(kdtId);

    return ctx.json(0, 'ok', data);
  }

  // 获取违规扣分详情
  async getDeductDetail(ctx) {
    const { lockTransId } = ctx.query;
    const { kdtId } = ctx;

    const data = await new MicroMallDeductionService(ctx).detail(lockTransId, kdtId);

    return ctx.json(0, 'ok', data);
  }

  // 获取违规扣分申诉记录
  async getDeductHistory(ctx) {
    const { lockTransId } = ctx.query;

    const data = await new MicroMallDeductionService(ctx).listDeductionAppeal(lockTransId);

    return ctx.json(0, 'ok', data);
  }

  // 获取违规处置列表
  async getPunishList(ctx) {
    const { kdtId } = ctx;

    const data = await new LocksDubboService(ctx).listByKdtId4App({
      ...ctx.query,
      isWarning: false,
      kdtId,
    });

    return ctx.json(0, 'ok', data);
  }

  // 撤回违规处置申诉
  async cancelPunishAppeal(ctx) {
    const { kdtId, userId } = ctx;
    const { lockTransId, time } = ctx.getPostData();

    const data = await new MicroMallAppealService(ctx).withdrawAppeal({
      kdtId,
      userId,
      lockTransId,
      time,
    });

    return ctx.json(0, 'ok', data);
  }

  // 获取违规处置详情
  async getPunishDetail(ctx) {
    const { kdtId } = ctx;

    const data = await new LocksDubboService(ctx).getLockByLockTransId({
      ...ctx.query,
      kdtId,
    });

    return ctx.json(0, 'ok', data);
  }

  // 获取违规处置申诉记录
  async getPunishHistory(ctx) {
    const { kdtId } = ctx;
    const { lockTransId } = ctx.query;

    const data = await new MicroMallAppealService(ctx).listAppealRecord(lockTransId, kdtId);

    return ctx.json(0, 'ok', data);
  }

  // 获取当前受限处罚
  async getLimitList(ctx) {
    const { kdtId } = ctx;

    const data = await new LocksDubboService(ctx).getLockByKdtId(kdtId);

    return ctx.json(0, 'ok', data);
  }

  // 获取预警记录列表
  async getWarnList(ctx) {
    const { kdtId } = ctx;
    const params = {
      kdtId,
      isWarning: true,
      ...ctx.query,
    };

    const data = await new LocksDubboService(ctx).listByKdtId4App(params);

    return ctx.json(0, 'ok', data);
  }

  // 获取预警商品列表
  async queryWarnItems(ctx) {
    const { kdtId } = ctx;
    const params = {
      kdtId,
      ...ctx.query,
    };

    const data = await new LocksDubboService(ctx).queryWarnItems(params);

    return ctx.json(0, 'ok', data);
  }

  // 获取处罚商品列表
  async queryItems(ctx) {
    const { kdtId } = ctx;
    const params = {
      kdtId,
      ...ctx.query,
    };

    const data = await new LocksDubboService(ctx).queryItems(params);

    return ctx.json(0, 'ok', data);
  }

  // 查询商品是否还存在
  async queryGoodsExist(ctx) {
    const { kdtId } = ctx;
    const { sourceId } = ctx.query;
    const data = await new LocksDubboService(ctx).queryGoodsExist(sourceId, kdtId);
    return ctx.json(0, 'ok', data);
  }

  // 获取违规概览
  async getViolationOverview(ctx) {
    const { kdtId } = ctx;
    const data = await new LocksDubboService(ctx).getViolationOverview(kdtId);

    return ctx.json(0, 'ok', data);
  }

  // 根据店铺Id查询 积分汇总
  async queryTotalByKdtId(ctx) {
    const { kdtId } = ctx;
    const data = await new ViolationIntegralService(ctx).queryTotalByKdtId(kdtId);

    return ctx.json(0, 'ok', data);
  }

  // 根据mediaId批量查询视频信息
  async queryRefRelationPageList(ctx) {
    const { kdtId } = ctx;
    const params = {
      partnerBizId: kdtId,
      ...ctx.query,
    };
    const data = await new RiskControlReadService(ctx).queryRefRelationPageList(params);
    return ctx.json(0, 'ok', data);
  }

  // 查询风控白名单
  async getMerchantWhite(ctx) {
    const { kdtId } = ctx; 
    const validatorParams = ctx.validator(
      ctx.joi.object({
        data: ctx.joi.string(),
        status: ctx.joi.number(),
        createdStartTime: ctx.joi.string(),
        createdEndTime: ctx.joi.string(),
        enableStartTime: ctx.joi.string(),
        enableEndTime: ctx.joi.string(),
        pageSize: ctx.joi.number(),
        pageNum: ctx.joi.number(),
      }),
      { stripUnknown: true }
    );

    const params = {
      ...validatorParams,
      type: 'phone',
      kdtId,
    };
    const res = await new MerchantWhiteNameListService(ctx).merchantQueryYxWhite(params);
    return ctx.json(0, 'ok', res);
  }

  // 新增风控白名单
  async addMerchantWhite(ctx) {
    const { kdtId } = ctx;
    const { data } = ctx.getPostData();
    const params = {
      type: 'phone',
      kdtId,
      data,
    };
    const res = await new MerchantWhiteNameListService(ctx).merchantAddYxWhite(params);
    return ctx.json(0, 'ok', res);
  }

  // 启用/禁用/删除 白名单
  async updateMerchantWhite(ctx) {
    const { operateType, id } = ctx.getPostData()
    const { kdtId } = ctx;;
    const params = {
      id: +id,
      operateType: +operateType,
      kdtId,
    };
    const res = await new MerchantWhiteNameListService(ctx).merchantUpdateYxWhite(params);
    return ctx.json(0, 'ok', res);
  }
}

module.exports = AppealController;
