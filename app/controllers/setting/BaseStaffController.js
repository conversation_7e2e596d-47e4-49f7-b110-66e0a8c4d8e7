const BaseController = require('../base/BaseController');
const ScrmWhiteListService = require('../../services/api/scrm/ScrmWhiteListService');
const SalesChannelMgrService = require('../../services/api/shopcenter/shopfront/SalesChannelMgrService');
const SalesOrgCategoryService = require('../../services/api/shopcenter/outer/SalesOrgCategoryService');
const { checkChainStore } = require('@youzan/utils-shop');
class BaseStaffController extends BaseController {
  /**
   * 判断是否在多渠道白名单内
   * @param {*} ctx
   */
  async initSupportMultiChannelData(ctx) {
    const { kdtId } = ctx;
    // 查询是否在多渠道白名单
    const isSupportMultiChannel = await new SalesChannelMgrService(ctx).isHitWhiteList(kdtId);
    ctx.setGlobal('isSupportMultiChannel', isSupportMultiChannel);
  }

  /**
   * 获取单个组织分类信息
   * @param {*} ctx
   */
  async initOrgInfo(ctx) {
    const { kdtId } = ctx;
    const orgInfo = await new SalesOrgCategoryService(ctx).queryOne(kdtId);
    ctx.setGlobal('orgInfo', orgInfo);
  }

  /**
   * 初始化角色详情与员工详情的权限树过滤配置
   * @param {*} ctx
   */
  async initPermissionFilter(ctx) {
    const { kdtId } = ctx;

    // 1. 构造过滤器 flags 请求队列
    const filterFlagPromises = [];

    const SCRM_MIGRATE_COMPLETE = 2;
    filterFlagPromises.push({
      filterKey: 'scrmMigrated',
      promise: new ScrmWhiteListService(ctx)
        .get({
          appName: 'wsc-pc-v4',
          kdtId,
          tenant: 'WSC',
        })
        .then(rsp => rsp.status === SCRM_MIGRATE_COMPLETE)
        .catch(() => false),
    });

    const filterFlags = {};
    (await Promise.all(filterFlagPromises.map(item => item.promise))).forEach((flag, index) => {
      filterFlags[filterFlagPromises[index].filterKey] = flag;
    });

    // 2. 获取 filter apollo 配置
    const permissionFilterConfigs =
      ctx.apolloClient.getConfig({
        appId: 'wsc-pc-v4',
        namespace: 'shop-settings',
        key: 'permission-tree-filter',
      }) || [];

    // 3. 将 flag 写到每一个filter配置项上，最后的结构为 { key: string, flag: boolean | undefined, blacklist, whitelist, blackPrefix, whitePrefix }
    permissionFilterConfigs.forEach(item => {
      item.flag = filterFlags[item.key];
    });

    ctx.setGlobal('permissionTreeFilter', permissionFilterConfigs);
  }
}

module.exports = BaseStaffController;
