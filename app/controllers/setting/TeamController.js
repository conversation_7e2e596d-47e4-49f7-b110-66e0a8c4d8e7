import { SHOP_CONFIG_KEY } from '../../constants';

const BaseController = require('../base/BaseController');
const BenefitDisplayCarrierService = require('../../services/api/scrm/BenefitDisplayCarrierService');
const CardTemplateService = require('../../services/api/scrm/CardTemplateService');
const StaffService = require('../../services/api/sam/StaffServiceV2');
const ShopAddressService = require('../../services/api/shop/ShopAddressServiceV2');
const MallTradeSettingService = require('../../services/api/ebiz/mall/TradeSettingService');
const WeappAccountService = require('../../services/api/channels/WeappAccountService');
const MpAccountService = require('../../services/api/channels/MpAccountService');
const IShopQueryProvider = require('../../services/qtt/IShopQueryProvider');
const InvoiceSwitchService = require('../../services/api/trade/InvoiceSwitchService');
const DiscountService = require('../../services/api/bigdata/ad/DiscountService');
const ShopConfigReadService = require('../../services/api/shop-config/ShopConfigReadService');
const ShopChainReadService = require('../../services/api/shop-center/ShopChainReadService');
const LevelGroupService = require('../../services/api/scrm/api/LevelGroupService');
const ShopConfigWriteService = require('../../services/api/shop-config/ShopConfigWriteService');
const ShopVisitService = require('../../services/api/shopcenter/shopfront/ShopVisitService');
const MessageMarketingSupportSettingService = require('../../services/api/message/MessageMarketingSupportSettingService');
const LimitPurchaseExtService = require('../../services/api/ump/limit/LimitPurchaseExtService');
const FansAccumulateFollowCheckService = require('../../services/api/fans/FansAccumulateFollowCheckService');
const OmniChannelService = require('../../services/api/uic/OmniChannelService');
const SplitFlowService = require('../../services/api/trade/detail/SplitFlowService');
const AbilityReadService = require('../../services/api/shop-center/AbilityReadService');
const ShopWhiteListService = require('../../services/api/wholesale/ShopWhiteListService');
const ShopGrayReleaseService = require('../../services/api/showcase/center/ShopGrayReleaseService.js');
const InvoiceProviderQueryService = require('../../services/api/invoice/InvoiceProviderQueryService.js');
const ScrmWhiteListService = require('../../services/api/scrm/ScrmWhiteListService');
const OverseaShopSettingService = require('../../services/api/common/OverseaShopSettingService');
const ShopShelfPageGrayReleaseService = require('../../services/api/shelf/ShopShelfPageGrayReleaseService');
const OptimalPricingShopConfigService = require('../../services/api/ump/marketing/OptimalPricingShopConfigService');
const { ShopAbilityUtil } = require('@youzan/plugin-shop-ability');
const isInGrayReleaseByKdtId = require('../../lib/WhiteListUtils');

const _get = require('lodash/get');
const { fromApp } = require('../../constants');
const {
  checkUnifiedOnlineBranchStore,
  checkWscBranchStore,
  checkRetailMinimalistBranchStore,
  checkChainStore,
  checkEduBranchStoreV4,
  createTotalCheck,
} = require('@youzan/utils-shop');

const CARD_PAGE_SIZE = 50;

const PAYMENT_ORDER_MODE = 'payment_setting_order_mode';
const PAYMENT_USER_PRIVACY = 'trade_user_privacy_info_display';
const SKU_ORDER = 'sku_direct_order';

class TeamController extends BaseController {
  constructor(ctx) {
    super(ctx);
    this.ctx.biz = '设置-通用设置';
  }

  async init() {
    await super.init();
    if (!this.ctx.acceptJSON) {
      await this.ctx.getAbilitiesByMultiNamespace(this.ctx, [
        {
          namespaceId: 'commonSettings',
          businessId: 'wsc-pc-v4',
        },
        {
          namespaceId: 'goods',
          businessId: 'wsc-pc-goods',
        },
        {
          namespaceId: 'orders',
          businessId: 'wsc-pc-trade',
        },
      ]);
      await this.ctx.getAbilitiesByMultiNamespaceV2(
        this.ctx,
        [{ businessName: 'v4', namespaceName: '通用设置', abilityKey: [] }],
        { shopInfo: this.ctx.getState('shopInfo') }
      );
    }
  }

  async getIndexHtml(ctx) {
    const kdtId = ctx.kdtId;
    // 小红书独立版本判断
    const xhsIndependentAbility = await new ShopAbilityUtil(ctx).checkAbilityValid({
      keys: ['xhs_independent_ability'],
      kdtId: ctx.kdtId,
    })

    if (xhsIndependentAbility && ctx.url.indexOf('xhs-independent') < 0) {
      this.ctx.redirect('/v4/setting/team/xhs-independent-index');
      return;
    }
    const hqKdtId = this.getHqKdtId();
    const userInfo = ctx.getLocalSession('userInfo');
    // 微商城、零售3.0、极简版的网店
    const isBranchStore =
      checkWscBranchStore(ctx.state.shopInfo) ||
      checkUnifiedOnlineBranchStore(ctx.state.shopInfo) ||
      checkEduBranchStoreV4(ctx.state.shopInfo) ||
      checkRetailMinimalistBranchStore(ctx.state.shopInfo);
    this.initShopInfo();

    const [
      isShowEstimatedPrice,
      weappCardImgRuleSwitch,
      isQttShop,
      invoiceProviderList,
    ] = await Promise.all([
      ...['goods_estimated_price', 'weapp_card_img_rule_switch'].map(key =>
        isInGrayReleaseByKdtId(
          ctx,
          {
            appId: 'wsc-h5-goods',
            namespace: 'wsc-h5-goods.goods-release',
            key,
          },
          kdtId
        ).catch(() => false)
      ),
      new IShopQueryProvider(ctx).isQttShop(ctx.rootKdtId || ctx.kdtId),
      new InvoiceProviderQueryService(ctx)
        .queryInvoiceProviderList({
          kdtId: ctx.kdtId,
        })
        .catch(() => []),
    ]);

    // 自动确认收货时间取消检验的白名单
    const expressAutoConfirmCancelWhiteList = ctx.apolloClient.getConfig({
      appId: 'trade-business',
      namespace: 'application',
      key: 'express_auto_confirm_cancel_valid_white_list',
    });

    const isCanelExpressAutoConfirmValid = (expressAutoConfirmCancelWhiteList || []).includes(
      ctx.kdtId
    );

    ctx.setGlobal('isQttShop', isQttShop);
    ctx.setGlobal('isCanelExpressAutoConfirmValid', isCanelExpressAutoConfirmValid);
    ctx.setGlobal('invoiceProviderList', invoiceProviderList);
    ctx.setGlobal(
      'invoiceTypeConfig',
      ctx.apolloClient.getConfig({
        appId: 'wsc-pc-trade',
        namespace: 'wsc-pc-trade.application',
        key: 'invoice_config',
      })
    );

    try {
      // 是否开启【买家自助修改地址】白名单
      const isGrayAllowBuyerModifyAddress = await new SplitFlowService(ctx).modifyAddressSplitFlow(
        kdtId
      );
      ctx.setGlobal('isGrayAllowBuyerModifyAddress', isGrayAllowBuyerModifyAddress);
    } catch (err) {
      ctx.setGlobal('isGrayAllowBuyerModifyAddress', false);
    }
    const [
      inNewPageListMode,
      mpAccountInfo,
      role,
      weappCode,
      defRefundAddress,
      isInRealNameAuthWhiteList,
      overseaSetting,
      isInCityOrderWhiteList,
      isInvoiceServiceOpening,
      inGuaranteeWhiteList,
      branchShopInfo,
      inReservedStockWhitelist,
      inWatermarkWhitelist,
      isNewEnterShop,
      isBuyLimitConfigReady = false,
      isInGoodsVideoDanmakuAllowList,
      inInGoodsRankingList,
      inArticleGrayV2 = false,
      isAuthSame = {},
      productMasterDataManageability,
      isInWholesaleWhitelist = false,
    ] = await Promise.all([
      new ShopGrayReleaseService(ctx).isHit({
        kdtId,
        grayReleaseKey: 'NEW_PAGE_MODE',
      }),
      new MpAccountService(ctx).getMpAccountByKdtId(kdtId),
      new StaffService(ctx).findStaffRole({
        kdtId,
        isPhysical: true,
        adminId: userInfo.userId,
        biz: fromApp,
      }),
      new WeappAccountService(ctx).getWeappCodeLcByKdtId(kdtId),
      new ShopAddressService(ctx).queryDefaultShopAddress(kdtId, 1),
      // 实名验证白名单
      this.grayRelease('real_name_auth', kdtId),
      new OverseaShopSettingService(ctx).query({ kdtId }).catch(() => {}),
      // 同城订单白名单
      this.grayRelease('local_order_print', kdtId),
      new InvoiceSwitchService(ctx).isInvoiceServiceOpening(kdtId).catch(() => false),
      // 是否在精简版店铺有赞担保白名单中
      this.grayRelease('zwsc_guarantee', kdtId),
      // 是网店才获取店铺信息，rootKdtId为该网店的总部id
      isBranchStore ? new ShopChainReadService(ctx).queryShopNodeInfo(kdtId) : Promise.resolve({}),
      // 预留库存方式
      this.grayRelease('deduct_not_occupied', kdtId),
      // 店铺水印
      this.grayRelease('watermark_whitelist', kdtId),
      // 新进店规则, 只针对零售连锁(D、L)有效
      checkChainStore(ctx.state.shopInfo)
        ? new ShopVisitService(ctx).checkShopVisitWhiteList(hqKdtId).catch(() => false)
        : Promise.resolve(false),
      new LimitPurchaseExtService(ctx).checkConfigReady(kdtId).catch(err => {
        ctx.logger.error('获取限购数据准备状态异常', err, err.extra);
        return false;
      }),
      this.grayRelease('goods_video_danmaku', kdtId),
      this.grayRelease('shop_ranking_list', kdtId),
      // 涨粉组件灰度
      new FansAccumulateFollowCheckService(ctx).inArticleGray('fans_second', kdtId),
      // 开放平台账号校验
      new OmniChannelService(ctx).kdtIdAuthTypeJudge({ kdtId }),
      new AbilityReadService(ctx).queryShopAbilityInfo(
        ctx.kdtId,
        'product_master_data_manage_ability'
      ),
      // 查询当前店铺是否在批发白名单中
      new ShopWhiteListService(ctx).isInWhiteList({
        key: 'wholesale_seller_whitelist',
        kdtId,
      }),
    ]);
    const isAdmin = role && role.length && role[0].roleId === 1;
    const { rootKdtId } = branchShopInfo;
    const params = ['goods_recommend'];
    let hqShopGoodsRecommend = {};
    if (rootKdtId) {
      const [hqShopGoodsRecommendRes, showOnlineShopStatusConfig] = await Promise.all([
        new ShopConfigReadService(ctx).queryShopConfigs(rootKdtId, params),
        this.grayRelease('shop_ope_status', rootKdtId),
      ]);
      hqShopGoodsRecommend = hqShopGoodsRecommendRes;
      ctx.setGlobal('showOnlineShopStatusConfig', showOnlineShopStatusConfig);
    }

    ctx.setGlobal('inNewPageListMode', inNewPageListMode);
    ctx.setGlobal('weappVersion', weappCode && weappCode.releasedVersion);
    ctx.setGlobal('teamStatus', ctx.getState('teamStatus'));
    ctx.setGlobal('isInvoiceServiceOpening', isInvoiceServiceOpening);
    ctx.setGlobal('inGuaranteeWhiteList', inGuaranteeWhiteList);
    ctx.setGlobal('hqShopGoodsRecommend', hqShopGoodsRecommend);
    ctx.setGlobal('productMasterDataManageability', productMasterDataManageability);
    // 公众号详情
    ctx.setGlobal('mpAccountInfo', mpAccountInfo);

    // 实名验证白名单
    ctx.setGlobal('isInRealNameAuthWhiteList', isInRealNameAuthWhiteList);
    // 店铺的跨境认证信息
    ctx.setGlobal('overseaSetting', overseaSetting);
    // 同城订单白名单
    ctx.setGlobal('isInCityOrderWhiteList', isInCityOrderWhiteList);
    ctx.setGlobal('isAdmin', isAdmin); // 是否是高级管理员
    ctx.setGlobal('defRefundAddress', defRefundAddress); // 默认退货地址
    ctx.setGlobal('inReservedStockWhitelist', inReservedStockWhitelist);
    ctx.setGlobal('inWatermarkWhitelist', inWatermarkWhitelist);
    ctx.setGlobal('isNewEnterShop', isNewEnterShop);
    ctx.setGlobal('isBuyLimitConfigReady', isBuyLimitConfigReady);
    ctx.setGlobal('isInGoodsVideoDanmakuAllowList', isInGoodsVideoDanmakuAllowList);
    ctx.setGlobal('isInGoodsRankingList', inInGoodsRankingList);
    // 是否是免费会员可引导的店铺类型
    this.setNeedFreeMemberGuideShop();
    ctx.setGlobal('isShowEstimatedPrice', isShowEstimatedPrice);
    ctx.setGlobal('weappCardImgRuleSwitch', weappCardImgRuleSwitch);
    this.setShowH5ItemShare();
    // 是否使用线上连接器地址
    const useOnlineConnectLink = ctx.apolloClient.getConfig({
      appId: 'wsc-pc-goods',
      namespace: 'wsc-pc-goods.goods-migrate',
    });
    ctx.setGlobal('useOnlineConnectLink', useOnlineConnectLink.useOnlineLink);
    ctx.setGlobal('inArticleGrayV2', inArticleGrayV2);
    ctx.setGlobal('isAuthSame', isAuthSame?.flag || false);
    ctx.setGlobal('isRetail', this.checkIsRetailType(ctx));

    await this.setAttractFansV2LimitWeappVersion(ctx);
    // 是否需要隐藏有赞担保标
    const hideGuaranteeList = ctx.apolloClient.getConfig({
      appId: 'yz-fin-aigis',
      namespace: 'yz-fin-aigis.black-gold-shop-list',
      key: 'yzdb.shield',
    });
    ctx.setGlobal('hideGuarantee', (hideGuaranteeList || []).includes(kdtId.toString()));
    const showRefundSetting = await isInGrayReleaseByKdtId(
      ctx,
      {
        appId: 'wsc-pc-v4',
        namespace: 'gray',
        key: 'refund_stock_white_list',
      },
      kdtId
    );
    ctx.setGlobal('showRefundSetting', showRefundSetting);

    // 是否是批发场景
    const isWholesaleScence = isInWholesaleWhitelist && this.ctx.url.includes('wholesale');
    ctx.setGlobal('isWholesaleScence', isWholesaleScence);
    const inCrossStoreSelfFetchSwitch = this.grayReleaseByKdtId(
      ctx,
      { key: 'cross_store_self_fetch_switch', namespace: 'gray' },
      hqKdtId || kdtId
    );

    ctx.setGlobal('inCrossStoreSelfFetchSwitch', inCrossStoreSelfFetchSwitch);

    // 是否支持商详页沉浸式导航配置
    const inGoodsDetailNavigationBarStyle = this.grayReleaseByKdtId(
      ctx,
      { key: 'goods_detail_navigation_bar_style', namespace: 'gray' },
      hqKdtId || kdtId
    );
    ctx.setGlobal('inGoodsDetailNavigationBarStyle', inGoodsDetailNavigationBarStyle);

    const { available: isCrmShop } = await new ScrmWhiteListService(ctx).get({ kdtId: ctx.kdtId });

    this.ctx.setGlobal('isCrmShop', isCrmShop);
    // 是否使用过点单宝
    const { hasOldPage, newPageModel } = await new ShopShelfPageGrayReleaseService(ctx).isHit(kdtId);
    ctx.setGlobal('isUpdateOrderPage', newPageModel);
    ctx.setGlobal('isUsedShelfOrderPage', hasOldPage);

    // 接入产品矩阵
    const features = await ctx.matrix.getDataSourceByFragmentIds([
      'trade.payment-setting',
      'shop.shop-general-setting',
    ]);
    ctx.matrix.setDataToRuntime(features);
    await ctx.render('setting/team.html');
  }

  /**
   * 获取展示载体信息
   * @param {*} ctx
   */
  async getDefault(ctx) {
    const { kdtId } = ctx;
    const result = await new BenefitDisplayCarrierService(ctx).getDefault({
      kdtId,
    });
    ctx.successRes(result);
  }

  /**
   * 更新展示载体信息
   * @param {*} ctx
   */
  async setDefault(ctx) {
    const { displayCarriers } = ctx.getPostData();
    const { kdtId } = ctx;
    const result = await new BenefitDisplayCarrierService(ctx).setDefault({
      displayCarriers,
      kdtId,
    });
    ctx.successRes(result);
  }

  /**
   * 获取可设置会员卡列表
   * @param {*} ctx
   */
  async getAvailableList(ctx) {
    const { kdtId } = ctx;
    const cardTemplateService = new CardTemplateService(ctx);
    const commonParam = {
      kdtId,
      grantConditionList: [
        // 付费卡
        {
          allowBuy: true,
        },
      ],
      options: {
        // 带上详情，用于过滤已经过期的会员卡
        withDetail: true,
      },
      pageSize: CARD_PAGE_SIZE,
    };
    let cardList = [];

    const firstResult = await cardTemplateService.getAvailableList({
      ...commonParam,
      page: 1,
    });

    // 一次接口不能获取全部会员卡的情况
    const totalCount = firstResult.paginator.totalCount;
    // const totalCount = 400;
    cardList = cardList.concat(firstResult.items);
    if (cardList.length < totalCount) {
      const pageCount = Math.ceil(totalCount / CARD_PAGE_SIZE);
      await Promise.all(
        Array.from({ length: pageCount - 1 }, (v, i) => i + 2).map(page =>
          cardTemplateService.getAvailableList({
            ...commonParam,
            page,
          })
        )
      ).then(results => {
        cardList = cardList.concat(...results.map(result => result.items));
      });
    }

    ctx.successRes(cardList);
  }

  /**
   * 获取付费等级列表
   */
  async getGroupDetailList() {
    const { type = 2 } = this.ctx.query;
    let result = await new LevelGroupService(this.ctx).getGroupDetailList({
      kdtId: this.ctx.kdtId,
      type,
    });
    if (result.length) {
      // 后端大哥说取返回数组中第一项就好了
      result = _get(result, '[0].levelV2List', []);
    }

    this.ctx.successRes(result);
  }

  async editSettingByKdtId(ctx) {
    const { kdtId } = ctx;
    const {
      [PAYMENT_USER_PRIVACY]: userPrivacy,
      selfFetchVerifyCrossShop,
      optimalPricingSetting,
    } = ctx.request.body;
    const userInfo = ctx.getLocalSession('userInfo');
    const operator = {
      fromApp,
      id: userInfo.id,
      name: userInfo.nickName,
    };
    const [
      tradeResult,
      discountResult,
      prevPrivacyStatus,
      shopConfigWriteResult,
      optimalPricingSettingResult,
    ] = await Promise.all([
      new MallTradeSettingService(ctx).editSettingByKdtId({
        ...ctx.request.body,
        source: 'merchant-pc', // 保持跟 queryTradeSetting 入参的 source 保持一致，给后端做操作日志比对
        kdtId,
      }),
      new DiscountService(ctx).setDiscount({
        kdtId,
        isOn: ctx.request.body.enableDiscount ? true : false,
        percentage: Number(ctx.request.body.discount) * 10,
      }),
      // 由于法务和合规要求 独立设置个人信息开启状态变更行为
      // 前置判断是否更新 无更新则不调用日志 不记录日志
      new ShopConfigReadService(ctx).queryShopConfig(kdtId, PAYMENT_USER_PRIVACY),
      new ShopConfigWriteService(ctx).setShopConfigs({
        kdtId,
        operator,
        configs: {
          [SHOP_CONFIG_KEY.SELF_FETCH_VERIFY_CROSS_SHOP]: selfFetchVerifyCrossShop,
        },
      }),
      new OptimalPricingShopConfigService(ctx).enableOptimalPricing({
        isPricingEnabled: !!optimalPricingSetting,
        kdtId,
        rootKdtId: this.getHqKdtId() || kdtId,
        operator,
      }),
    ]);

    // 判断前值和当前值是否一致
    const shouldUpdateUserPrivacy = !(_get(prevPrivacyStatus, 'value') === userPrivacy);

    // 独立调用更新接口 记录日志
    const privacyWriteResult = shouldUpdateUserPrivacy
      ? await new ShopConfigWriteService(ctx).setShopConfigs({
          kdtId,
          operator,
          configs: {
            [PAYMENT_USER_PRIVACY]: userPrivacy,
          },
        })
      : true;
    const isSuccess =
      tradeResult &&
      discountResult &&
      privacyWriteResult &&
      shopConfigWriteResult &&
      optimalPricingSettingResult;

    return ctx.json(0, 'ok', isSuccess);
  }

  async getSettingByKdtId(ctx) {
    const { kdtId } = ctx;
    const [tradeSetting, discountSetting, optimalPricingSetting, { configs }] = await Promise.all([
      new MallTradeSettingService(ctx).queryTradeSetting({
        kdtId,
        source: 'merchant-pc', // 保持跟 editSettingByKdtId 入参的 source 保持一致，给后端做操作日志比对
      }),
      new DiscountService(ctx).getDiscount({
        kdtId,
      }),
      // 获取最优价配置
      new OptimalPricingShopConfigService(ctx).getOptimalPricingSetting({ kdtId }).catch(() => {}),
      new ShopConfigReadService(ctx).queryShopConfigs(kdtId, [
        PAYMENT_ORDER_MODE,
        PAYMENT_USER_PRIVACY,
        SHOP_CONFIG_KEY.SELF_FETCH_VERIFY_CROSS_SHOP,
        SKU_ORDER,
      ]),
    ]);
    const userPrivacy = _get(configs, PAYMENT_USER_PRIVACY, '0');
    // 后端一定会返回值，所以这个默认值1应该没有作用，暂时不更改
    return ctx.json(0, 'ok', {
      ...tradeSetting,
      [PAYMENT_USER_PRIVACY]: userPrivacy,
      selfFetchVerifyCrossShop: _get(configs, SHOP_CONFIG_KEY.SELF_FETCH_VERIFY_CROSS_SHOP, '0'),
      enableDiscount: discountSetting.isOn ? 1 : 0,
      discount: discountSetting.isOn ? (discountSetting.discount / 10).toString() : '',
      optimalPricingSetting,
    });
  }
  // 获取是否显示微信支付优惠设置
  async getIsShowWepaySetting(ctx) {
    const { kdtId } = ctx;
    let isShowWepaySetting;
    const shopInfo = ctx.getState('shopInfo');
    if (shopInfo.shopType === 7) {
      isShowWepaySetting = false;
    } else {
      isShowWepaySetting = await this.grayRelease('wsc_wepay_discount', kdtId);
    }

    return ctx.json(0, 'ok', {
      isShowWepaySetting,
    });
  }
  // 查询小程序自有是否打开
  async getSelfPayIsOpen(ctx) {
    const { kdtId } = ctx;
    const params = ['weixin_pay_origin', 'wx_applet_origin'];
    const result = await new ShopConfigReadService(ctx).queryShopConfigs(kdtId, params);
    return ctx.json(0, 'OK', result);
  }

  async getPushStatus4Manager(ctx) {
    const { kdtId } = ctx;
    const result = await new MessageMarketingSupportSettingService(ctx).getPushStatus4Manager(
      kdtId
    );
    return ctx.json(0, 'OK', result);
  }

  setNeedFreeMemberGuideShop() {
    const { ctx } = this;
    const shopMetaInfo = ctx.getState('shopMetaInfo');
    // 引导办会员二期可引导店铺类型
    const memberGuideShopType =
      ctx.apolloClient.getConfig({
        appId: 'wsc-h5-goods',
        namespace: 'wsc-h5-goods.goods-release',
        key: 'member-guide-shop-type',
      }) || '';
    const shopTypeCheck = createTotalCheck(shopMetaInfo);
    ctx.setGlobal(
      'needFreeMemberGuideShop',
      memberGuideShopType.split(',').some(item => shopTypeCheck[item])
    );
  }

  async setAttractFansV2LimitWeappVersion(ctx) {
    const defaultVersion = '2.88.5';
    try {
      const result = await ctx.apolloClient.getConfig({
        appId: 'wsc-pc-ump',
        namespace: 'wsc-pc-ump.miniprogram',
        key: 'attract_fans_v2_limit_weapp_version',
      });
      ctx.setGlobal('attractFansV2LimitWeappVersion', result?.[0] || defaultVersion);
    } catch (e) {
      ctx.setGlobal('attractFansV2LimitWeappVersion', defaultVersion);
    }
  }

  setShowH5ItemShare() {
    const { ctx } = this;
    // H5智能分享开关
    const isShowH5ItemShare = ctx.apolloClient.getConfig({
      appId: 'wsc-h5-goods',
      namespace: 'wsc-h5-goods.switch',
      key: 'show_h5_item_share',
    });
    ctx.setGlobal('isShowH5ItemShare', isShowH5ItemShare);
  }
}

module.exports = TeamController;
