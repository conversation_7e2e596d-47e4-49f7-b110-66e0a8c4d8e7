const { assign } = require('lodash');
const BaseController = require('../../base/BaseController');
const FxInfoService = require('../../../services/api/shop/FxInfoService');

class FxInfoController extends BaseController {
  constructor(ctx) {
    super(ctx);
    this.ctx.biz = '设置-分销信息';
  }

  async getIndexHtml(ctx) {
    /**
     * 做一层兜底操作,新应用线上逻辑有问题直接打到老应用
     * */
    // ctx.redirect('https://www.youzan.com/v2/setting/store/index#fenxiao');
    await ctx.render('setting/store/fx_info.html');
  }

  async getFxInfo(ctx) {
    const { kdtId } = ctx;
    const fxInfo = await new FxInfoService(ctx).getFxInfo(kdtId);
    return ctx.json(0, 'ok', fxInfo);
  }

  /**
   * 更新店铺
   */
  async updateFxInfo(ctx) {
    const { kdtId } = ctx;
    const params = assign({}, ctx.request.body, { kdtId });
    const res = await new FxInfoService(ctx).updateFxInfo(params);
    return ctx.json(0, 'ok', res);
  }
}

module.exports = FxInfoController;
