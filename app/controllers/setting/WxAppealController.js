const BaseController = require('../base/BaseController');
const WxAppealDubboService = require('../../services/api/wx-appeal');

class WxAppealController extends BaseController {
  constructor(ctx) {
    super(ctx);
    this.ctx.biz = '设置-微信申诉';
  }

  init() {
    super.init();
    super.initUserInfo();
  }

  async getIndexHtml(ctx) {
    await ctx.render('setting/wx-appeal.html');
  }

  async getDetailHtml(ctx) {
    await ctx.render('setting/wx-appeal-detail.html');
  }

  async getListBykdtId(ctx) {
    const {
      kdtId,
      query: { pageNum = 1, pageSize = 10 },
    } = ctx;
    const res = await new WxAppealDubboService(ctx).getListByKdtId({
      kdtId,
      pageNum,
      pageSize,
      orderBy: 'desc',
      sortField: 'checkedAt',
    });
    return ctx.json(0, 'OK', res);
  }

  // 2022.08.02
  // v4商家后台查询微信处罚记录
  async listForMerchant(ctx) {
    const { kdtId, pageNum = 1, pageSize = 10 } = ctx;
    const res = await new WxAppealDubboService(ctx).listForMerchant({
      kdtId,
      pageNum,
      pageSize,
      ...ctx.query,
    });
    return ctx.json(0, 'OK', res);
  }

  async getDetailByTransId(ctx) {
    const {
      kdtId,
      query: { transId = '' },
    } = ctx;
    const res = await new WxAppealDubboService(ctx).getDetailByTransId(kdtId, transId);
    return ctx.json(0, 'OK', res);
  }

  async getDefaultBaseInfo(ctx) {
    const {
      kdtId,
      query: { transId = '', last = '' },
    } = ctx;
    const res = await new WxAppealDubboService(ctx).getDefaultBaseInfo(kdtId, transId, last);
    return ctx.json(0, 'OK', res);
  }

  async getByStep(ctx) {
    const {
      kdtId,
      query: { transId = '', step = '' },
    } = ctx;

    const res = await new WxAppealDubboService(ctx).getByStep(kdtId, transId, step);
    return ctx.json(0, 'OK', res);
  }

  // 保存并下一步
  async saveByStep(ctx) {
    const { kdtId } = ctx;
    const {
      baseInfo = {},
      supplement = {},
      step = '',
      orders = [],
      transId = '',
    } = ctx.getPostData();
    const res = await new WxAppealDubboService(ctx).saveByStep({
      baseInfo,
      supplement,
      orders,
      step,
      transId,
      kdtId,
    });
    return ctx.json(0, 'OK', res);
  }

  async listHistoryByTransId(ctx) {
    const {
      query: { transId = '' },
    } = ctx;
    const res = await new WxAppealDubboService(ctx).listHistoryByTransId(transId);
    return ctx.json(0, 'OK', res);
  }

  // 补充材料并提交
  async supplement(ctx) {
    const { kdtId } = ctx;
    const postData = ctx.getPostData();
    const res = await new WxAppealDubboService(ctx).supplement({ ...postData, kdtId });
    return ctx.json(0, 'OK', res);
  }
}

module.exports = WxAppealController;
