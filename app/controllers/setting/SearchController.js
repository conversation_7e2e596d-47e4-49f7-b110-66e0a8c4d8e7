const { checkEduWktStore } = require('@youzan/utils-shop');
const BaseController = require('../base/BaseController');
const HelpSearchApiService = require('../../services/api/ebiz/mall/HelpSearchApiService');

const BLOCK_WKT_PLUGIN_LIST = [
  // 快手卖货助手
  '/v4/ump/appcenter-thirdapp/43724',
  // 学员奖励
  '/v4/vis/edu/page/rewards',
  // 学员证书
  '/v4/vis/edu/page/certificate',
  // 报名表单
  '/v2/showcase/feature/design',
  // 好友助力
  '/v4/vis/pct/page/boost',
  // 转介绍
  '/v4/vis/ump/invite-reward',
  // 攒学费
  '/v4/vis/ump/tuition-offset',
  // 进店有礼
  '/v4/ump/visit-gift',

  // 支付宝生活号
  '/v4/message/alipay',
  '/v3/apps/alipay',
  // 涨粉海报
  '/v4/industry/fans-poster/list',
  // 裂变优惠券
  '/v4/ump/coupon-split/list',
  // 秒杀
  '/v4/ump/seckill',
  '/v2/ump/seckill',
  // 优惠券礼包
  '/v4/ump/coupon-package/list',
  // 趣味测试
  '/v4/vis/pct/page/exam',
  // 刮刮卡
  '/v4/ump/lucky/cards',
  '/v2/apps/cards',
  // 生肖翻翻看
  '/v4/ump/lucky/zodiac',
  '/v2/apps/zodiac',
  // 幸运大抽奖
  '/v4/ump/lucky/draw',
  '/v2/apps/wheel',
  // 摇一摇
  '/v4/ump/lucky/shake',
  '/v2/apps/shake',
  // 日历签到
  '/v4/ump/checkin',
  // 场景营销
  '/v4/scrm/marketing/scene/dashboard',
  // 店铺笔记
  '/v4/shop/shopnote',
  '/v4/deco/content-center/shopnote',
  // 主图水印
  '/v4/ump/watermark',
];

const BLOCK_WKT_PLUGIN_SET = new Set();

BLOCK_WKT_PLUGIN_LIST.forEach(v => BLOCK_WKT_PLUGIN_SET.add(v));

/**
 * 全局搜索页面
 * @class SearchController
 * @extends {BaseController}
 */
class SearchController extends BaseController {
  constructor(ctx) {
    super(ctx);
    this.ctx.biz = '设置-全局搜索';
  }

  /**
   * 页面渲染
   * @param {*} ctx
   * @memberof SearchController
   */
  async getIndexHtml(ctx) {
    const { isBeauty } = ctx;
    ctx.setGlobal('isBeauty', isBeauty);
    await ctx.render('setting/search.html');
  }

  /**
   * 获取搜索结果
   * @param {*} ctx
   * @memberof SearchController
   */
  async getSearchResult(ctx) {
    const { kdtId, userId } = ctx;
    const shopInfo = ctx.getState('shopInfo');
    const { rootKdtId: hqKdtId = kdtId } = shopInfo;
    const { keyword, page, pageSize, sourceType, shopVersion, searchType } = ctx.query;
    const params = {
      keyword,
      page,
      pageSize,
      sourceType,
      kdtId,
      hqKdtId,
      userId,
      searchType,
    };

    if (shopVersion) {
      params.shopVersion = shopVersion;
    }
    const result = await new HelpSearchApiService(ctx).searchAll(params);

    // 微课堂店铺需要屏蔽该版本不支持的插件能力
    // See: https://xiaolv.qima-inc.com/#/demand/search?show=true&ids=75402
    if (
      checkEduWktStore(ctx.getState('shopInfo')) &&
      result &&
      result.shortcuts &&
      result.shortcuts.length
    ) {
      result.shortcuts = result.shortcuts.filter(
        v => !BLOCK_WKT_PLUGIN_SET.has(new URL(v.url).pathname)
      );
    }

    ctx.json(0, 'ok', result);
  }

  /**
   * 获取零售搜索结果
   * @param {*} ctx
   * @memberof SearchController
   */
  async getSearchRetailResult(ctx) {
    const { shopInfo = {} } = ctx.state || {};
    const isNewStore = shopInfo.saasSolution > 0;
    const { keyword, page, pageSize, sourceType, shopVersion } = ctx.query;
    const verStr = isNewStore ? `${shopVersion}_retailv3` : shopVersion;
    const params = {
      keyword,
      page,
      pageSize,
      sourceType,
      shopVersion: verStr,
    };
    const result = await new HelpSearchApiService(ctx).searchAll(params);
    ctx.json(0, 'ok', result);
  }
}

module.exports = SearchController;
