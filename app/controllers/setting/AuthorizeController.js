const args = require('@youzan/utils/url/args');
const { checkHqStore, checkPartnerStore } = require('@youzan/utils-shop');
const BaseController = require('../base/BaseController');
const OmniChannelService = require('../../services/api/uic/OmniChannelService');
const SellerService = require('../../services/api/fx/SellerService');
const ChannelCoreAccountService = require('../../services/api/channels/ChannelCoreAccountService');
const { CHANNEL_MAP, BUSINESS_TYPE } = require('../../constants');
const AgreementProdService = require('../../services/api/agreement/AgreementProdService');
const HomepageService = require('../../services/api/homepage/HomepageService');
const GiftBagService = require('../../services/api/scrm/GiftBagService.js');
const ShopConfigReadService = require('../../services/api/shop-config/ShopConfigReadService');
const ShopConfigWriteService = require('../../services/api/shop-config/ShopConfigWriteService');

class AuthorizeController extends BaseController {
  constructor(ctx) {
    super(ctx);
    this.ctx.biz = '设置-用户授权';
  }

  async getIndexHtml(ctx) {
    const { shopInfo = {}, teamStatus = {}, isSuperStore = false } = ctx.state || {};
    const { rootKdtId = ctx.kdtId } = shopInfo;

    const showShopManagement = shopInfo.shopRole !== 1 && shopInfo.shopRole !== 2;
    const showAddress =
      shopInfo.shopRole === 0 ||
      (shopInfo.shopRole === 1 && shopInfo.chainOnlineShopMode === 1) ||
      (shopInfo.shopRole === 2 && shopInfo.onlineShopOpen);
    const showCard =
      shopInfo.shopRole !== 2 && !(teamStatus.weixinServer || teamStatus.weixinCertsub);

    const [fxBaseInfo, inMemberJoinGray, protocolList, mpResult] = await Promise.all([
      // 1. 分销商基础信息
      new SellerService(ctx).getBaseInfo(ctx.kdtId),

      // 2. 个人中心入会灰度
      new GiftBagService(ctx).inMemberJoinGray(ctx.kdtId),

      // 3. 店铺协议列表
      this.getShopProtocolsData(ctx).catch((e) => {
        ctx.logger.warn(`查询店铺协议列表异常：${e && (e.message || e.msg || e)}`, e);
        return [];
      }),

      // 4. 判断店铺是否授权了QQ小程序
      new ChannelCoreAccountService(ctx).queryMpBindInfoByKdtId({
        accountType: CHANNEL_MAP.QQ,
        businessType: BUSINESS_TYPE.Wsc,
        externalId: ctx.kdtId,
      }),
    ]);

    // 授权记录，是否可见
    const grayYzCostVisible = this.grayReleaseByKdtId(
      ctx,
      {
        namespace: 'wsc-pc-v4.setting.authorize',
        key: 'yz_cost_visible',
      },
      rootKdtId || ctx.kdtId // 连锁使用总部 ID
    );

    const isHqStore = checkHqStore(shopInfo);
    const isPartnerStore = checkPartnerStore(shopInfo);

    // 判断店铺是否为分销商，非null -> 是 null -> 否
    const showDistribution = !isSuperStore && !!fxBaseInfo;

    ctx.setGlobal({
      isHqStore,
      protocolList,
      isPartnerStore,
      inMemberJoinGray: !!inMemberJoinGray,
      yzCostVisible: grayYzCostVisible,
      hasBoundQQ: !!mpResult, // 如果 mpResult 有数据就代表已经绑定了
    });

    await ctx.render('setting/authorize.html', {
      showShopManagement,
      showDistribution,
      showAddress,
      showCard,
    });
  }

  async getSceneModelInfoListByKdtId(ctx) {
    const { kdtId } = ctx;
    const data = await new OmniChannelService(ctx).getSceneModelInfoListByKdtId({
      ...ctx.query,
      kdtId,
    });
    return ctx.json(0, '', data);
  }

  async isWeChatAuthJudge(ctx) {
    const { kdtId } = ctx;
    const data = await new OmniChannelService(ctx).isWeChatAuthJudge({ kdtId });
    return ctx.json(0, '', data);
  }

  async kdtIdAuthTypeJudge(ctx) {
    const { kdtId } = ctx;
    const data = await new OmniChannelService(ctx).kdtIdAuthTypeJudge({ kdtId });
    return ctx.json(0, '', data);
  }

  async saveSceneModelInfoList(ctx) {
    const { kdtId } = ctx;
    const params = ctx.request.body || {};
    const data = await new OmniChannelService(ctx).saveSceneModelInfoList({
      ...params,
      kdtId,
    });
    return ctx.json(0, '', data);
  }

  /**
   * 查询店铺协议列表
   */
  async getShopProtocolsData(ctx) {
    const kdtId = +ctx.kdtId;
    const { shopInfo = {} } = ctx.state || {};
    const { rootKdtId = kdtId } = shopInfo;

    const agreementProdService = new AgreementProdService(ctx);

    // (平台)用户协议、(平台)隐私协议
    const prodTypeList = ['PLATFORM_USER_USE', 'PLATFORM_USER_PRIVACY'];

    // 需要过滤标准商家隐私协议的店铺
    const excludeKdtId = ctx.apolloClient.getConfig({
      appId: 'wsc-h5-account',
      namespace: 'passport-tee-config',
      key: 'shopPrivacyExcludeKdtId',
    });
    const excludeShopPrivacyKdtIds = (excludeKdtId + '').split(',');

    // 商家定制协议
    const customProtocols = await agreementProdService.listByBizType({
      bizTypeList: ['TEXT_BESPOKE'],
      kdtId: rootKdtId,
      currentKdtId: kdtId,
      appName: this.APP_NAME,
    });

    // 如果有商家定制协议，则显示商家定制协议，不显示实标准商家协议
    if (!excludeShopPrivacyKdtIds.includes(rootKdtId + '') && customProtocols.length === 0) {
      // 标准商家隐私协议
      prodTypeList.push('SHOP_USER_PRIVACY');
    }

    const shopProtocols = await agreementProdService.batchQuery({
      agProdTypeList: prodTypeList,
      appName: this.APP_NAME,
    });

    /** 对协议列表排序 */
    const sortProtocols = (items) => {
      /** 协议展示顺序权重 */
      const PRIORITY_MAP = {
        PLATFORM_USER_USE: 100,
        PLATFORM_USER_PRIVACY: 200,
        SHOP_USER_PRIVACY: 300,
        DEFAULT: 9999,
      };

      const getPriority = (prodType) => PRIORITY_MAP[prodType] || PRIORITY_MAP.DEFAULT;

      return items.sort((pre, next) => getPriority(pre.agProdType) - getPriority(next.agProdType));
    };

    const protocols = await Promise.all(
      shopProtocols
        .concat(customProtocols)
        .map((item) => this.normalizeProtocolInfo(item, rootKdtId))
    );

    return sortProtocols(protocols);
  }

  /**
   * 统一协议信息
   */
  async normalizeProtocolInfo(item, rootKdtId) {
    if (!item) {
      return {};
    }

    let { kdtId: itemKdtId, agProdType, extInfo: { url, renderType } = {} } = item;

    // 商家隐私协议 URL 添加 kdtIdAlias
    if (agProdType === 'SHOP_USER_PRIVACY') {
      const kdtId = itemKdtId || rootKdtId;
      if (url && kdtId) {
        const kdtIdAlias = await this.getKdtIdAlias(kdtId);
        url = args.add(url, { kdtIdAlias });
      }
    } else if (!url && renderType === 2) {
      // 定制富文本协议
      // renderType: 1 链接；2 富文本
      url = args.add('https://account.youzan.com/protocol/vip-bespoke', {
        type: agProdType,
      });
    }

    return {
      ...item,
      url,
    };
  }

  async getKdtIdAlias(kdtId) {
    if (!kdtId) {
      return '';
    }
    return new HomepageService(this.ctx).getAliasByKdtId(kdtId);
  }

  async getProtocolAuthConfigData(ctx) {
    const configRaw = await new ShopConfigReadService(ctx).queryShopConfig(
      ctx.kdtId,
      'protocol_auth_config'
    );

    try {
      return JSON.parse(configRaw.value);
    } catch (e) {
      ctx.logger.warn(`协议授权配置数据异常：${JSON.stringify(configRaw)}`, e);

      throw new Error('配置数据读取异常');
    }
  }

  /**
   * 获取隐私协议配置
   */
  async getProtocolAuthConfig(ctx) {
    const result = await this.getProtocolAuthConfigData(ctx);

    ctx.success(result);
  }

  /**
   * 保存隐私协议配置
   */
  async saveProtocolAuthConfig(ctx) {
    const { authScenes = [] } = ctx.validator(
      {
        authScenes: ctx.joi.array().items(ctx.joi.string()).required(),
      },
      { stripUnknown: true }
    );

    const originData = await this.getProtocolAuthConfigData(ctx);

    const result = await new ShopConfigWriteService(ctx).setShopConfig({
      ...this.getOperatorParams(),
      key: 'protocol_auth_config',
      value: JSON.stringify({
        ...originData, // 保留原始其他字段，避免以后数据扩展后被直接覆盖
        authScenes: authScenes.filter((scene) => scene !== 'required'), // required 配置不需要存储，仅用于 UI 显示
      }),
    });

    ctx.success(result);
  }
}

module.exports = AuthorizeController;
