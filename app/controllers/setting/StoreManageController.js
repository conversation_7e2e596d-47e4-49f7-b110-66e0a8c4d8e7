const BaseController = require('../base/BaseController');
const PhysicalStoreService = require('../../services/api/multistore/center/PhysicalStoreService');
const StoreReadService = require('../../services/api/multistore/center/StoreReadService');
const StoreOperateService = require('../../services/api/multistore/center/StoreOperateService');

class StoreManageController extends BaseController {
  constructor(ctx) {
    super(ctx);
    this.ctx.biz = '设置-门店管理';
  }

  get operator() {
    const ctx = this.ctx;
    const operator = {
      fromApp: 'wsc-pc-ump',
      operatorId: ctx.getLocalSession('userInfo').id,
    };
    return operator;
  }

  async getIndexHtml(ctx) {
    await ctx.render('setting/store-manage.html');
  }

  async create(ctx) {
    const postData = ctx.getPostData();
    const params = { ...postData, kdtId: ctx.kdtId, operator: this.operator };
    const result = await new PhysicalStoreService(ctx).create(params);
    return ctx.success(result);
  }

  async listByPage(ctx) {
    const { pageSize, page, keyword } = ctx.getRequestData();
    const params = {
      pageSize,
      page,
      keyword,
      source: 'store-manage', // 门户管理支持精确搜索
      isStore: 1,
      kdtId: ctx.kdtId,
    };
    const result = await new StoreReadService(ctx).listPagedByCondition(params);
    return ctx.success(result);
  }

  async delete(ctx) {
    const { id: storeId } = ctx.getPostData();
    const param = {
      storeId,
      kdtId: ctx.kdtId,
      operatorId: this.operator.operatorId,
    };
    const result = await new StoreOperateService(ctx).delete(param);
    return ctx.success(result);
  }

  async getStoreInfoWithOption(ctx) {
    const { storeId } = ctx.getRequestData();
    const params = {
      storeId,
      kdtId: ctx.kdtId,
    };
    const result = await new StoreReadService(ctx).getStoreInfoWithOption(params);
    return ctx.success(result);
  }

  async updateByKdtIdAndId(ctx) {
    const postData = ctx.getPostData();
    const params = {
      ...postData,
      kdtId: ctx.kdtId,
      operator: this.operator,
    };
    const result = await new PhysicalStoreService(ctx).updateByKdtIdAndId(params);
    return ctx.success(result);
  }
}

module.exports = StoreManageController;
