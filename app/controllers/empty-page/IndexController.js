const BaseController = require('../base/BaseController');
const { MicroAppMode } = require('@youzan/micro-app-plugin');

class IndexController extends BaseController {
  async getIndexHtml() {
    const { ctx } = this;

    try {
      if (ctx.microAppMode === MicroAppMode.MicroPageContainer) {
        const menuInfo = ctx.getState('menuInfo');
        const { microAppConfig } = menuInfo;
        ctx.setState('microAppConfig', JSON.stringify(microAppConfig));
      }
    } catch (error) {
      ctx.logger.error('从菜单获取 microAppConfig 失败');
    }

    await ctx.render('empty-page/index.html');
  }
}

module.exports = IndexController;
