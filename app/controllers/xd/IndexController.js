const BaseController = require('../base/BaseController');
const { fromApp } = require('../../constants');
const XdOpenShopService = require('../../services/api/xd/XdOpenShopService');
const StorageQiniuFileWriteService = require('../../services/api/material/materialcenter/StorageQiniuFileWriteService');
const ShopConfigWriteService = require('../../services/api/shop-config/ShopConfigWriteService');
const ShopConfigReadService = require('../../services/api/shop-config/ShopConfigReadService');
const OpenShopMetadataService = require('../../services/api/xd/OpenShopMetadataService');
const XdWeekBusinessReportService = require('../../services/api/xd/XdWeekBusinessReportService');
const XdShopBrandService = require('../../services/api/xd/XdShopBrandService');
const QiniuAggregationWriteService = require('../../services/api/material/materialcenter/QiniuAggregationWriteService');
const StaffManageService = require('../../services/api/xd/StaffManageService');
const CorpCoreKVService = require('../../services/api/common/CorpCoreKVService');
const RegionV3Service = require('../../services/api/delivery/RegionV3Service');

const isInGrayReleaseByKdtId = require('../../lib/WhiteListUtils');

class IndexController extends BaseController {
  constructor(ctx) {
    super(ctx);
    this.ctx.biz = '微信小店托管';
  }

  init() {
    super.init();
  }

  /**
   * 保存初始化进度
   */
  async saveInitProgress(ctx) {
    const { kdtId } = ctx;
    const { value } = ctx.request.body;
    const userInfo = ctx.getLocalSession('userInfo');

    const ret = await new ShopConfigWriteService(ctx).setShopConfig({
      kdtId,
      key: this.getInitProgressKey(),
      operator: {
        id: userInfo.userId,
        name: userInfo.nickName,
        type: 1,
        fromApp,
      },
      value: String(value),
    });
    ctx.json(0, 'success', ret);
  }

  /**
   * 保存初始化进度
   */
  async getInitProgress(ctx) {
    const { kdtId } = ctx;
    const ret = await new ShopConfigReadService(ctx).queryShopConfig(
      kdtId,
      this.getInitProgressKey()
    );
    ctx.json(0, 'success', ret);
  }

  getInitProgressKey() {
    return `wxxd_agent_init_progress`;
  }

  /**
   * 获取详情
   */
  async getDetail(ctx) {
    const { kdtId } = ctx;
    const { appId, authStatus } = ctx.request.query;
    const ret = await new XdOpenShopService(ctx).getDetail({ kdtId, appId, authStatus });
    ctx.json(0, 'success', ret);
  }

  /**
   * 编辑并提交
   */
  async editAndSubmit(ctx) {
    const { kdtId } = ctx;
    const userInfo = ctx.getLocalSession('userInfo');

    const ret = await new XdOpenShopService(ctx).editAndSubmit({
      ...ctx.request.body,
      kdtId,
      userId: userInfo.userId,
    });
    ctx.json(0, 'success', ret);
  }

  /**
   * 获取代开店状态
   */
  async getOpenShopStatus(ctx) {
    const { kdtId } = ctx;
    const { appId } = ctx.request.query;
    const ret = await new XdOpenShopService(ctx).getOpenShopStatus({ kdtId, appId });
    ctx.json(0, 'success', ret);
  }

  /**
   * 获取七牛云上传token
   */
  async getUploadToken(ctx) {
    const userInfo = ctx.getLocalSession('userInfo');
    const params = {
      channel: 'videoChannelImage',
      // 通用素材
      storeType: 2,
      // 媒体类型, 1 图片
      mediaType: 1,
      // 访问权限 1 公开 2 私有
      mediaAccessType: 2,
      // 有赞账号
      operatorType: 1,
      // 有赞账号id
      operatorId: userInfo.userId,
      // 来源应用
      fromApp: 'wsc-pc-v4',
    };

    const { token } = await new QiniuAggregationWriteService(ctx).getUploadToken(params);

    ctx.json(0, 'ok', {
      token,
    });
  }

  /**
   * 查询代开店授权链接
   */
  async getAuthLink(ctx) {
    const { kdtId } = ctx;
    const ret = await new XdOpenShopService(ctx).getAuthLink({ kdtId });
    ctx.json(0, 'success', ret);
  }

  /**
   * 查询微信代开店状态
   */
  async getWechatOpenShopStatus(ctx) {
    const { kdtId } = ctx;
    const { appId } = ctx.request.query;

    const ret = await new XdOpenShopService(ctx).getWechatOpenShopStatus({ kdtId, appId });
    ctx.json(0, 'success', ret);
  }

  /**
   * 品牌词典搜索
   */
  async brandDictionarySearch(ctx) {
    const { nextKey = 0, outChannel = 1, pageSize = 20, searchName } = ctx.request.query;
    const ret = await new XdShopBrandService(ctx).brandDictionarySearch({
      outChannel: Number(outChannel),
      nextKey: Number(nextKey),
      pageSize: Number(pageSize),
      searchName,
    });
    ctx.json(0, 'success', ret);
  }

  /** 获取开户行信息 */
  async querySettleBankList(ctx) {
    const ret = await new OpenShopMetadataService(ctx).querySettleBankList();
    ctx.json(0, 'success', ret);
  }

  /** 获取微信小店周报 */
  async getXdWeekBusinessReport(ctx) {
    const { kdtId } = ctx;
    const { flowId, weekStr } = ctx.request.query;
    const ret = await new XdWeekBusinessReportService(ctx).getXdWeekBusinessReport({
      kdtId,
      flowId,
      weekStr,
    });
    ctx.json(0, 'success', ret);
  }

  /** 获取企业微信员工列表 */
  async getWecomStaffList(ctx) {
    const { kdtId } = ctx;
    const userInfo = ctx.getLocalSession('userInfo');

    const { pageNum = 1, pageSize = 20, searchName } = ctx.request.query;

    const wecomKdtId = await new CorpCoreKVService(ctx).getWecomKdtIdByWechatMallId({
      wechatKdtId: kdtId,
    });

    const params = {
      operator: {
        yzUserId: userInfo.userId,
        nickname: userInfo.nickName,
        appName: fromApp,
      },
      pageRequest: {
        pageNumber: Number(pageNum),
        pageSize: Number(pageSize),
      },
      staffMobileOrName: searchName,
      status: 0,
      yzKdtId: wecomKdtId,
    };

    const ret = await new StaffManageService(ctx).search(params);
    ctx.json(0, 'success', ret);
  }

  /** 根据ID获取地区模型 */
  async getRegionModelById(ctx) {
    const { regionId } = ctx.request.query;
    const ret = await new RegionV3Service(ctx).getRegionModelById({
      regionId,
      fromApp,
    });
    ctx.json(0, 'success', ret);
  }

  /**
   * 是否开启品牌店铺编辑（默认开启）
   */
  async getIsEnableBrand(ctx) {
    const disableBrand = await isInGrayReleaseByKdtId(
      ctx,
      {
        appId: 'wsc-pc-v4',
        namespace: 'gray',
        key: 'disable_wxxd_brand',
      },
      ctx.kdtId
    );
    ctx.json(0, 'success', !disableBrand);
  }
}

module.exports = IndexController;
