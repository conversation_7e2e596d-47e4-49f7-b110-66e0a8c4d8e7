const BaseController = require('../base/BaseController');
const RegionService = require('../../services/api/delivery/RegionService');
const RegionV3Service = require('../../services/api/delivery/RegionV3Service');

class DeliveryController extends BaseController {
  constructor(ctx) {
    super(ctx);
    this.ctx.biz = '配送';
  }

  async getRegionPartitions(ctx) {
    const data = await new RegionService(ctx).getRegionPartitions(ctx.query);
    return ctx.json(0, '', data);
  }

  async getAllIdNameMap(ctx) {
    const data = await new RegionV3Service(ctx).getAllIdNameMap({
      ...ctx.query,
      fromApp: 'wsc',
    });
    ctx.json(0, '', data);
  }
}

module.exports = DeliveryController;
