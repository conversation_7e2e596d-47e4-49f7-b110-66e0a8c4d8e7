const BaseController = require('../../base/BaseController');

const AgentImportReportService = require('../../../services/third-plugin/business-weekly-report/AgentImportReportService');
const AsyncResponseService = require('../../../services/third-plugin/business-weekly-report/BigDataAsyncResponseService');

class AgentImportReportController extends BaseController {
  constructor(ctx) {
    super(ctx);
  }

  async importReportHead() {
    const ctx = this.ctx;

    const params = {
      kdtId: ctx.kdtId,
      adminId: ctx.userId,
      retailSource: 'wsc-pc-v4',
      ...ctx.request.body,
    };

    const result = await new AgentImportReportService(ctx).importReportHead(params);
    return ctx.json(0, 'ok', result);
  }

  async getReportHeadResponse() {
    const ctx = this.ctx;

    const result = await new AsyncResponseService(ctx).getResponse(ctx.query.responseId);
    return ctx.json(0, 'ok', result);
  }
}

module.exports = AgentImportReportController;
