const BaseController = require('../../base/BaseController');

const AgentPerformanceTargetService = require('../../../services/third-plugin/business-weekly-report/AgentPerformanceTargetService');

class AgentPerformanceTargetController extends BaseController {
  constructor(ctx) {
    super(ctx);
  }

  async createConfig() {
    const ctx = this.ctx;
    const data = ctx.request.body;

    const params = {
      kdtId: ctx.kdtId,
      adminId: ctx.userId,
      retailSource: 'wsc-pc-v4',
      ...data,
    };

    const result = await new AgentPerformanceTargetService(ctx).createConfig(params);
    return ctx.json(0, 'ok', result);
  }

  async queryConfig() {
    const ctx = this.ctx;

    const params = {
      kdtId: ctx.kdtId,
      adminId: ctx.userId,
      retailSource: 'wsc-pc-v4',
    };

    const result = await new AgentPerformanceTargetService(ctx).queryConfig(params);
    return ctx.json(0, 'ok', result);
  }
}

module.exports = AgentPerformanceTargetController;
