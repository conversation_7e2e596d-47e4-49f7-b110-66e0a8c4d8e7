const BaseController = require('../../base/BaseController');

const DingTalkAppConfigService = require('../../../services/third-plugin/business-weekly-report/DingTalkAppConfigService');
const CorpOpenService = require('../../../services/third-plugin/business-weekly-report/CorpOpenService');

/**
 * 消息通道配置状态
 */
const ChannelConfigStatus = {
  /** 配置异常 */
  Error: 0,
  /** 配置正常 */
  Ok: 1,
  /** 未绑定此服务（目前仅企微用到） */
  Unbind: 10,
};

class IndexController extends BaseController {
  constructor(ctx) {
    super(ctx);
  }

  // 获取钉钉配置状态
  async getDingTalkChannelStatus() {
    const { ctx } = this;
    const rawConfig = await new DingTalkAppConfigService(ctx).getDingTalkConfig(ctx.kdtId);
    if (!rawConfig) {
      return null;
    }

    return rawConfig.status;
  }

  // 获取企业微信配置状态
  async getWecomChannelStatus() {
    const { ctx } = this;

    const wecomStatus = await new CorpOpenService(ctx).getBoundWecomStatus(ctx.kdtId);
    let status = ChannelConfigStatus.Error;

    if (!wecomStatus.wecomKdtId) {
      status = ChannelConfigStatus.Unbind;
    } else if (
      wecomStatus.miniProgramCheckResult &&
      wecomStatus.generalCheckResult &&
      wecomStatus.generalCheckCode === 0
    ) {
      status = ChannelConfigStatus.Ok;
    }

    return status;
  }

  /**
   * 获取指定频道的状态。
   * dingTalk: 钉钉
   * wecom: 企业微信
   * @return {object} 消息通道配置状态
   * @example: {
   *   dingTalk: {
   *     status: ChannelConfigStatus.Ok,
   *   },
   *   wecom: {
   *     status: ChannelConfigStatus.Ok,
   *   },
   * }
   */
  async getChannelStatus() {
    const { ctx } = this;

    const dingTalkStatus = await this.getDingTalkChannelStatus();
    const wecomStatus = await this.getWecomChannelStatus();

    const result = {
      dingTalk: {
        status: dingTalkStatus,
      },
      wecom: {
        status: wecomStatus,
      },
    };
    ctx.json(0, 'ok', result);
  }
}

module.exports = IndexController;
