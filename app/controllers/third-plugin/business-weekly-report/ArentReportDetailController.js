const BaseController = require('../../base/BaseController');

const AgentReportConfigService = require('../../../services/third-plugin/business-weekly-report/AgentReportConfigService');
const SelfReportService = require('../../../services/third-plugin/business-weekly-report/SelfReportService');
const SingleManageService = require('../../../services/api/staff/SingleManageService');
const AuditService = require('../../../services/third-plugin/business-weekly-report/AuditService');
const BusinessReportAgentService = require('../../../services/third-plugin/business-weekly-report/BusinessReportAgentService');

const uuidv4 = require('uuid/v4');

class AgentReportPushConfigController extends BaseController {
  constructor(ctx) {
    super(ctx);
  }

  getBaseParams(ctx) {
    const operatorInfo = this.getOperatorParams();
    return {
      ...operatorInfo,
      userId: ctx.userId,
      requestId: uuidv4(),
      operator: JSON.stringify(operatorInfo.operator),
    };
  }

  // 获取概要数据
  async getReportOverviewData() {
    const { ctx } = this;
    const { kdtId } = ctx;
    const { id: userId, nickName } = ctx.getLocalSession('userInfo');
    const params = {
      kdtId,
      operator: {
        operatorId: String(userId),
        operatorName: nickName,
      },
    };
    const result = await new BusinessReportAgentService(ctx).getData(params);
    return ctx.json(0, 'ok', result);
  }

  /**
   * 获取报表列表的list
   */
  async getReportList() {
    const { ctx } = this;
    const { kdtId, query } = ctx;
    const { page, pageSize } = query;
    const params = {
      pageNo: +page,
      pageSize: +pageSize,
      kdtId,
      retailSource: 'wsc-pc-v4',
      adminId: ctx.userId,
    };
    const result = await new AgentReportConfigService(ctx).queryConfigList(params);
    return ctx.json(0, 'ok', result);
  }

  /**
   * 获取执行记录的list
   */
  async getExecutionRecords() {
    const { ctx } = this;
    const { query } = ctx;
    const { page, pageSize, reportId } = query;
    const params = {
      ...this.getBaseParams(ctx),
      page: +page,
      pageSize: +pageSize,
      reportId,
      dataTypeList: [3, 4],
    };
    const result = await new SelfReportService(ctx).queryReport(params);
    return ctx.json(0, 'ok', result);
  }

  async getReportDownloadUrl(ctx) {
    const { reportId } = ctx.getPostData();
    const params = {
      reportId,
      ...this.getBaseParams(ctx),
    };

    const result = await new SelfReportService(ctx).getDownload(params);
    return ctx.json(0, 'ok', result);
  }

  async getPasswd() {
    const { ctx } = this;
    const params = ctx.getPostData();
    params.kdtId = ctx.kdtId;
    params.userId = ctx.userId;
    params.operatorId = ctx.getLocalSession('userInfo').id;

    this.validator
      .required(params.kdtId, '请求参数 kdtId 不能为空')
      .required(params.userId, '请求参数 operatorId 不能为空');

    const res = await new SelfReportService(ctx).getPasswd(params);
    return ctx.json(0, 'ok', res);
  }

  async getShopKeeper() {
    const { ctx } = this;
    const { kdtId } = ctx;
    const operatorId = ctx.getLocalSession('userInfo').id;
    try {
      const rst = await new SingleManageService(ctx).getShopKeeper({
        kdtId,
        operatorId,
      });
      return ctx.json(0, 'ok', rst);
    } catch (err) {
      this.ctx.logger.warn(`店铺负责人基础信息获取失败：${err.message}`, err);
      return ctx.json(0, 'ok', {});
    }
  }

  /**
   * 上报审计信息
   */
  async reportAudit(ctx) {
    const params = ctx.getPostData();
    params.time = Date.now();
    params.operator = `${ctx.userId}`;
    params.kdtId = ctx.kdtId;

    const result = await new AuditService(ctx).report(params);

    ctx.json(0, 'ok', result);
  }
}

module.exports = AgentReportPushConfigController;
