const BaseController = require('../../base/BaseController');

const AgentInitConfigService = require('../../../services/third-plugin/business-weekly-report/AgentInitConfigService');
const ScrmWhiteListService = require('../../../services/api/scrm/ScrmWhiteListService');

class AgentInitConfigController extends BaseController {
  constructor(ctx) {
    super(ctx);
  }

  async createInitConfig() {
    const ctx = this.ctx;
    const { initType } = ctx.validator(
      ctx.joi.object({
        initType: ctx.joi.number().integer(),
      })
    );

    const params = {
      kdtId: ctx.kdtId,
      adminId: ctx.userId,
      retailSource: 'wsc-pc-v4',
      initType,
    };

    const result = await new AgentInitConfigService(ctx).createConfig(params);
    return ctx.json(0, 'ok', result);
  }

  async queryInitConfig() {
    const ctx = this.ctx;

    const params = {
      kdtId: ctx.kdtId,
      adminId: ctx.userId,
      retailSource: 'wsc-pc-v4',
    };

    const result = await new AgentInitConfigService(ctx).queryInitConfig(params);
    return ctx.json(0, 'ok', result);
  }

  async getShopInfo() {
    const ctx = this.ctx;
    const shopInfo = ctx.getState('shopInfo');
    const { available: isCrmShop } = await new ScrmWhiteListService(ctx).get({ kdtId: ctx.kdtId });
    return ctx.json(0, 'ok', { ...shopInfo, isCrmShop });
  }
}

module.exports = AgentInitConfigController;
