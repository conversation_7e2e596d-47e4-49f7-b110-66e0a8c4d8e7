const { checkBranchStore, checkRetailShop } = require('@youzan/utils-shop');
const BaseController = require('../../base/BaseController');

const ReportService = require('../../../services/third-plugin/business-weekly-report/ReportService');
const SelfReportService = require('../../../services/third-plugin/business-weekly-report/SelfReportService');
const AgentReportConfigService = require('../../../services/third-plugin/business-weekly-report/AgentReportConfigService');
const AsyncResponseService = require('../../../services/third-plugin/business-weekly-report/AsyncResponseService');

class ReportController extends BaseController {
  constructor(ctx) {
    super(ctx);
  }

  async getBatchReportData() {
    const ctx = this.ctx;

    const { userId, kdtId } = ctx;
    const shopInfo = ctx.getState('shopInfo');
    const { rootKdtId, shopTopic, shopType, shopRole, saasSolution } = shopInfo || {};
    const { startDay, endDay, selfDefineKdtIndex, ...rest } = ctx.query;

    let params = {
      sessionKdtId: kdtId,
      userId,
      fromApp: 'wsc-pc-v4',
      kdtId,
      hqKdtId: rootKdtId || kdtId,
      shopTopic,
      shopType,
      shopRole,
      saasSolution,
      timeParam: {
        startDay,
        endDay,
      },
      selfDefineKdtIndex: selfDefineKdtIndex ? JSON.parse(selfDefineKdtIndex) : [],
      ...rest,
    };

    if (checkBranchStore(shopInfo)) {
      params = {
        ...params,
        kdtIdList: [kdtId],
      };
    }

    const result = await new ReportService(ctx).getBatchReportData(params);
    return ctx.json(0, 'ok', result);
  }

  async getBatchResponse() {
    const ctx = this.ctx;

    const result = await new AsyncResponseService(ctx).getBatchResponse({
      responseId: ctx.query.responseId,
    });
    return ctx.json(0, 'ok', result);
  }

  async getDimensionAndIndexMeta() {
    const ctx = this.ctx;

    const shopInfo = ctx.getState('shopInfo');
    const { rootKdtId } = shopInfo || {};

    let params = {
      userId: ctx.userId.toString(),
      fromApp: 'wsc-pc-v4',
      sessionKdtId: ctx.kdtId,
      hqKdtId: rootKdtId || ctx.kdtId,
      kdtId: ctx.kdtId,
      reportTemplateId: 2,
      dateRange: 1,
      dateType: 1,
      periodType: 2,
      summaryType: 2,
      dimensionFilterList: [],
      indexIdList: [],
      dimensionIdList: [],
      ...ctx.query,
    };

    if (checkRetailShop(shopInfo)) {
      params.reportTemplateId = 18; // 零售报表模板ID
    }

    if (checkBranchStore(shopInfo)) {
      params = {
        ...params,
        kdtIdList: [ctx.kdtId],
        dimensionIdList: [426],
      };
    }

    const result = await new SelfReportService(ctx).getMangeReportQueryIndexAndDimensionMeta(
      params
    );
    return ctx.json(0, 'ok', result);
  }

  async queryConfigById() {
    const ctx = this.ctx;

    const result = await new AgentReportConfigService(ctx).queryConfigById(+ctx.query.id);
    return ctx.json(0, 'ok', result);
  }

  async createConfig() {
    const ctx = this.ctx;

    const params = {
      kdtId: ctx.kdtId,
      adminId: ctx.userId,
      source: 'wsc-pc-v4',
      ...ctx.request.body,
    };

    const result = await new AgentReportConfigService(ctx).createConfig(params);
    return ctx.json(0, 'ok', result);
  }

  async updateConfig() {
    const ctx = this.ctx;

    const params = {
      kdtId: ctx.kdtId,
      adminId: ctx.userId,
      source: 'wsc-pc-v4',
      ...ctx.request.body,
    };

    const result = await new AgentReportConfigService(ctx).updateConfig(params);
    return ctx.json(0, 'ok', result);
  }
}

module.exports = ReportController;
