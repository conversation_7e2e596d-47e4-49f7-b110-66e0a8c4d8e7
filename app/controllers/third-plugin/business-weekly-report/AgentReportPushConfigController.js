const BaseController = require('../../base/BaseController');

const AgentReportPushConfigService = require('../../../services/third-plugin/business-weekly-report/AgentReportPushConfigService');
const StaffOpenQueryService = require('../../../services/third-plugin/business-weekly-report/StaffOpenQueryService');
const ChainManageService = require('../../../services/api/staff/ChainManageService');
const SingleStaffService = require('../../../services/api/staff/SingleStaffService');
const { SHOP_MODE } = require('../../../constants');
const { checkChainStore, checkPartnerStore, checkHqStore } = require('@youzan/utils-shop');

class AgentReportPushConfigController extends BaseController {
  constructor(ctx) {
    super(ctx);
  }

  async createConfig() {
    const ctx = this.ctx;
    const kdtId = ctx.kdtId;
    const body = ctx.request.body;

    const params = {
      ...body,
      kdtId,
      retailSource: 'wsc-pc-v4',
      adminId: ctx.userId,
    };

    const result = await new AgentReportPushConfigService(ctx).createConfig(params);
    return ctx.json(0, 'ok', result);
  }

  async updateConfig() {
    const ctx = this.ctx;
    const kdtId = ctx.kdtId;
    const body = ctx.request.body;

    const params = {
      ...body,
      kdtId,
      retailSource: 'wsc-pc-v4',
      adminId: ctx.userId,
    };

    const result = await new AgentReportPushConfigService(ctx).updateConfig(params);
    return ctx.json(0, 'ok', result);
  }

  async queryConfigByReportId() {
    const ctx = this.ctx;
    const kdtId = ctx.kdtId;
    const { reportId } = ctx.query;

    const params = {
      reportId,
      kdtId,
      retailSource: 'wsc-pc-v4',
      adminId: ctx.userId,
    };

    const result = await new AgentReportPushConfigService(ctx).queryConfigByReportId(params);
    return ctx.json(0, 'ok', result);
  }

  /**
   * 获取企业微信通讯录列表
   * @returns
   * @example {
   *    items: [
   *      {name: 'John', id: '123456'}
   *    ],
   *    paginator: {
   *      page: 1,
   *      pageSize: 10,
   *      totalCount: 43
   *    }
   */
  async getWecomContactList() {
    const ctx = this.ctx;
    const kdtId = ctx.kdtId;
    const { page = 1, pageSize = 200 } = ctx.query;

    const { nickName, id } = ctx.getLocalSession('userInfo');
    const operator = {
      appName: 'wsc-pc-v4',
      yzUserId: id,
      clientIp: ctx.firstXff,
      nickName,
    };
    const pageRequest = {
      countEnabled: true,
      pageNumber: +page,
      pageSize: +pageSize,
    };

    const params = {
      operator,
      pageRequest,
      status: 0, // 状态为正常
      wscKdtId: kdtId,
    };
    const result = await new StaffOpenQueryService(ctx).search(params);
    const { content, total } = result;
    const items = content.map(item => ({
      id: item.staffId,
      name: item.staffName,
    }));
    const paginator = {
      totalCount: total,
      page: +page,
      pageSize: +pageSize,
    };
    return ctx.json(0, 'ok', { items, paginator });
  }

  /**
   * 获取当前店铺的类型标识
   * @returns {string}
   * @private
   */
  _getMode() {
    const { ctx } = this;
    const shopInfo = ctx.getState('shopInfo');
    if (checkPartnerStore(shopInfo)) {
      return SHOP_MODE.PARTNER_SHOP;
    }
    if (checkHqStore(shopInfo)) {
      return SHOP_MODE.CHAIN_SHOP;
    }
    return SHOP_MODE.SELF_SHOP;
  }

  /**
   * 获取当前店铺员工列表
   * @returns
   * @example {
   *    items: [
   *      {name: 'John', id: '123456'}
   *    ],
   *    paginator: {
   *      page: 1,
   *      pageSize: 10,
   *      totalCount: 43
   *    }
   */
  async getStaffList() {
    const ctx = this.ctx;
    const kdtId = ctx.kdtId;
    const { page = 1, pageSize = 200, targetKdtId, roleId } = ctx.query;

    const { id: userId } = ctx.getLocalSession('userInfo');
    const shopInfo = ctx.getState('shopInfo');
    // 是否为连锁
    const isChainStore = checkChainStore(shopInfo);
    if (isChainStore) {
      const params = {
        kdtId: targetKdtId || kdtId,
        operatorId: userId,
        mode: targetKdtId ? SHOP_MODE.SELF_SHOP : this._getMode(),
        pageNo: page,
        pageSize,
        statusList: [0], // 启用的员工
      };
      if (roleId) {
        params.roleIds = [+roleId];
      }
      const res = await new ChainManageService(ctx).findForManage(params);

      const { items, total } = res;
      const itemsFormat = items.map(vo => {
        return {
          name: vo.staffBaseInfoResultDTO?.name || '',
          id: vo.staffBaseInfoResultDTO?.adminId || '',
        };
      });
      const paginator = {
        totalCount: total,
        page: +page,
        pageSize: +pageSize,
      };
      return ctx.json(0, 'ok', { items: itemsFormat, paginator });
    }
    const params = {
      operatorId: userId,
      kdtId,
      pageNo: page,
      pageSize,
      status: 'ON',
      needExtra: { offlineInfo: true },
    };
    if (roleId) {
      params.roleIds = [+roleId];
    }
    const res = await new SingleStaffService(ctx).find(params);
    const { items, paginator } = res;
    const itemsFormat = items.map(item => {
      return {
        name: item.name,
        id: item.adminId,
      };
    });
    return ctx.json(0, 'ok', { items: itemsFormat, paginator });
  }
}

module.exports = AgentReportPushConfigController;
