const BaseController = require('../../base/BaseController');
const ContentPlanService = require('../../../services/third-plugin/xhs-note/ContentPlanService');
const CategoryReadService = require('../../../services/third-plugin/xhs-note/CategoryReadService');
const SingleStaffService = require('../../../services/third-plugin/xhs-note/SingleStaffService');
const ContentPlanItemService = require('../../../services/third-plugin/xhs-note/ContentPlanItemService');
const CorpCoreKVService = require('../../../services/api/common/CorpCoreKVService');
const StaffManageService = require('../../../services/api/xd/StaffManageService');
const XiaohongshuChannelConsoleService = require('../../../services/third-plugin/xhs-note/XiaohongshuChannelConsoleService');
const HotNewsService = require('../../../services/third-plugin/xhs-note/HotNewsService');
const ChannelReferenceAccountService = require('../../../services/third-plugin/xhs-note/ChannelReferenceAccountService');
const ContentAccountExampleNoteService = require('../../../services/third-plugin/xhs-note/ContentAccountExampleNoteService');
const ContentXhsApiService = require('../../../services/third-plugin/xhs-note/ContentXhsApiService');
class XhsNotePlanController extends BaseController {
  get kdtIdAndRootKdtId() {
    const kdtId = this.ctx.kdtId;
    const rootKdtId = this.getHqKdtId() || kdtId;

    return { kdtId, rootKdtId };
  }
  injectKdtId(data = {}) {
    const { kdtIdAndRootKdtId } = this;
    return Object.assign({}, data, kdtIdAndRootKdtId);
  }

  async tryCatch(fn) {
    try {
      const result = await fn();
      return result;
    } catch (e) {
      console.log('e', e);
      return this.ctx.json(e.code || 12345, 'fail', { msg: e.msg || '系统异常，请稍后重试！' });
    }
  }

  async getWecomKdtId() {
    const { kdtId } = this.ctx;
    let wecomKdtId = 0;
    try {
      wecomKdtId = await new CorpCoreKVService(this.ctx).getWecomKdtIdByWechatMallId({
        wechatKdtId: kdtId,
      });
    } catch (e) {}
    return wecomKdtId;
  }

  async getPlanList() {
    const { ctx } = this;
    const queryData = this.ctx.getQueryData();
    const { accountId, status } = queryData;
    const params = this.injectKdtId({ accountId: +accountId });
    if (status) {
      params.status = +status;
    }
    const result = await new ContentPlanService(ctx).queryPlans(params).catch(() => []);
    return this.ctx.json(0, 'ok', result);
  }

  async changePlanState() {
    const { ctx } = this;
    const queryData = this.ctx.getPostData();
    const { planId, state, accountId, planType } = queryData;
    const params = this.injectKdtId({
      planId: +planId,
      state,
      accountId: +accountId,
      planType: +planType,
    });
    return this.tryCatch(async () => {
      const result = await new ContentPlanService(ctx).changePlanState(params);
      this.ctx.json(0, 'ok', result);
    });
  }

  async modifyPlanConfig() {
    const { ctx } = this;
    const queryData = this.ctx.getPostData();
    const { planId, accountId, planType, config = {}, ...rest } = queryData;
    const { materialConfig = {}, publishTime = {}, styleConfig = {}, ...otherConfig } = config;
    const { custom, style } = styleConfig;
    const { chooseType, groupIds, materials = [] } = materialConfig;
    const { publishType, pushTime } = publishTime;
    const params = this.injectKdtId({
      planId: +planId,
      accountId: +accountId,
      planType: +planType,
      config: {
        materialConfig: {
          chooseType: +chooseType,
          groupIds,
          materials,
        },
        publishTime: {
          publishType: +publishType,
          pushTime,
        },
        styleConfig: {
          custom: custom === 'true' ? true : false,
          style,
        },
        ...otherConfig,
      },
      ...rest,
    });
    return this.tryCatch(async () => {
      const result = await new ContentPlanService(ctx).modifyPlanConfig(params);
      this.ctx.json(0, 'ok', result);
    });
  }
  async queryCategoryList(ctx) {
    const params = {
      mediaType: 1,
      partnerBizType: 1,
      partnerBizId: ctx.kdtId,
    };

    const result = await new CategoryReadService(ctx).queryCategoryList(params);
    const compatResult = (result || []).map(item => {
      return {
        count: item.count,
        createTime: parseInt(item.createdAt / 1000, 10),
        id: item.categoryId,
        kdtId: ctx.kdtId,
        mediaType: item.mediaType,
        name: item.categoryName,
        type: item.categoryType,
        updateTime: parseInt(item.updatedAt / 1000, 10),
      };
    });
    return this.ctx.json(0, 'ok', compatResult);
  }

  async findStaff() {
    const { ctx } = this;
    const { page: pageNo, pageSize, keyword, needExtra, adminId } = ctx.validator({
      page: ctx.joi.number().integer(),
      pageSize: ctx.joi.number().integer(),
      keyword: ctx.joi.string(),
      needExtra: ctx.joi.object({
        offlineInfo: ctx.joi.boolean(),
      }),
      adminId: ctx.joi.number().integer(),
    });
    const param = {
      kdtId: +ctx.kdtId,
      pageNo,
      pageSize,
      keyword,
      status: 'ON',
      adminId,
      needExtra,
      roleIds: [1, 8],
    };

    const result = await new SingleStaffService(ctx).find(param);
    return this.ctx.json(0, 'ok', result);
  }

  async nextCreationPlanItem() {
    const { ctx } = this;
    const queryData = this.ctx.getQueryData();
    const { accountId, planType } = queryData;
    const params = this.injectKdtId({ accountId: +accountId, planType: +planType });
    const result = await new ContentPlanItemService(ctx).nextCreationPlanItem(params);
    return this.ctx.json(0, 'ok', result);
  }

  async queryPlanItems() {
    const { ctx } = this;
    const queryData = this.ctx.getQueryData();
    const { accountIds = '[]', planType, status, page, pageSize, offset, ...rest } = queryData;
    const params = this.injectKdtId({
      ...rest,
      accountIds: JSON.parse(accountIds),
    });

    if (planType) {
      params.planType = +planType;
    }

    if (status) {
      params.status = +status;
    }

    if (page !== undefined) {
      params.page = +page;
    }

    if (offset) {
      params.offset = +offset;
    }

    if (pageSize !== undefined) {
      params.pageSize = +pageSize;
    }
    const result = await new ContentPlanItemService(ctx).queryPlanItems(params);
    return this.ctx.json(0, 'ok', result);
  }

  async getPlanItem() {
    const { ctx } = this;
    const queryData = this.ctx.getQueryData();
    const { planItemId, accountId, planType } = queryData;
    const params = this.injectKdtId({
      planItemId: +planItemId,
      accountId: +accountId,
      planType: +planType,
    });
    const result = await new ContentPlanItemService(ctx).getPlanItem(params);
    return this.ctx.json(0, 'ok', result);
  }

  async deletePlanItem() {
    const { ctx } = this;
    const queryData = this.ctx.getPostData();
    const { accountId, planItemId, planType } = queryData;
    const params = this.injectKdtId({
      accountId: +accountId,
      planItemId: +planItemId,
      planType: +planType,
    });
    return this.tryCatch(async () => {
      const result = await new ContentPlanItemService(ctx).deletePlanItem(params);
      this.ctx.json(0, 'ok', result);
    });
  }

  async getWecomStaffList() {
    const { ctx } = this;
    const queryData = this.ctx.getQueryData();
    const userInfo = ctx.getLocalSession('userInfo');

    const { pageNum = 1, pageSize = 100, searchName = '' } = queryData;
    const wecomKdtId = await this.getWecomKdtId();
    if (!wecomKdtId) {
      throw new Error('未找到企微kdtId');
    } else {
      const params = {
        operator: {
          yzUserId: userInfo.userId,
          nickname: userInfo.nickName,
          appName: 'wsc-pc-v4',
        },
        pageRequest: {
          pageNumber: Number(pageNum),
          pageSize: Number(pageSize),
        },
        staffMobileOrName: searchName,
        status: 0,
        yzKdtId: wecomKdtId,
      };
      const ret = await new StaffManageService(ctx).search(params);
      ctx.json(0, 'success', ret);
    }
  }

  /** 获取企微工作状态 */
  async getWechatWorkStatus(ctx) {
    const wecomKdtId = await this.getWecomKdtId();
    ctx.json(0, 'success', { status: wecomKdtId ? 1 : 0 });
  }

  async auditPlanItem() {
    const { ctx } = this;
    const queryData = this.ctx.getPostData();
    const params = this.injectKdtId({ ...queryData });
    return this.tryCatch(async () => {
      const result = await new ContentPlanItemService(ctx).auditPlanItem(params);
      this.ctx.json(0, 'ok', result);
    });
  }

  async modifyPlanItem() {
    const { ctx } = this;
    const queryData = this.ctx.getPostData();
    const { accountId, planType } = queryData;
    const params = this.injectKdtId({ ...queryData, accountId: +accountId, planType: +planType });
    return this.tryCatch(async () => {
      const result = await new ContentPlanItemService(ctx).modifyPlanItem(params);
      this.ctx.json(0, 'ok', result);
    });
  }

  async listChannelPaged() {
    const { ctx } = this;
    const { title, ...queryData } = this.ctx.getQueryData();
    const params = this.injectKdtId({ ...queryData, channelCanSale: 1 });
    if (title) {
      params.title = title;
    }
    const result = await new XiaohongshuChannelConsoleService(ctx).listChannelPaged(params);
    return this.ctx.json(0, 'ok', result);
  }

  async listChannelItem() {
    const { ctx } = this;
    const { itemIds = '[]' } = this.ctx.getQueryData();
    const params = this.injectKdtId({ itemIds: JSON.parse(itemIds) });
    const result = await new XiaohongshuChannelConsoleService(ctx).listChannelItem(params);
    return this.ctx.json(0, 'ok', result);
  }

  async getHotNewsInfos() {
    const { ctx } = this;
    const { planId, dateStr } = this.ctx.getQueryData();
    const params = this.injectKdtId({ planId: +planId, dateStr: +dateStr });
    const result = await new HotNewsService(ctx).getHotNewsInfos(params);
    return this.ctx.json(0, 'ok', result);
  }

  async relationReferenceAccount() {
    const { ctx } = this;
    const {
      channel,
      operationAccountId,
      referenceAccountId,
      ...queryData
    } = this.ctx.getPostData();
    const params = this.injectKdtId({
      ...queryData,
      channel: +channel,
      operationAccountId: +operationAccountId,
    });
    if (referenceAccountId) {
      params.referenceAccountId = +referenceAccountId;
    }
    return this.tryCatch(async () => {
      const result = await new ChannelReferenceAccountService(ctx).relationReferenceAccount(params);
      this.ctx.json(0, 'ok', result);
    });
  }

  async removeReferenceAccount() {
    const { ctx } = this;
    const { operationAccountId, referenceAccountId } = this.ctx.getPostData();
    const params = this.injectKdtId({
      operationAccountId: +operationAccountId,
      referenceAccountId: +referenceAccountId,
    });
    return this.tryCatch(async () => {
      const result = await new ChannelReferenceAccountService(ctx).removeReferenceAccount(params);
      this.ctx.json(0, 'ok', result);
    });
  }

  async queryReferenceAccount() {
    const { ctx } = this;
    const { operationAccountId } = this.ctx.getQueryData();
    const params = this.injectKdtId({ operationAccountId: +operationAccountId });
    return this.tryCatch(async () => {
      const result = await new ChannelReferenceAccountService(ctx).queryReferenceAccount(params);
      this.ctx.json(0, 'ok', result);
    });
  }

  async addAccountExampleNote() {
    const { ctx } = this;
    const { channel, accountId, ...queryData } = this.ctx.getPostData();
    const params = this.injectKdtId({ ...queryData, channel: +channel, accountId: +accountId });
    return this.tryCatch(async () => {
      const result = await new ContentAccountExampleNoteService(ctx).addAccountExampleNote(params);
      this.ctx.json(0, 'ok', result);
    });
  }

  async cancelRelationExampleNote() {
    const { ctx } = this;
    const { accountId, exampleNoteId } = this.ctx.getPostData();
    const params = this.injectKdtId({ accountId: +accountId, exampleNoteId: +exampleNoteId });
    return this.tryCatch(async () => {
      const result = await new ContentAccountExampleNoteService(ctx).cancelRelationExampleNote(
        params
      );
      this.ctx.json(0, 'ok', result);
    });
  }

  async getAccountExampleNoteInfo() {
    const { ctx } = this;
    const { channel, accountId } = this.ctx.getQueryData();
    const params = this.injectKdtId({ channel: +channel, accountId: +accountId });
    return this.tryCatch(async () => {
      const result = await new ContentAccountExampleNoteService(ctx).getAccountExampleNoteInfo(
        params
      );
      this.ctx.json(0, 'ok', result);
    });
  }

  async searchTopicWithKeywordForApi() {
    const { ctx } = this;
    const queryData = this.ctx.getQueryData();
    const params = this.injectKdtId({ ...queryData });
    const result = await new ContentXhsApiService(ctx).searchTopicWithKeywordForApi(params);
    this.ctx.json(0, 'ok', result);
  }

  async useHotCreation() {
    const { ctx } = this;
    const { accountId, planType, planId, hotId } = this.ctx.getQueryData();
    const params = this.injectKdtId({
      accountId: +accountId,
      planType: +planType,
      planId: +planId,
      hotId: +hotId,
    });
    return this.tryCatch(async () => {
      const result = await new ContentPlanItemService(ctx).useHotCreation(params);
      this.ctx.json(0, 'ok', result);
    });
  }
}

module.exports = XhsNotePlanController;
