const BaseController = require('../../base/BaseController');
const XhsAppAccountService = require('../../../services/third-plugin/xhs-note/XhsAppAccountService');
const XhsPoiService = require('../../../services/third-plugin/xhs-note/XhsPoiService');
const XhsMiniAppService = require('../../../services/third-plugin/xhs-note/XhsMiniAppService');

class XhsNoteAccountController extends BaseController {
  get kdtIdAndRootKdtId() {
    const kdtId = this.ctx.kdtId;
    const rootKdtId = this.getHqKdtId() || kdtId;

    return { kdtId, rootKdtId };
  }
  injectKdtId(data = {}) {
    const { kdtIdAndRootKdtId } = this;
    return Object.assign({}, data, kdtIdAndRootKdtId);
  }

  async getAccountList() {
    const { ctx } = this;
    const queryData = this.ctx.getQueryData();
    const { channel } = queryData;
    const params = this.injectKdtId({ ...queryData, channel: +channel });
    const result = await new XhsAppAccountService(ctx).queryAccount(params).catch(() => []);
    return this.ctx.json(0, 'ok', result);
  }

  async saveAccount() {
    const postData = this.ctx.getPostData();
    const { accountType, notePublishType, channel, accountId } = postData;
    const params = this.injectKdtId({
      ...postData,
      channel: +channel,
      accountType: +accountType,
      notePublishType: +notePublishType,
    });
    if (accountId) {
      params.accountId = +accountId;
    }
    try {
      const result = await new XhsAppAccountService(this.ctx).saveAccount(params);
      return this.ctx.json(0, 'ok', result);
    } catch (e) {
      return this.ctx.json(e.code || 12345, 'fail', { msg: e.msg || '系统异常，请稍后重试！'});
    }
  }

  // 删除账号
  async deleteAccount() {
    const postData = this.ctx.getPostData();
    const { channel, accountId } = postData;
    const params = this.injectKdtId({ ...postData, channel: +channel, accountId: +accountId });
    try {
      const result = await new XhsAppAccountService(this.ctx).deleteAccount(params);
      return this.ctx.json(0, 'ok', result);
    } catch (e) {
      return this.ctx.json(e.code || 12345, 'fail', { msg: e.msg || '系统异常，请稍后重试！' });
    }
  }

  // 查询账号详情
  async getAccount() {
    const queryData = this.ctx.getQueryData();
    const { accountId } = queryData;
    const params = this.injectKdtId({ accountId: +accountId, isQueryData: true });
    const result = await new XhsAppAccountService(this.ctx).getAccount(params);
    return this.ctx.json(0, 'ok', result);
  }

  // 查询账号详情
  async queryAccountTotalData() {
    const queryData = this.ctx.getQueryData();
    const { channel } = queryData;
    const params = this.injectKdtId({ ...queryData, channel: +channel });
    const result = await new XhsAppAccountService(this.ctx).queryAccountTotalData(params);
    return this.ctx.json(0, 'ok', result || {});
  }

  async getAccountLimit() {
    const { ctx } = this;
    const { kdtId } = ctx;
    let limit = {};
    try {
      const config = await ctx.apolloClient.getConfig({
        appId: 'wsc-pc-channel',
        namespace: `wsc-pc-channel.whitelist`,
        key: `ai-xhs-account`,
      });

      if (typeof config === 'object') {
        limit = config;
      }
    } catch (error) {}

    return this.ctx.json(0, 'ok', +limit[kdtId] || 10);
  }

  // 查询行业列表
  async queryIndustryTypeList() {
    const result = await new XhsAppAccountService(this.ctx).queryIndustryTypeList();
    return this.ctx.json(0, 'ok', result);
  }

  // 查询账号绑定小程序等信息
  async queryAccountAppletAndPoi() {
    const queryData = this.ctx.getQueryData();
    const { channel } = queryData;
    const params = this.injectKdtId({ ...queryData, channel: +channel });
    const result = await new XhsPoiService(this.ctx).queryAccountAppletAndPoi(params);
    return this.ctx.json(0, 'ok', result);
  }
  // 查询账号绑定小程序等信息
  async queryMiniApp() {
    const queryData = this.ctx.getQueryData();
    const { channel } = queryData;
    const params = this.injectKdtId({ ...queryData, channel: +channel });
    const result = await new XhsMiniAppService(this.ctx).queryMiniApp(params);
    return this.ctx.json(0, 'ok', result);
  }
}

module.exports = XhsNoteAccountController;
