const { ShopAbilityUtil } = require('@youzan/plugin-shop-ability');
const uuid = require('@youzan/utils/string/makeRandomString');

const BaseController = require('../../base/BaseController');
const AgentTaskApiService = require('../../../services/third-plugin/salesman/AgentTaskApiService');
const AgentActivityApiService = require('../../../services/third-plugin/salesman/AgentActivityApiService');
const ActivityWriteFacadeService = require('../../../services/third-plugin/salesman/ActivityWriteFacadeService');
const SnapshotService = require('../../../services/third-plugin/salesman/SnapshotService');
const ExportV3WebApiService = require('../../../services/third-plugin/salesman/ExportV3WebApiService');
const CouponV3WebApiService = require('../../../services/third-plugin/salesman/CouponV3WebApiService');
const CouponsApiService = require('../../../services/third-plugin/salesman/CouponsApiService');
const SalesmanTaskWebApiService = require('../../../services/third-plugin/salesman/SalesmanTaskWebApiService');
const BehaviorCaptchaService = require('../../../services/third-plugin/salesman/BehaviorCaptchaService');
const DistributionModeWebApiService = require('../../../services/third-plugin/salesman/DistributionModeWebApiService');
const TaskVerifyService = require('../../../services/third-plugin/salesman/TaskVerifyService');
const AgentApiService = require('../../../services/third-plugin/salesman/AgentApiService');

function generateRandomRecruitInfo(data, sum, part) {
  const randomIndex = arr => Math.floor(Math.random() * arr.length);

  const randomThinking = data.thinkings[randomIndex(data.thinkings)]
    .replace('{sum}', sum)
    .replace('{part}', part);
  const randomActivityTitle = data.activityTitles[randomIndex(data.activityTitles)];
  const randomTaskTitle = data.taskTitles[randomIndex(data.taskTitles)];
  const defaultThinking = data.defaultThinking.replace('{sum}', sum);

  return {
    thinking: randomThinking,
    activityTitle: randomActivityTitle,
    taskTitle: randomTaskTitle,
    defaultThinking,
  };
}

class SalesmanRecruitAgentController extends BaseController {
  filterNumber(val) {
    const numberVal = +val;
    return isNaN(numberVal) ? null : numberVal;
  }
  getOperator(ctx) {
    const { id: operatorId, nickName: operatorName, mobile: operatorPhone } = ctx.getLocalSession(
      'userInfo'
    );
    return {
      operatorId,
      operatorName,
      operatorPhone: typeof operatorPhone === 'string' ? operatorPhone : null,
      // operatorBuyerId: operatorId,
      operatorKdtId: ctx.kdtId,
    };
  }
  // 查询店铺能力
  async getShopAbility() {
    const { ctx } = this;
    const [advanceValid, baseValid] = await Promise.all([
      new ShopAbilityUtil(ctx).checkAbilityValid({
        kdtId: ctx.kdtId,
        keys: ['salesman_advance_ability'],
        mode: 'some',
      }),
      new ShopAbilityUtil(ctx).checkAbilityValid({
        kdtId: ctx.kdtId,
        keys: ['salesman_base_ability'],
        mode: 'some',
      }),
    ]);
    return ctx.json(0, 'ok', { advanceValid, baseValid });
  }

  async getSalesmanName() {
    const result = await this.getGetJson();
    const { salesmanName } = result;
    return this.ctx.json(0, 'ok', { salesmanName });
  }

  /** @description 查询店铺分销模式 */
  async getGetJson() {
    const { ctx } = this;
    const distributionModeGetWebDTO = {
      operator: {
        operatorSource: 'wsc-pc-salesman',
        operatorClientIp: ctx.firstXff,
        ...this.getOperator(ctx),
      },
      kdtId: ctx.kdtId,
    };

    const result = await new DistributionModeWebApiService(ctx).get(distributionModeGetWebDTO);
    return result;
  }
  async getCoreDatas() {
    const { ctx } = this;
    const { kdtId } = ctx;
    const { agentType = 1 } = ctx.getQueryData();
    const result = await new AgentApiService(ctx).getAgentData({
      agentType: this.filterNumber(agentType),
      kdtId,
    });
    ctx.json(0, 'ok', result);
  }

  // 查询apollo配置的思考过程、活动名称、奖励名称
  async getApolloCoT() {
    const { ctx } = this;
    const [jsonStr, customerSum, customerPart] = await Promise.all([
      ctx.apolloClient.getConfig({
        appId: 'wsc-pc-salesman',
        namespace: 'wsc-pc-salesman.config',
        key: 'recruit_agent_CoT',
      }),
      new AgentTaskApiService(ctx)
        .getRecruitCrowdSearchCount({
          kdtId: ctx.kdtId,
          crowdFilterMode: 1, // 按宽口径筛选人群
        })
        .catch(() => 0),
      new AgentTaskApiService(ctx)
        .getRecruitCrowdSearchCount({
          kdtId: ctx.kdtId,
          crowdFilterMode: 2, // 按宽口径筛选人群
        })
        .catch(() => 0),
    ]);
    const result = generateRandomRecruitInfo(jsonStr, customerSum, customerPart);
    ctx.json(0, 'ok', {
      ...result,
      thinking: customerSum === customerPart ? result.defaultThinking : result.thinking,
    });
  }

  async listActivityPaged() {
    const { ctx } = this;
    const { kdtId } = ctx;
    const { page = 1, pageSize = 20, agentType = 1 } = ctx.getQueryData();
    const params = {
      kdtId,
      page,
      pageSize,
      agentType,
    };
    const result = await new AgentActivityApiService(ctx).listActivityPaged(params);
    ctx.json(0, 'ok', result);
  }

  async getPosterJson() {
    const { ctx } = this;
    const url = `https://h5.youzan.com/wscump/salesman/tutorial?kdt_id=${ctx.kdtId}`;
    const qrCode = await this.ctx.qrcode.create(
      url,
      {
        size: 200,
      },
      'base64'
    );
    ctx.setState('qrCode', qrCode);
    const html = await ctx.renderView(`salesman-agent/recruit-poster.html`);
    let snapshot;

    try {
      snapshot = await new SnapshotService(ctx).postSnapshot({
        html,
        operatorId: ctx.userId,
        height: 667,
        width: 375,
        alwaysCdn: 1,
        quality: 100,
      });
    } catch (err) {}
    return snapshot.img || '';
  }

  async getPoster() {
    const { ctx } = this;
    const recruitUrl = `https://h5.youzan.com/wscump/salesman/tutorial?kdt_id=${ctx.kdtId}`;
    const url = await this.ctx.shortUrl.toShort(recruitUrl);
    return ctx.json(0, 'ok', { posterUrl: url });
  }

  async getRecruitActivityInitialInfo() {
    const { ctx } = this;
    const recruitUrl = `https://h5.youzan.com/wscump/salesman/tutorial?kdt_id=${ctx.kdtId}`;
    const [
      customerMax,
      customerMin,
      startTime,
      setting,
      posterUrl,
      recruitShortUrl,
    ] = await Promise.all([
      new AgentTaskApiService(ctx)
        .getRecruitCrowdSearchCount({
          kdtId: ctx.kdtId,
          crowdFilterMode: 1, // 按宽口径筛选人群
        })
        .catch(() => 0),
      new AgentTaskApiService(ctx)
        .getRecruitCrowdSearchCount({
          kdtId: ctx.kdtId,
          crowdFilterMode: 2, // 按宽口径筛选人群
        })
        .catch(() => 0),
      new AgentActivityApiService(ctx)
        .getActivityStartTime({
          kdtId: ctx.kdtId,
          activityType: 1, // 分销员招募
        })
        .catch(() => 0),
      this.getGetJson(),
      this.getPosterJson(),
      this.ctx.shortUrl.toShort(recruitUrl),
    ]);

    const { salesmanName } = setting;

    return ctx.json(0, 'ok', {
      customerMax,
      customerMin,
      startTime,
      posterUrl,
      salesmanName,
      recruitShortUrl,
    });
  }

  async invalidateActivity() {
    const { ctx } = this;
    const { activityId } = ctx.getPostData();
    this.validator.required(activityId, '活动id获取异常');

    const result = await new AgentActivityApiService(ctx).invalidateActivity({
      kdtId: ctx.kdtId,
      activityId,
      operator: {
        ...this.getOperator(ctx),
        // staffId: ctx.firstXff,
      },
    });
    ctx.json(0, 'ok', result);
  }

  async deleteActivity() {
    const { ctx } = this;
    const { activityId } = ctx.getPostData();
    this.validator.required(activityId, '活动id获取异常');

    const result = await new AgentActivityApiService(ctx).deleteActivity({
      kdtId: ctx.kdtId,
      activityId: this.filterNumber(activityId),
      operator: {
        ...this.getOperator(ctx),
        // staffId: ctx.firstXff,
      },
    });
    ctx.json(0, 'ok', result);
  }

  async createActivity() {
    const { ctx } = this;
    const {
      title,
      taskAwardId,
      activityType = '1', // 招募
      couponId,
      startTime,
      endTime,
      msgPushContent,
      crowdFilterMode,
      posterUrl,
      day,
      rateDescend,
      disturbDay = 3, // 免打扰天数
    } = ctx.getPostData();
    const createData = {
      kdtId: ctx.kdtId,
      title,
      taskAwardId: this.filterNumber(taskAwardId),
      activityType: this.filterNumber(activityType),
      couponId: this.filterNumber(couponId),
      startTime: this.filterNumber(startTime),
      endTime: this.filterNumber(endTime),
      msgPushContent,
      crowdFilterMode: this.filterNumber(crowdFilterMode),
      posterUrl,
      day: this.filterNumber(day),
      rateDescend: this.filterNumber(rateDescend),
      disturbDay,
    };

    const bizId = await new AgentActivityApiService(ctx).createActivity(createData);

    if (activityType === '1') {
      await new AgentTaskApiService(ctx).submitTask({
        kdtId: ctx.kdtId,
        bizId,
        activityType: this.filterNumber(activityType),
      });
    }

    return ctx.json(0, 'ok', bizId);
  }

  async getActivityDetail() {
    const { ctx } = this;
    const { activityId } = ctx.getQueryData();
    const result = await new AgentActivityApiService(ctx).getActivityDetail({
      kdtId: ctx.kdtId,
      activityId,
    });
    ctx.json(0, 'ok', result);
  }

  async updateCouponShareType(params) {
    const { ctx } = this;
    const { kdtId } = ctx;
    const result = await new CouponsApiService(ctx).saveCouponShareConfig({
      ...params,
      kdtId,
    });
    ctx.json(0, 'ok', result);
  }

  async saveCoupons(couponId) {
    const { ctx } = this;
    const { kdtId } = ctx;
    const param = {
      couponIds: [couponId],
      kdtId,
      operator: this.getOperator(ctx),
    };
    const result = await new CouponV3WebApiService(ctx).saveCoupons(param);
    ctx.json(0, 'ok', result);
  }

  async setSalesmanCouponSetting(prams) {
    const { ctx } = this;
    const { kdtId } = ctx;
    const { couponId, couponName } = prams;
    const setting = await new CouponV3WebApiService(ctx).getCouponSetting(kdtId);
    // 如果开启了优惠券绑客 并且设置了展示指定优惠券
    if (setting.allowCouponsBindRelation && setting.configCouponsBind) {
      try {
        await this.saveCoupons(couponId);
        await this.updateCouponShareType({
          couponId,
          couponName,
          shareScope: 1,
          shareScopeType: 1,
          shareType: 1,
          relationIds: [],
          notCheckRelationId: true,
        });
        ctx.json(0, 'ok', true);
      } catch (err) {
        throw new Error(err.msg || '设置优惠券失败');
      }
    } else {
      ctx.json(0, 'ok', true);
    }
  }
  async createCardActivity(ctx) {
    const {
      kdtId,
      userId,
      request: { body },
    } = ctx;
    const userInfo = ctx.getLocalSession('userInfo');
    body.kdtId = kdtId;
    body.userId = userId;
    body.operatorId = userId;
    body.operatorName = userInfo.nickName;
    body.operatorType = 1;
    body.requestId = uuid(18);
    const data = await new ActivityWriteFacadeService(ctx).createCardActivity(body);
    await this.setSalesmanCouponSetting({
      couponId: data,
      couponName: body.title,
    });
    ctx.json(0, 'ok', data);
  }

  async createMessage(ctx) {}

  /** @description 创建或编辑任务 */
  async getCreateOrUpdateTaskJson() {
    const { ctx } = this;
    const { kdtId } = ctx;
    const {
      endTime,
      excludeGoodsIds,
      includeGoodsIds,
      joinLevels,
      limitJoin,
      startTime,
      settleTimeType,
      settleTime,
      taskBizType,
      taskAwardConfigList,
      title,
      verifyCode,
      goodsJoinConf,
      taskAwardForm,
      groupIds,
      taskOrigin,
      excludeProbation,
      taskType,
      recommendGoodsIds,
      salesContent,
      newSalesmanDefinition,
      notCheckNewSalesmanBuyerIds = true,
    } = ctx.getPostData();

    const params = {
      kdtId,
      endTime: this.filterNumber(endTime),
      excludeGoodsIds,
      includeGoodsIds,
      joinLevels,
      limitJoin: this.filterNumber(limitJoin),
      startTime: this.filterNumber(startTime),
      settleTime: this.filterNumber(settleTime),
      settleTimeType: this.filterNumber(settleTimeType),
      taskBizType: this.filterNumber(taskBizType),
      taskAwardConfigList,
      title,
      goodsJoinConf,
      operator: this.getOperator(ctx),
      verifyCode,
      hqKdtId: ctx.kdtId,
      taskAwardForm,
      groupIds,
      taskOrigin: this.filterNumber(taskOrigin),
      excludeProbation,
      taskType: this.filterNumber(taskType),
      recommendGoodsIds,
      salesContent,
      newSalesmanDefinition,
      notCheckNewSalesmanBuyerIds,
      notCheckVerifyCode: true,
    };

    const result = await new SalesmanTaskWebApiService(ctx).createOrUpdateTask(params);
    ctx.success(result);
  }

  async listExportRecordById() {
    const { ctx } = this;
    const {
      kdtId,
      request: { body },
    } = ctx;
    const { ids, bizType } = body;
    this.validator.required(ids, '任务id获取异常');
    const data = await new ExportV3WebApiService(ctx).listExportRecordById({
      kdtId,
      ids,
      bizType,
      taskKdtId: kdtId,
    });
    ctx.json(0, 'ok', data);
  }

  async exportSubmit() {
    const { ctx } = this;
    const { kdtId } = ctx;
    const { args, bizType } = ctx.getPostData();
    args.kdtId = kdtId;
    args.operator = this.getOperator(ctx);
    this.validator.required(bizType, 'bizType获取异常');
    const data = await new ExportV3WebApiService(ctx).exportSubmit({
      kdtId,
      args,
      bizType,
    });
    ctx.json(0, 'ok', data);
  }

  async getSetting(ctx) {
    const { kdtId } = ctx;
    const result = await new CouponV3WebApiService(ctx).getCouponSetting(kdtId);
    ctx.json(0, 'ok', result);
  }

  async openCouponSetting(ctx) {
    const { kdtId } = ctx;
    const {
      allowCouponsBindRelation,
      couponsNotDisplaySellerInfo,
      configCouponsBind,
      showAllCoupons,
    } = ctx.getPostData();
    const params = {
      kdtId,
      allowCouponsBindRelation,
      couponsNotDisplaySellerInfo,
      configCouponsBind,
      showAllCoupons,
      operator: this.getOperator(ctx),
    };
    const result = await new CouponV3WebApiService(ctx).saveOrUpdateCouponSetting(params);
    ctx.json(0, 'ok', result);
  }

  async checkBehaviorCaptcha() {
    const { ctx } = this;
    const { ticket } = ctx.getPostData();
    this.validator.required(ticket, '参数错误，ticket不能为空');
    let validResult = false;
    try {
      validResult = await new BehaviorCaptchaService(ctx).secondCheckBehaviorToken({
        token: ticket,
        ip: ctx.firstXff,
      });
    } catch (e) {
      // 二次校验服务只会返回 true/false，如果能进来这里，说明服务出问题了，为了不阻塞主流程，可以暂时放行
      validResult = true;
    }
    ctx.json(0, 'ok', validResult);
  }

  // 发送验证码
  async sendVerifyCode(ctx) {
    const { kdtId } = ctx;

    const params = {
      kdtId,
      operator: this.getOperator(ctx),
    };

    const result = await new TaskVerifyService(ctx).sendVerifyCode(params);
    ctx.json(0, 'ok', result);
  }
  // 校验验证码
  async checkVerifyCode(ctx) {
    const { kdtId } = ctx;
    const { verifyCode } = ctx.getPostData();

    const params = {
      kdtId,
      verifyCode,
      operator: this.getOperator(ctx),
    };

    const result = await new TaskVerifyService(ctx).verify(params);
    ctx.json(0, 'ok', result);
  }
}

module.exports = SalesmanRecruitAgentController;
