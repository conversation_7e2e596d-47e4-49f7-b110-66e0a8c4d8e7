const BaseController = require('../../base/BaseController');
const AgentTaskApiService = require('../../../services/third-plugin/salesman/AgentTaskApiService');
const AccountReadApiService = require('../../../services/third-plugin/salesman/AccountReadApiService');
const AgentActivityApiService = require('../../../services/third-plugin/salesman/AgentActivityApiService');
const FlowTempService = require('../../../services/flow/FlowTempService');
const FlowService = require('../../../services/flow/FLowService');
const DistributionModeWebApiService = require('../../../services/third-plugin/salesman/DistributionModeWebApiService');
const SnapshotService = require('../../../services/third-plugin/salesman/SnapshotService');
const merge = require('deepmerge');

const subDays = require('date-fns/sub_days');
const format = require('date-fns/format');

const isQA = process.env.NODE_ENV === 'qa';
const centerUrl = 'https://h5.youzan.com/wscump/salesman/center-v2/home';

function filterNumber(val) {
  const numberVal = +val;
  return isNaN(numberVal) ? null : numberVal;
}

function generateRandomRecruitInfo(data, num) {
  const randomIndex = arr => Math.floor(Math.random() * arr.length);

  const randomThinking = data.thinkings[randomIndex(data.thinkings)].replace('{num}', num);
  const randomActivityTitle = data.activityTitles[randomIndex(data.activityTitles)];
  const randomTaskTitle = data.taskTitles[randomIndex(data.taskTitles)];

  return {
    thinking: randomThinking,
    activityTitle: randomActivityTitle,
    taskTitle: randomTaskTitle,
  };
}

function getUpdateParams(postData) {
  const { activityType, activityId, activityTitle, endTime, startTime } = postData;
  // console.log(endTime, startTime);
  return {
    chainRules: [
      {
        action: {
          // 任务执行参数传值的位置（传值结构）
          actionFieldValueMaps4Execute: JSON.stringify({
            activityType: filterNumber(activityType),
            bizId: filterNumber(activityId),
          }),
          id: isQA ? 72 : 67, // 固定值
        },
      },
    ],
    description: activityTitle,
    name: activityTitle,
    triggerId: isQA ? 21 : 16, // 固定值
    triggerDefine: {
      // 要转 json 字符串
      subjectDetail: JSON.stringify({
        repeatRule: {
          cycleRule: 'week',
          endType: 1,
          endTime: format(new Date(filterNumber(endTime)), 'YYYY-MM-DD HH:mm'),
          startTime: format(new Date(filterNumber(startTime)), 'YYYY-MM-DD HH:mm'),
          // weekDays: [2],
        },
        timeZone: 'GMT+8',
      }),
    },
    type: 1,
  };
}

function handleTempWithPost(tempData, postData) {
  const updateParams = getUpdateParams(postData);
  // 自定义合并策略，用于替换数组
  const overwriteMerge = (destinationArray, sourceArray) => sourceArray;
  const submitData = merge(tempData, updateParams, { arrayMerge: overwriteMerge });
  return submitData;
}

class OperationAgentController extends BaseController {
  getOperator(ctx) {
    const { id: operatorId, nickName: operatorName, mobile: operatorPhone } = ctx.getLocalSession(
      'userInfo'
    );
    return {
      operatorId,
      operatorName,
      operatorPhone: typeof operatorPhone === 'string' ? operatorPhone : null,
      // operatorBuyerId: operatorId,
      operatorKdtId: ctx.kdtId,
    };
  }

  getActivityCrowd(params) {
    const { actType, day, rateDescend } = params;
    const { ctx } = this;
    const activityType = filterNumber(actType);

    // 并发获取最大限制数量和业务数据
    const limitPromise = ctx.apolloClient.getConfig({
      appId: 'ebiz-salesman',
      namespace: 'application',
      key: 'salesmanAgentLimitConfig',
    });

    const businessPromise = (() => {
      if (activityType === 2) {
        return new AccountReadApiService(ctx).listFullAccountInfo({
          kdtId: ctx.kdtId,
          salesmanType: 2,
          withoutAmount: true,
          startTime: Math.floor(subDays(new Date(), day).getTime() / 1000),
          endTime: Math.floor(new Date().getTime() / 1000),
        });
      } else if (activityType === 3) {
        return new AgentTaskApiService(ctx)
          .listLowActivateDatePage({
            kdtId: ctx.kdtId,
            day: filterNumber(day),
            rateDescend: filterNumber(rateDescend),
          })
          .catch();
      }
    })();

    return Promise.all([limitPromise, businessPromise]).then(([limitConfigs, businessRes]) => {
      const targetConfig = limitConfigs.find(config => config.activityType === activityType);
      const maxLimit = targetConfig ? targetConfig.totalLimit : Infinity;
      const totalItems = businessRes?.totalItems || 0;
      return Math.min(totalItems, maxLimit);
    });
  }

  async getCrowdNum() {
    const { ctx } = this;
    const { actType, day, rateDescend } = this.ctx.getQueryData();
    const result = await this.getActivityCrowd({ actType, day, rateDescend });
    ctx.json(0, 'ok', result);
  }

  // 查询apollo配置的思考过程、活动名称、奖励名称
  async getApolloCoT() {
    const { ctx } = this;
    const { actType, day, rateDescend } = this.ctx.getQueryData();

    const fetchList = [
      ctx.apolloClient.getConfig({
        appId: 'wsc-pc-salesman',
        namespace: 'wsc-pc-salesman.config',
        key: 'operation_agent_CoT',
      }),
      this.getActivityCrowd({ actType, day, rateDescend }),
    ];

    const actMap = { 2: 'new', 3: 'old' };
    const [jsonMap, num] = await Promise.all(fetchList);
    const result = generateRandomRecruitInfo(jsonMap[actMap[actType]], num);
    ctx.json(0, 'ok', result);
  }

  /**
   * 创建定时任务
   * 1. 拉 ai 那边的定时任务模板信息
   * 2. 填充模板的必要字段，保存，返回自动任务id
   * 3. 自动任务 id 和分销员的活动 id 关联上。
   */
  async createAutoTask() {
    const { ctx } = this;
    const postData = ctx.getPostData();
    const tempId = isQA ? 101 : 110;
    try {
      const tempData = await new FlowTempService(ctx).getFlowTempByIdV2(tempId, ctx.kdtId);
      const taskData = {
        ...handleTempWithPost(tempData, postData),
        ...this.getOperator(ctx),
        kdtId: ctx.kdtId,
      };
      // console.log('createFlow', taskData, taskData.chainRules[0].action);
      const flowId = await new FlowService(ctx).createFlow(taskData);
      // console.log('flowRes', flowRes);
      const result = await new AgentActivityApiService(ctx).updateActivity({
        kdtId: ctx.kdtId,
        actionTaskId: flowId,
        activityType: postData.activityType,
        activityId: postData.activityId,
      });
      ctx.json(0, 'ok', result);
    } catch (e) {
      ctx.fail(e.code, e.msg);
    }
  }

  async getInitPlanData() {
    const { ctx } = this;
    const { actType, day, rateDescend } = this.ctx.getQueryData();
    const recruitUrl = `${centerUrl}?kdt_id=${ctx.kdtId}`;
    const fetchList = [
      new AgentActivityApiService(ctx)
        .getActivityStartTime({
          kdtId: ctx.kdtId,
          activityType: actType,
        })
        .catch(() => 0),
      this.getGetJson(),
      this.getPosterJson({ actType }),
      this.ctx.shortUrl.toShort(recruitUrl),
      this.getActivityCrowd({ actType, day, rateDescend }),
    ];

    const [startTime, setting, posterUrl, recruitShortUrl, crowdNum] = await Promise.all(fetchList);

    const { salesmanName } = setting;

    return ctx.json(0, 'ok', {
      startTime,
      posterUrl,
      salesmanName,
      recruitShortUrl,
      crowdNum,
    });
  }

  /** @description 查询店铺分销模式 */
  async getGetJson() {
    const { ctx } = this;
    const distributionModeGetWebDTO = {
      operator: {
        operatorSource: 'wsc-pc-salesman',
        operatorClientIp: ctx.firstXff,
        ...this.getOperator(ctx),
      },
      kdtId: ctx.kdtId,
    };

    const result = await new DistributionModeWebApiService(ctx).get(distributionModeGetWebDTO);
    return result;
  }

  async getPosterJson({ actType }) {
    const { ctx } = this;
    const url = `${centerUrl}?kdt_id=${ctx.kdtId}`;
    const qrCode = await this.ctx.qrcode.create(
      url,
      {
        size: 200,
      },
      'base64'
    );
    ctx.setState('qrCode', qrCode);
    const tempPath =
      filterNumber(actType) === 2
        ? 'salesman-agent/operation-new.html'
        : 'salesman-agent/operation-old.html';
    const html = await ctx.renderView(tempPath);
    let snapshot;

    try {
      snapshot = await new SnapshotService(ctx).postSnapshot({
        html,
        operatorId: ctx.userId,
        height: 562,
        width: 375,
        alwaysCdn: 1,
        quality: 100,
      });
    } catch (err) {}
    return snapshot.img || '';
  }

  async getPoster() {
    const { ctx } = this;
    const recruitUrl = `${centerUrl}?kdt_id=${ctx.kdtId}`;
    const url = await this.ctx.shortUrl.toShort(recruitUrl);
    return ctx.json(0, 'ok', { posterUrl: url });
  }
}

module.exports = OperationAgentController;
