const BaseController = require('../../base/BaseController');
const FllowService = require('../../../services/flow/FLowService');

class IndexController extends BaseController {
  constructor(ctx) {
    super(ctx);
  }

  getOperator(ctx) {
    const { id: operatorId, nickName: operatorName, mobile: operatorPhone } = ctx.getLocalSession(
      'userInfo'
    );
    return {
      operatorId,
      operatorName,
      operatorPhone: typeof operatorPhone === 'string' ? operatorPhone : null,
      // operatorBuyerId: operatorId,
      operatorKdtId: ctx.kdtId,
    };
  }

  // 暂停自动任务
  async pauseAutoTask() {
    const { ctx } = this;
    try {
      const { kdtId } = ctx;
      const { flowId } = ctx.getPostData();
      const result = await new FllowService(ctx).disable({
        kdtId,
        flowId,
        ...this.getOperator(ctx),
      });
      ctx.json(0, 'ok', result);
    } catch (e) {
      ctx.fail(e.code, e.msg);
    }
  }

  // 启用自动任务
  async runAutoTask() {
    const { ctx } = this;
    const { kdtId } = ctx;
    const { flowId } = ctx.getPostData();
    try {
      const result = await new FllowService(ctx).enableV2({
        kdtId,
        flowId,
        ...this.getOperator(ctx),
      });
      ctx.json(0, 'ok', result);
    } catch (e) {
      ctx.fail(e.code, e.msg);
    }
  }
}

module.exports = IndexController;
