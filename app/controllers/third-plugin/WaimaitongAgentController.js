const { checkRetailChainStore } = require('@youzan/utils-shop');
const BaseController = require('../base/BaseController');
const MyAgentService = require('../../services/api/agent/MyAgentService');
const MyAgentSkillService = require('../../services/api/agent/MyAgentSkillService');
const ChannelItemOperateFrontService = require('../../services/api/mall-item/ChannelItemOperateFrontService');
const ChannelItemQueryFrontService = require('../../services/api/mall-item/ChannelItemQueryFrontService');
const SalesChannelMgrService = require('../../services/api/shopcenter/shopfront/SalesChannelMgrService');
const OrderSkillService = require('../../services/api/trade/OrderSkillService');
const SingleStaffService = require('../../services/api/staff/SingleStaffService');
const WmAgentOverviewService = require('../../services/api/trade/WmAgentOverviewService');
const HQStoreSearchService = require('../../services/api/retail/shop/HQStoreSearchService');
const ShopBaseReadService = require('../../services/api/shop-center/ShopBaseReadService');
const ItemQueryForICService = require('../../services/api/ic/search/ItemQueryForICService');
const { fromApp, retailSource } = require('../../constants');

class WaimaitongAgentController extends BaseController {
  constructor(ctx) {
    super(ctx);
    this.ctx.biz = '外卖通智能体';
  }

  get operator() {
    const { userId } = this.ctx;
    const { nickName } = this.ctx.getLocalSession('userInfo');
    return {
      operatorId: `${userId}`,
      operatorName: nickName,
    };
  }

  /**
   * 获取智能体详情
   * @param ctx
   * @returns {Promise<void>}
   */
  async getMyAgentDetail(ctx) {
    const { myAgentId } = ctx.query;
    const result = await new MyAgentService(ctx).getMyAgentDetail({
      kdtId: ctx.kdtId,
      myAgentId: +myAgentId,
      operator: this.operator,
    });
    return ctx.json(0, '', result);
  }

  /**
   * 获取技能列表
   * @param ctx
   * @returns {Promise<void>}
   */
  async querySkills(ctx) {
    const { agentId } = ctx.query;
    const result = await new MyAgentSkillService(ctx).querySkills({
      agentId: +agentId,
      kdtId: ctx.kdtId,
      operator: this.operator,
    });
    return ctx.json(0, '', result);
  }

  /**
   * 保存技能
   * @param ctx
   * @returns {Promise<void>}
   */
  async saveSkill(ctx) {
    const { agentId, skillId, enable, config } = ctx.request.body;
    const result = await new MyAgentSkillService(ctx).save({
      agentId,
      skillId,
      enable,
      config,
      operator: this.operator,
      kdtId: ctx.kdtId,
    });
    return ctx.json(0, '', result);
  }

  /**
   * 获取执行记录
   * @param ctx
   * @returns {Promise<void>}
   */
  async pageExecuteRecords(ctx) {
    const { myAgentId, pageSize, page } = ctx.query;
    const result = await new MyAgentService(ctx).pageExecuteRecords({
      kdtId: ctx.kdtId,
      myAgentId: +myAgentId,
      pageSize: +pageSize,
      page: +page,
      operator: this.operator,
    });
    return ctx.json(0, '', result);
  }

  /**
   * 商品-获取托管技能
   * @param ctx
   * @returns {Promise<void>}
   */
  async queryHostAgentSkill(ctx) {
    const { agentId } = ctx.query;
    const result = await new ChannelItemQueryFrontService(ctx).queryHostAgentSkill({
      kdtId: ctx.kdtId,
      agentId: +agentId,
      fromApp,
      operator: {
        nickName: this.operator.operatorName,
        userId: +this.operator.operatorId,
        source: fromApp,
      },
      retailSource,
    });
    return ctx.json(0, '', result);
  }

  /**
   * 商品-保存托管技能
   * @param ctx
   * @returns {Promise<void>}
   */
  async saveHostAgentSkill(ctx) {
    const { agentId, skills } = ctx.request.body;
    const result = await new ChannelItemOperateFrontService(ctx).saveHostAgentSkill({
      kdtId: ctx.kdtId,
      agentId: +agentId,
      skills,
      fromApp,
      operator: {
        nickName: this.operator.operatorName,
        userId: +this.operator.operatorId,
        source: fromApp,
      },
      retailSource,
    });
    return ctx.json(0, '', result);
  }

  /**
   * 店铺-获取托管配置
   * @param ctx
   * @returns {Promise<void>}
   */
  async queryWmShopHostConfig(ctx) {
    const result = await new SalesChannelMgrService(ctx).queryWmShopHostConfig(ctx.kdtId);

    return ctx.json(0, '', result);
  }

  /**
   * 店铺-更新托管配置
   * @param ctx
   * @returns {Promise<void>}
   */
  async updateWmShopHostConfig(ctx) {
    const { channelAiConfigDetailDTO, enable } = ctx.request.body;

    const result = await new SalesChannelMgrService(ctx).updateWmShopHostConfig({
      channelAiConfigDetailDTO,
      enable,
      kdtId: ctx.kdtId,
    });

    return ctx.json(0, '', result);
  }

  /**
   * 店铺-获取托管状态
   * @param ctx
   * @returns {Promise<void>}
   */
  async queryWmChannelHostRunStatus(ctx) {
    const { agentId } = ctx.query;
    const result = await new SalesChannelMgrService(ctx).queryWmChannelHostRunStatus({
      agentId: +agentId,
      kdtId: ctx.kdtId,
    });
    return ctx.json(0, '', result);
  }

  /**
   * 交易-获取订单技能
   * @param ctx
   * @returns {Promise<void>}
   */
  async getOrderSkill(ctx) {
    const { skillId, agentId } = ctx.query;
    const result = await new OrderSkillService(ctx).getOrderSkill({
      skillId: +skillId,
      agentId: +agentId,
      kdtId: ctx.kdtId,
    });
    return ctx.json(0, '', result);
  }

  /**
   * 交易-创建订单技能
   * @param ctx
   * @returns {Promise<void>}
   */
  async createOrderSkill(ctx) {
    const { agentId, skillList, rootKdtId, agentName } = ctx.request.body;
    const result = await new OrderSkillService(ctx).createOrderSkill({
      agentId,
      skillList,
      rootKdtId,
      kdtId: ctx.kdtId,
      agentName,
    });
    return ctx.json(0, '', result);
  }

  /**
   * 获取员工列表
   * @param ctx
   * @returns {Promise<void>}
   */
  async getStaffList(ctx) {
    const { pageNo, pageSize } = ctx.query;
    const result = await new SingleStaffService(ctx).find({
      kdtId: ctx.kdtId,
      pageNo: +pageNo,
      pageSize: +pageSize,
    });
    return ctx.json(0, '', result);
  }

  /**
   * 交易-获取外卖托管智能体概况
   * @param ctx
   * @returns {Promise<void>}
   */
  async getWmAgentOverview(ctx) {
    const { startDate, endDate, agentId, skillTemplateId } = ctx.query;
    const result = await new WmAgentOverviewService(ctx).getWmAgentOverview({
      startDate: +startDate,
      endDate: +endDate,
      agentId: +agentId,
      skillTemplateId: +skillTemplateId,
      kdtId: ctx.kdtId,
      source: 'agent',
    });
    return ctx.json(0, '', result);
  }

  /**
   * 查询店铺信息
   */
  async getShopInfo(ctx) {
    const query = ctx.query || {};
    const shopInfo = ctx.getState('shopInfo');
    const isRetailChainStore = checkRetailChainStore(shopInfo);

    const [organizationInfo = {}, baseShopInfo = {}, goodsInfo = {}] = await Promise.all([
      isRetailChainStore
        ? new HQStoreSearchService(ctx).search({
            pageNo: 1,
            pageSize: 10,
            shopRoleList: [2],
            ...query,
            retailSource,
            kdtId: ctx.kdtId,
            adminId: ctx.userId,
          })
        : Promise.resolve({}),
      new ShopBaseReadService(ctx).getShopBaseInfoByKdtId(ctx.kdtId),
      new ItemQueryForICService(ctx).listItemsWithoutCache({
        fromApp: retailSource,
        page: 1,
        pageSize: 1,
        headKdtId: ctx.kdtId,
        kdtIds: [ctx.kdtId],
        channel: 0,
        customIsDisplays: [0, 1, 8],
        isDelete: 0,
        notExcludeAbilityMarks: [10033],
        sellType: 1,
      }),
    ]);

    const { businessName = '', province = '', city = '' } = baseShopInfo;
    const subShopNum = organizationInfo.paginator?.totalCount || 0;
    const goodsNum = goodsInfo.paginator?.totalCount || 0;

    return ctx.json(0, '', {
      isRetailChainStore,
      businessName,
      province,
      city,
      subShopNum,
      goodsNum,
    });
  }
}

module.exports = WaimaitongAgentController;
