const BaseController = require('../base/BaseController');
const BirthdayMarketingAgentService = require('../../services/api/scrm/BirthdayMarketingAgentService');
const SmsSignatureService = require('../../services/api/scrm/SmsSignatureService');
const MarketingAgentService = require('../../services/api/scrm/MarketingAgentService');
const ContentAsyncService = require('../../services/api/aigc/ContentAsyncService');
const AsyncResponseService = require('../../services/api/aigc/AsyncResponseService');

class IndexController extends BaseController {
  constructor(ctx) {
    super(ctx);
    this.ctx.biz = '生日营销智能体';
  }

  async checkIsSupportSalesChannel() {
    /**
     * 抄的wsc-pc-ump
     * 店铺是否支持多渠道 - 在多渠道白名单内的连锁L
     * PS: 在白名单内的一定都是新商品模型
     */
    return this.ctx.matrix.canIUse('ump.sales-channel.base');
  }

  // 查询任务列表
  async getTaskList() {
    const { ctx } = this;
    const { pageSize, pageNo } = ctx.getQueryData();
    const params = {
      pageSize,
      pageNo,
      kdtId: ctx.kdtId,
    };
    const result = await new BirthdayMarketingAgentService(ctx).getTaskList(params);
    return ctx.success(result);
  }

  // 查询店铺签名
  async getCurShopSign() {
    const { ctx } = this;
    const signInfo = await new SmsSignatureService(ctx).getLatestSmsSignature({
      kdtId: ctx.kdtId,
    });

    // 检查signInfo是否存在且有name属性
    if (signInfo && signInfo.name) {
      return signInfo.name;
    }

    // 如果没有获取到签名，尝试获取默认签名
    const defaultSignResult = await new SmsSignatureService(ctx).getDefaultSmsSignature({
      kdtId: ctx.kdtId,
    });

    // 确保返回字符串类型
    return defaultSignResult || '有赞';
  }

  // 查询任务预设数据
  async getInitialTaskInfo() {
    const { ctx } = this;
    const { skillType = 2 } = ctx.getQueryData();
    const [result, isSupportSalesChannel, currentShopSign] = await Promise.all([
      new BirthdayMarketingAgentService(ctx).getInitialTaskInfo({
        kdtId: ctx.kdtId,
        type: skillType,
      }),
      this.checkIsSupportSalesChannel(),
      this.getCurShopSign(),
    ]);
    return ctx.success({
      ...result,
      isSupportSalesChannel,
      currentShopSign,
      shopInfo: ctx.getState('shopInfo'),
    });
  }

  // 创建任务
  async createTask() {
    const { ctx } = this;
    const { skillType = 2, ...birthdayMarketingTaskDTO } = ctx.getPostData();
    const params = {
      kdtId: ctx.kdtId,
      operatorDTO: {
        operatorId: ctx.userId,
      },
      type: skillType,
      birthdayMarketingTaskDTO,
    };
    const result = await new BirthdayMarketingAgentService(ctx).createTask(params);
    return ctx.success(result);
  }

  // 轮询任务结果
  async getTaskResult() {
    const { ctx } = this;
    const { key } = ctx.getQueryData();
    const result = await new BirthdayMarketingAgentService(ctx).getTaskResult({ key });
    return ctx.success(result);
  }

  // 查询任务详情
  async getTaskDetail() {
    const { ctx } = this;
    const { taskId } = ctx.getQueryData();
    const params = {
      kdtId: ctx.kdtId,
      taskId,
    };
    const result = await new BirthdayMarketingAgentService(ctx).getTaskDetail(params);
    return ctx.success(result);
  }

  // 停止任务
  async stopTask() {
    const { ctx } = this;
    const { planId } = ctx.getPostData();
    const params = {
      kdtId: ctx.kdtId,
      msg: '',
      operatorId: ctx.userId,
      planId,
    };
    const result = await new MarketingAgentService(ctx).stopPlan(params);
    return ctx.success(result);
  }

  async aigcMiddleId() {
    const { ctx } = this;
    const { kdtId, userId } = ctx;
    const { requestDesc } = ctx.getPostData() || {};
    const hqKdtId = this.getHqKdtId();
    const userInfo = ctx.getState('userInfo');
    ctx.logger.info(`🚀 ~ IndexController ~ aigcMiddleId ~ hqKdtId: ${hqKdtId}`);

    const idRes = await new ContentAsyncService(ctx).queryAiGenerateContentId({
      kdtId,
      hqKdtId: hqKdtId || kdtId,
      userId,
      userPhoneNumber: userInfo.mobile,
      itemType: 3,
      requestDesc,
      requestUrl: '',
      requestUrlHash: 'cg:test:9',
      promoteChannel: 5,
      isEmoji: 0,
      wordNumber: 20,
      contentCount: 1,
      toneStyle: 0,
      requestKeywords: '',
      itemComment: '',
      sourceType: 1,
      sceneTag: 'retail_crowd_manage_strategy',
    });

    return ctx.success(idRes);
  }

  async aigcTextInfo() {
    const { ctx } = this;
    const { responseId } = ctx.getPostData() || {};

    const res = await new AsyncResponseService(ctx).getAiGenerateContentById({
      responseId: responseId || '',
    });

    return ctx.success(res);
  }

  // 查询订单/生日关系网挖掘开启状态
  async getDiggerStatus(ctx) {
    const { kdtId } = ctx;
    const result = await new BirthdayMarketingAgentService(ctx).getOrderDiggerStatus({
      kdtId,
    });
    return ctx.success(result);
  }

  // 更新订单/生日关系网挖掘开启状态
  async updateDiggerStatus(ctx) {
    const { kdtId } = ctx;
    const { status, birthdayRelationStatus } = ctx.getPostData();
    const result = await new BirthdayMarketingAgentService(ctx).updateOrderDiggerStatus({
      kdtId,
      status,
      birthdayRelationStatus,
    });
    return ctx.success(result);
  }

  // 执行订单挖掘任务
  async executeDiggerTask(ctx) {
    const { kdtId } = ctx;
    const result = await new BirthdayMarketingAgentService(ctx).executeOrderDiggerTask({
      kdtId,
    });
    return ctx.success(result);
  }

  // 查询订单挖掘执行结果
  async getOrderDiggerResult(ctx) {
    const { kdtId } = ctx;
    const result = await new BirthdayMarketingAgentService(ctx).getOrderDiggerResult({
      kdtId,
    });
    return ctx.success(result);
  }

  // 查询运营数据
  async getOperateData(ctx) {
    const { kdtId } = ctx;
    const result = await new BirthdayMarketingAgentService(ctx).getOperateData({
      kdtId,
    });
    return ctx.success(result);
  }

  // 查询挖掘数据
  async getDiggerData(ctx) {
    const { kdtId } = ctx;
    const result = await new BirthdayMarketingAgentService(ctx).getDiggerData({
      kdtId,
    });
    return ctx.success(result);
  }

  async checkCoupon() {
    const { ctx } = this;
    const params = {
      kdtId: ctx.kdtId,
      operatorDTO: {
        operatorId: ctx.userId,
      },
      couponDTO: ctx.getPostData(),
    };
    const result = await new BirthdayMarketingAgentService(ctx).checkCoupon(params);
    return ctx.success(result);
  }
}

module.exports = IndexController;
