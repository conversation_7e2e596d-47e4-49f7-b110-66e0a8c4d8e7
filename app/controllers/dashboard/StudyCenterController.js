const BaseController = require('../base/BaseController');
const AppStudyService = require('../../services/api/ebiz/mall/AppStudyService');

class StudyCenterController extends BaseController {
  constructor(ctx) {
    super(ctx);
    this.ctx.biz = '工作台-学习交流';
  }

  async listByColumnInPage(ctx) {
    const { page = 1, pageSize = 3, columnId } = ctx.query;
    const result = await new AppStudyService(ctx).listByColumnInPage({
      page,
      pageSize,
      columnId,
      status: 1, // 已上架专栏
    });
    ctx.successRes(result);
  }
}

module.exports = StudyCenterController;
