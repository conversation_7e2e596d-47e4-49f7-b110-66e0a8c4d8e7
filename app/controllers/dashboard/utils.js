/* eslint-disable camelcase */
// 特殊处理供货商后台的实时概览title
const formatCmpListToSupplier = cmpList => {
  const _cmpList = [...cmpList];
  // 所要修改的type所对应的title
  const typeMap = {
    dash_ov_fx_order_pay_num: '供货支付订单数',
    dash_ov_fx_order_pay_amount: '供货订单金额(元)',
    dash_ov_order_fx_supplier_amount: '供货金额(元)',
  };
  Object.keys(typeMap).forEach(type => {
    const item = _cmpList.find(it => it.type === type);
    !!item && (item.title = typeMap[type]);
  });
  return _cmpList;
};

module.exports = {
  formatCmpListToSupplier,
};
