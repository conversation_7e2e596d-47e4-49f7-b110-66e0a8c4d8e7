const DashboardBaseController = require('./DashboardBaseController');
const DataOverviewService = require('../../services/api/showcase/center/DataOverviewService');
const DataAdapter = require('./data-adapter');
const { getRole, getMultiRoles, getMultiSamRoles } = require('./common/user-role');
const { SAM_ROLE_TYPE, fromApp } = require('../../constants');
const { PLATFORM_ENUM } = require('./constants');
const { formatCmpListToSupplier } = require('./utils');

class DataOverviewController extends DashboardBaseController {
  constructor(ctx) {
    super(ctx);
    this.ctx.biz = '工作台-实时概况';
  }

  async saveCustomDataOverviewSetting(ctx) {
    const { cmpList, platform } = ctx.getPostData();
    const userRole = await getRole(ctx);
    if (!userRole) {
      ctx.successRes();
      return;
    }
    const { roleId: samRoleId, roleType: samRoleType, biz: samBiz } = userRole;
    const isLiteOnlineManager = await this.checkLiteOnlineManager(ctx);
    let storeKdtId = null;
    if (isLiteOnlineManager) {
      storeKdtId = await this.getLiteKdtId(ctx);
    }
    const shopBizType = await this.getBizType4ShowCaseCenterV1(ctx, { isLiteOnlineManager });
    const result = await new DataOverviewService(ctx).saveCustomDataOverviewSetting({
      kdtId: ctx.kdtId,
      cmpList,
      fromApp,
      adminId: ctx.userId,
      operatorType: 1,
      operatorId: ctx.userId,
      platform: platform || PLATFORM_ENUM.PC,
      showType: 1,
      samBiz,
      samRoleId,
      samRoleType: SAM_ROLE_TYPE[samRoleType] || SAM_ROLE_TYPE.DEFAULT_ROLE,
      shopBizType,
      storeKdtId,
    });
    ctx.successRes(result);
  }

  async queryDataOverviewDefaultSetting(ctx) {
    const { userRole, multiRoles } = await getMultiRoles(ctx);
    const multiSamRoles = getMultiSamRoles(multiRoles);

    if (!userRole) {
      ctx.successRes();
      return;
    }
    const { roleId: samRoleId, roleType: samRoleType, biz: samBiz } = userRole;
    const isLiteOnlineManager = await this.checkLiteOnlineManager(ctx);
    let storeKdtId = null;
    if (isLiteOnlineManager) {
      storeKdtId = await this.getLiteKdtId(ctx);
    }
    const shopBizType = await this.getBizType4ShowCaseCenterV1(ctx, { isLiteOnlineManager });
    const result = await new DataOverviewService(ctx).queryDataOverviewDefaultSetting({
      shopBizType,
      samBiz,
      samRoleId,
      kdtId: ctx.kdtId,
      samRoleType: SAM_ROLE_TYPE[samRoleType] || SAM_ROLE_TYPE.DEFAULT_ROLE,
      adminId: ctx.userId,
      platform: PLATFORM_ENUM.PC,
      storeKdtId,
      multiSamRoles,
    });
    ctx.successRes(result);
  }

  async listDataOverviewCmpsGroupByTag(ctx) {
    const { keyword } = ctx.query;
    const { userRole, multiRoles } = await getMultiRoles(ctx);
    if (!userRole) {
      ctx.successRes();
      return;
    }
    const { roleId: samRoleId, roleType: samRoleType, biz: samBiz } = userRole;
    const multiSamRoles = getMultiSamRoles(multiRoles);
    const isLiteOnlineManager = await this.checkLiteOnlineManager(ctx);
    let storeKdtId = null;
    if (isLiteOnlineManager) {
      storeKdtId = await this.getLiteKdtId(ctx);
    }
    const shopBizType = await this.getBizType4ShowCaseCenterV1(ctx, { isLiteOnlineManager });
    const result = await new DataOverviewService(ctx).listDataOverviewCmpsGroupByTag({
      shopBizType,
      samBiz,
      samRoleId: multiSamRoles.length > 1 ? null : samRoleId,
      kdtId: ctx.kdtId,
      samRoleType: SAM_ROLE_TYPE[samRoleType] || SAM_ROLE_TYPE.DEFAULT_ROLE,
      title: keyword || null,
      multiSamRoles,
      storeKdtId,
    });
    if (ctx.hostname.indexOf('fx.youzan.com') === 0) {
      result.length &&
        result.forEach((it) => {
          it.cmpBaseInfoDTOList = formatCmpListToSupplier(it.cmpBaseInfoDTOList);
        });
    }
    ctx.successRes(result);
  }

  async getLiteWscSummaryData(ctx) {
    const userRole = await getRole(ctx);
    if (!userRole) {
      ctx.successRes();
      return;
    }
    const { roleId: samRoleId, roleType: samRoleType, biz: samBiz } = userRole;

    const result = await new DataOverviewService(ctx).queryDataOverviewDefaultForBriefWsc({
      samBiz,
      samRoleType: SAM_ROLE_TYPE[samRoleType] || 1,
      samRoleId,
      kdtId: ctx.kdtId,
      adminId: ctx.userId,
      platform: PLATFORM_ENUM.PC,
    });
    const { cmpList } = result;
    const types = cmpList.map((item) => item.type);
    const data = await new DataAdapter(ctx).getData(types, samRoleId);
    ctx.successRes({
      ...result,
      data,
    });
  }
}

module.exports = DataOverviewController;
