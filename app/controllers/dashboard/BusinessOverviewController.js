const DashboardBaseController = require('./DashboardBaseController');
const MenuService = require('../../services/api/rig/MenuService');
const ShopAdminConfigReadService = require('../../services/api/shopcenter/shopconfig/ShopAdminConfigReadService');
const ShopAdminConfigWriteService = require('../../services/api/shopcenter/shopconfig/ShopAdminConfigWriteService');
const DataOverviewService = require('../../services/api/showcase/center/DataOverviewService');
const SingleOverviewDataService = require('../../services/api/retail/data/SingleOverviewDataService');
const { fromApp, SAM_ROLE_TYPE } = require('../../constants');
const { getMultiRoles, getMultiSamRoles } = require('./common/user-role');

const {
  MENU_TREE_VERSION,
  MENU_TREE_ITEM_TYPE,
  CUSTOM_SETTING_KEY,
  PLATFORM_ENUM,
} = require('./constants');
const DataAdapter = require('./data-adapter');

const { checkRetailShop, checkPartnerStore } = require('@youzan/utils-shop');
const {
  checkMenuItemIsAccessible,
  checkMenuItemIsAbilityValid,
  getShopConfigKey,
} = require('./common');

class DataOverviewController extends DashboardBaseController {
  /** for 存量数据 */
  async _getCustomSummaryDataKeys(storeKdtId) {
    const { ctx } = this;
    const { kdtId, userId } = this.ctx;

    const { userRole, multiRoles } = await getMultiRoles(ctx);

    const { roleId: samRoleId, roleType: samRoleType, biz: samBiz } = userRole;
    const multiSamRoles = getMultiSamRoles(multiRoles);
    const isLiteOnlineManager = await this.checkLiteOnlineManager(ctx);
    const shopBizType = await this.getBizType4ShowCaseCenterV1(ctx, { isLiteOnlineManager });

    const result = await new DataOverviewService(ctx)
      .queryDataOverviewSetting({
        shopBizType,
        samBiz,
        samRoleId: multiSamRoles.length > 1 ? null : samRoleId,
        kdtId,
        samRoleType: SAM_ROLE_TYPE[samRoleType] || SAM_ROLE_TYPE.DEFAULT_ROLE,
        adminId: userId,
        platform: PLATFORM_ENUM.PC,
        multiSamRoles,
        storeKdtId,
      })
      .catch(err => {
        this.ctx.logger.warn('[实时概况]-查询自定义设置结果报错', null, err);
        return null;
      });

    if (!result) {
      return null;
    }
    const keys = result.cmpList.map(item => item.type);
    if (!keys.length) {
      this.ctx.logger.warn('[实时概况]-查询自定义设置结果为空', null, keys);
    }
    return keys.length > 0 ? keys : null;
  }
  /**
   * 查询聚合数据接口
   * 因为部分接口需要角色信息，所以保留旧版逻辑，只是偏好从新接口处获得
   *
   * 未保留同城网店管理员逻辑判断
   * storeKdtId 和同城网店相关，暂不保留
   */
  async getCustomSummaryDataJson(ctx) {
    const { kdtId, userId } = ctx;
    const { storeKdtId, timeType, startTime, endTime } = ctx.getRequestData();

    /**
     * 查询营业日数据
     */
    const isBusinessDay = Number(timeType) === 1;

    const response = {
      data: [],
      updateTime: Date.now(),
    };

    // 部分接口需要角色信息，先请求，用于查询数据
    const { userRole, multiRoles } = await getMultiRoles(ctx);
    if (!userRole) {
      return ctx.successRes(response);
    }
    let isLiteOnlineManager, finalStoreKdtId;
    if (checkRetailShop(ctx.getState('shopInfo'))) {
      // 兼容高级版非多渠道的情况
      isLiteOnlineManager = await this.checkLiteOnlineManager(ctx);
      finalStoreKdtId = storeKdtId;
      if (isLiteOnlineManager) {
        finalStoreKdtId = await this.getLiteKdtId(ctx);
      }
    }

    const [menuTreeResult, customSettingResult] = await Promise.all([
      new MenuService(ctx).getMenuTree(await this.getMenuTreeParams(kdtId, userId)),
      new ShopAdminConfigReadService(ctx).queryShopAdminConfig(
        kdtId,
        userId,
        getShopConfigKey(ctx, CUSTOM_SETTING_KEY)
      ),
    ]);

    this.ctx.logger.info('[实时概况]-查询RIG菜单结果', null, { menuTreeResult });

    const customSetting = this.parseCustomSetting(customSettingResult.value);
    const { keys: customSettingWithAccessible, menuItems } = await this.getAccessibleCustomSetting(
      menuTreeResult,
      customSetting,
      finalStoreKdtId
    );

    // 分销数据未录入，暂不进行分销转换
    // if (ctx.hostname.indexOf('fx.youzan.com') === 0) {
    //   result.cmpList = formatCmpListToSupplier(cmpList);
    // }

    // 营业日数据走单独的逻辑
    if (isBusinessDay) {
      let params = {
        kdtId,
        channelType: 'ALL',
        dateType: 'NATURAL_DAY',
        adminId: userId,
        startTime,
        endTime,
      };
      if (finalStoreKdtId) {
        params = {
          ...params,
          storeKdtId: finalStoreKdtId,
        };
      }
      const result = await new SingleOverviewDataService(ctx).findBusinessOverViewData(params);
      try {
        response.data = new DataAdapter(ctx).buildBusinessData(customSettingWithAccessible, result);
      } catch (e) {
        /* empty */
      }
      // 包装数据
    } else {
      try {
        response.data = await new DataAdapter(ctx).getData(
          customSettingWithAccessible,
          userRole.roleId,
          multiRoles,
          true,
          finalStoreKdtId
        );
      } catch (e) {
        /* empty */
      }
    }

    // 对返回信息进行包装
    response.data.forEach((item, index) => {
      // 兜底处理，防止出现意外
      let findMenuItem = menuItems[index];
      if (findMenuItem.menuItemKey !== item.type) {
        findMenuItem = menuItems.find(menuItem => menuItem.menuItemKey === item.type);
      }

      ({ name: item.name, url: item.url } = findMenuItem);
      if (isBusinessDay && item.url) {
        item.url = 'https://store.youzan.com/erp/business-overview#/';
      }
    });

    ctx.successRes(response);
  }
  /**
   * 新版工作台获取 menuTree 请求参数
   */
  async getMenuTreeParams(kdtId, userId) {
    return {
      namespace: 'np_yz_shop',
      platform: 10,
      thirdUserId: userId.toString(),
      thirdTenantId: kdtId.toString(),
      version: await this.getMenuVersion(this.ctx, MENU_TREE_VERSION),
    };
  }

  /**
   * 解析偏好设置
   */
  parseCustomSetting(rawCustomSetting) {
    // 尝试解析获得的数据
    let customSetting;
    try {
      customSetting = JSON.parse(rawCustomSetting);
      if (!Array.isArray(customSetting)) throw Error('');
    } catch (e) {
      customSetting = null;
    }
    return customSetting;
  }

  /**
   * 新版工作台获得有权限的偏好设置信息
   * 需要保持 customSettingData 顺序不变，移除无权限的项
   * customSettingData === null 时表示无数据，需要获得默认值
   */
  async getAccessibleCustomSetting(menuTreeData, customSettingData, storeKdtId, maxLength = 8) {
    const shopInfo = this.ctx.getState('shopInfo');
    let customSettingDataWithRetail = customSettingData;
    const flatten = (item, array) => {
      const children = item.children;
      if (!Array.isArray(children)) return;

      array.push(...children);

      children.forEach(child => flatten(child, array));
    };
    const flattenMenu = [];
    flatten({ children: menuTreeData }, flattenMenu);
    if (checkRetailShop(shopInfo) && !checkPartnerStore(shopInfo) && !customSettingData) {
      customSettingDataWithRetail = await this._getCustomSummaryDataKeys(storeKdtId);
    }

    if (customSettingDataWithRetail === null) {
      // 需要返回默认值，只需要返回前 8 项即可

      let defaultCustomSetting = [];

      // 根据 menuItemType 和 accessible 过滤合法值
      defaultCustomSetting = flattenMenu.filter(
        item => checkMenuItemIsAccessible(item) && item.menuItemType === MENU_TREE_ITEM_TYPE
      );

      // 排序
      defaultCustomSetting.sort((a, b) => a.sort - b.sort);
      // 取前 maxLength 项
      if (defaultCustomSetting.length > maxLength) {
        defaultCustomSetting.length = maxLength;
      }
      // 获得 key 和 item
      return {
        menuItems: defaultCustomSetting,
        keys: defaultCustomSetting.map(item => item.menuItemKey),
      };
    }

    // 判断已存在的偏好设置是否有权限
    const menuItems = [];
    const customSetting = customSettingDataWithRetail.filter(key => {
      const findMenuItem = flattenMenu.find(menuItem => menuItem.menuItemKey === key);

      if (
        findMenuItem &&
        checkMenuItemIsAccessible(findMenuItem) &&
        findMenuItem.menuItemType === MENU_TREE_ITEM_TYPE
      ) {
        menuItems.push(findMenuItem);
        return true;
      }
      return false;
    });

    if (customSetting.length === 0) {
      this.ctx.logger.warn('[实时概况]-获取有权限的偏好设置信息为空', null, customSetting);
    }

    return {
      menuItems,
      keys: customSetting,
    };
  }

  /**
   * 新版工作台查询实时概况菜单数据
   */
  async queryBusinessOverviewMenuTreeJson(ctx) {
    const { kdtId, userId } = ctx;

    const result = await new MenuService(ctx).getMenuTree(
      await this.getMenuTreeParams(kdtId, userId)
    );
    // 根据店铺能力过滤
    if (Array.isArray(result)) {
      result.forEach(category => {
        if (Array.isArray(category.children)) {
          category.children = category.children.filter(item => checkMenuItemIsAbilityValid(item));
        }
      });
    }

    ctx.successRes(result);
  }

  /**
   * 新版工作台查询实时概况个人偏好
   */
  async queryBusinessOverviewCustomSettingJson(ctx) {
    const { kdtId, userId } = ctx;

    const [menuTreeResult, customSettingResult] = await Promise.all([
      new MenuService(ctx).getMenuTree(await this.getMenuTreeParams(kdtId, userId)),
      new ShopAdminConfigReadService(ctx).queryShopAdminConfig(
        kdtId,
        userId,
        getShopConfigKey(ctx, CUSTOM_SETTING_KEY)
      ),
    ]);

    const customSetting = this.parseCustomSetting(customSettingResult.value);
    const customSettingWithAccessible = {
      keys: (await this.getAccessibleCustomSetting(menuTreeResult, customSetting)).keys,
    };

    ctx.successRes(customSettingWithAccessible);
  }

  /**
   * 新版工作台保存实时概况个人偏好
   * @param ctx
   * @return {Promise<void>}
   */
  async saveBusinessOverviewCustomSettingJson(ctx) {
    const { kdtId, userId } = ctx;
    const { keys } = ctx.getPostData();

    // 校验参数合法性
    if (!Array.isArray(keys)) {
      ctx.fail(0, '参数错误！');
      return;
    }

    const menuTreeResult = await new MenuService(ctx).getMenuTree(
      await this.getMenuTreeParams(kdtId, userId)
    );

    const customSettingWithAccessible = (
      await this.getAccessibleCustomSetting(menuTreeResult, keys)
    ).keys;

    // 这里不用兼容高级版的同城管理员
    // 根据 kdtId 和 userId 确定数据，除非用户是高级管理员保存过一次自定义设置，然后又配置成了同城云店管理员
    const result = await new ShopAdminConfigWriteService(ctx).setShopAdminConfig({
      kdtId,
      adminId: userId,
      key: getShopConfigKey(ctx, CUSTOM_SETTING_KEY),
      value: JSON.stringify(customSettingWithAccessible),
      operator: {
        fromApp,
        id: userId,
      },
    });

    ctx.successRes(result);
  }
}

module.exports = DataOverviewController;
