# 概况数据聚合流程

<a href="https://doc.qima-inc.com/pages/viewpage.action?pageId=225156580">新数据接入指南</a>

1. 调用熊振华提供的接口获取到用户需要查看哪些数据（包含数据项的跳转链接，数据名称及type--唯一标识）

2. 调用类DataAdapter实例的getData方法, 传入数据的type组成的数组

3. getData通过数据的type在constants文件内定义的大对象 DATA_TYPE 中，去获取该type对应的配置信息（[adapter](#adapter)、method、[key](#key)、[unitType](#unitType)、[delay](#delay)、needCompare、dateType）

4. 在获取到所有数据的配置后，调用各个adapter内的method，adapter根据keyIdxMap吐出符合要求的结果（ 重点： adapter不能对取不到的数据给予默认值）

5. getData拿到所有的结果后，合并成一个对象，取出所有value返回

## 变量解释

### key
数据项在对应接口的返回值内的字段名

### unitType
单位类型，client根据unitType来处理，该处理逻辑放在client端的原因是目前概况页的数据展示有countUp的动画，放在node处理后，client处理起来比较麻烦。可优化

### keyIdxMap
[key](#key)与对应的数据在原数组中的index的map

eg: { pv: 3, uv: 6 }

### adapter
最后调用获取数据接口的class

adapter下的方法发起数据请求接口

adapter下的方法入参有 (keyIdxMap, storeId, roleId, delay, dateType)，其余特殊入参，在方法内自行获取

出参需吐出一个 { idx: value } 格式的对象

eg: { 3: pvValus, 6: uvValue }

目前需要由各个adapter去处理数据顺序的原因是，客服数据那边存在多个接口返回相同字段名的数据，而同时客服数据的处理逻辑较复杂，可能存在同名字段名的情况。当前的处理方法可以保证adapter内的方法之间不会互相影响，即使在adapter内有合并操作。

### delay

少部分数据存在获取不到实时数据的问题，delay和dateType用于描述数据的延期时间,例如dateType为月，delay为1，则表明该数据只能获取上月数据。

同时delay在需要获取对比数据（needCompare为true）时，会在获取对比数据时将自身+1传入adapter的method（有更复杂的场景，可将needCompare换成number)