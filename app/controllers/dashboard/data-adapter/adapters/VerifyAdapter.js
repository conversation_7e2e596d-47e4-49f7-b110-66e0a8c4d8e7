const startOfToDay = require('date-fns/start_of_today');
const endOfToDay = require('date-fns/end_of_today');
const subDays = require('date-fns/sub_days');
const format = require('date-fns/format');

const BaseAdaptor = require('./BaseAdaptor');
const CouponVerifyService = require('../../../../services/api/ump/asset/CouponVerifyService');
const { DATA_DATE_TYPE } = require('../constants');

class VerifyAdapter extends BaseAdaptor {
  constructor(ctx) {
    super(ctx);
  }

  async dispatch({ keyIdxMap = {}, ...options }) {
    const keys = Object.keys(keyIdxMap);
    const promises = keys.map(key => {
      return this.getVerifyList({ keyIdxMap: { [key]: keyIdxMap[key] }, ...options });
    });
    const datas = await Promise.all(promises);
    const result = {};
    datas.forEach(data => {
      Object.assign(result, data);
    });
    return result;
  }

  async getVerifyList({ dateType, keyIdxMap = {}, delay }) {
    const keys = Object.keys(keyIdxMap);
    const params = {};
    switch (dateType) {
      case DATA_DATE_TYPE.DAY:
        params.beginAt = format(subDays(startOfToDay(), delay), 'YYYY-MM-DD HH:mm:ss');
        params.endAt = format(subDays(endOfToDay(), delay), 'YYYY-MM-DD HH:mm:ss');
        break;
      default:
        break;
    }
    const { total } = await new CouponVerifyService(this.ctx).getVerifyList({
      pageNo: 1,
      kdtId: this.ctx.kdtId,
      pageSize: 1,
      couponType: keys[0],
      ...params,
    });
    return {
      [keyIdxMap[keys[0]]]: total,
    };
  }
}

module.exports = VerifyAdapter;
