const BaseAdaptor = require('./BaseAdaptor');
const DataOverViewService = require('../../../../services/api/ebiz/mall/DataOverViewService');

class RetailTradeAdapter extends BaseAdaptor {
  constructor(ctx) {
    super(ctx);
  }

  // 零售交易数据
  async getRetailTradeOverViewData({ isCompare, keyIdxMap = {} }) {
    if (!isCompare) {
      const { kdtId, userId } = this.ctx;
      const result = await new DataOverViewService(this.ctx).getRetailTradeOverViewData({
        kdtId,
        adminId: userId,
      });
      const { todayData, yesterdayData } = result;
      return this.getResult(keyIdxMap, todayData, yesterdayData);
    }
  }
}

module.exports = RetailTradeAdapter;
