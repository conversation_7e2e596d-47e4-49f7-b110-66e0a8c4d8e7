const BaseAdaptor = require('./BaseAdaptor');
const StatisticsReadService = require('../../../../services/api/material/materialcenter/StatisticsReadService');
const MicropageReadService = require('../../../../services/api/showcase/center/MicropageReadService');

class ShowcaseAdapter extends BaseAdaptor {
  constructor(ctx) {
    super(ctx);
  }

  async queryTencentVideoUsageStatistics({ keyIdxMap = {} }) {
    const result = await new StatisticsReadService(this.ctx).queryTencentVideoUsageStatistics({
      partnerBizType: 1,
      partnerBizId: this.ctx.kdtId,
    });
    return this.getResult(keyIdxMap, result);
  }

  async queryNumStatistics({ keyIdxMap = {} }) {
    const result = await new StatisticsReadService(this.ctx).queryNumStatistics({
      partnerBizType: 1,
      partnerBizId: this.ctx.kdtId,
    });
    return this.getResult(keyIdxMap, result);
  }

  async queryMicropageCount({ keyIdxMap = {} }) {
    const result = await new MicropageReadService(this.ctx).queryMicropageCount({
      kdtId: this.ctx.kdtId,
    });
    const keys = Object.keys(keyIdxMap);
    const data = {};
    keys.forEach(key => {
      data[keyIdxMap[key]] = result;
    });
    return data;
  }
}

module.exports = ShowcaseAdapter;
