const BaseAdaptor = require('./BaseAdaptor');
const SupplierPlatformService = require('../../../../services/api/fx/SupplierPlatformService');
const SupplierService = require('../../../../services/api/fx/SupplierService');

class FenxiaoAdapter extends BaseAdaptor {
  async payCount({ keyIdxMap = {}, isCompare }) {
    const result = await new SupplierPlatformService(this.ctx).getDashboardStatistic({
      kdtId: this.ctx.kdtId,
      option: {
        withPurchaseOrderPayNum: true,
        withRealTimeStatistic: !isCompare,
        withYesterdayStatistic: isCompare,
      },
    });
    const dataObj = isCompare ? result.yesterdayData : result.realTimeData;
    const data = {};
    const keys = Object.keys(keyIdxMap);
    keys.forEach(key => (data[keyIdxMap[key]] = dataObj && dataObj.purchaseOrderPayNum));
    return data;
  }

  async payAmount({ keyIdxMap = {}, isCompare }) {
    const result = await new SupplierPlatformService(this.ctx).getDashboardStatistic({
      kdtId: this.ctx.kdtId,
      option: {
        withPurchaseOrderPayAmount: true,
        withRealTimeStatistic: !isCompare,
        withYesterdayStatistic: isCompare,
      },
    });
    const dataObj = isCompare ? result.yesterdayData : result.realTimeData;
    const data = {};
    const keys = Object.keys(keyIdxMap);
    keys.forEach(key => (data[keyIdxMap[key]] = dataObj && dataObj.purchaseOrderPayAmount));
    return data;
  }

  async newFxSeller({ keyIdxMap = {}, isCompare }) {
    const result = await new SupplierService(this.ctx).querySellerCount(
      this.ctx.kdtId,
      isCompare ? 2 : 1
    );
    const count = isCompare ? result.yesterdayAddSellerCount : result.todayAddSellerCount;
    const data = {};
    const keys = Object.keys(keyIdxMap);
    keys.forEach(key => (data[keyIdxMap[key]] = count));
    return data;
  }

  async payedFxSeller({ keyIdxMap = {}, isCompare }) {
    const result = await new SupplierPlatformService(this.ctx).getDashboardStatistic({
      kdtId: this.ctx.kdtId,
      option: {
        withPaidFxSellerNum: true,
        withRealTimeStatistic: !isCompare,
        withYesterdayStatistic: isCompare,
      },
    });
    const dataObj = isCompare ? result.yesterdayData : result.realTimeData;
    const data = {};
    const keys = Object.keys(keyIdxMap);
    keys.forEach(key => (data[keyIdxMap[key]] = dataObj && dataObj.paidFxSellerNum));
    return data;
  }

  async creditPoint({ keyIdxMap = {} }) {
    const result = await new SupplierService(this.ctx).getFullInfo(this.ctx.kdtId);
    const data = {};
    const keys = Object.keys(keyIdxMap);
    keys.forEach(key => (data[keyIdxMap[key]] = result.score / 100));
    return data;
  }
}

module.exports = FenxiaoAdapter;
