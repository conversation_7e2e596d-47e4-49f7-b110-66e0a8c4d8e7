const BaseAdaptor = require('./BaseAdaptor');
const TeamOrderDataService = require('../../../../services/api/bigdata/datacenter/TeamOrderDataService');
const DataOverViewService = require('../../../../services/api/ebiz/mall/DataOverViewService');
const { getShopType } = require('../../common/biz-type');

class TeamAdapter extends BaseAdaptor {
  constructor(ctx) {
    super(ctx);
  }

  /**
   * 大数据支持：交易，商品，店铺，店铺级别数据
   */
  async getTeamData({ hqKdtId, keyIdxMap = {}, storeId, isCompare }) {
    if (!isCompare) {
      const { shopRole = 0 } = this.ctx;
      const shopType = getShopType(this.ctx);
      const result = await new DataOverViewService(this.ctx).getWscBaseDataOverView({
        hqKdtId,
        kdtId: this.ctx.kdtId,
        shopRole,
        shopType,
        storeId,
      });
      const { todayData, yesterdayData } = result;
      return this.getResult(keyIdxMap, todayData, yesterdayData);
    }
  }

  async getTeamOrderStatFromTrade({ storeId, keyIdxMap = {} }) {
    const result = await new TeamOrderDataService(this.ctx).getTeamOrderStatFromTrade({
      kdtId: this.ctx.kdtId,
      storeId,
    });
    return this.getResult(keyIdxMap, result);
  }
}

module.exports = TeamAdapter;
