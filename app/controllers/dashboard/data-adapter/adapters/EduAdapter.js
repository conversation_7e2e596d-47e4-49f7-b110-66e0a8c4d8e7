const BaseAdaptor = require('./BaseAdaptor');
const DashboardFacade = require('../../../../services/api/owl/pc/DashboardFacade');
const { getMultiSamRoles } = require('../../common/user-role');

class EduAdapter extends BaseAdaptor {
  constructor(ctx) {
    super(ctx);
  }

  // 教育工作台概览数据
  async getOverview({ keyIdxMap = {}, multiRoles, ...options }) {
    const { kdtId } = this.ctx;
    const keys = Object.keys(keyIdxMap);
    const multiSamRoles = getMultiSamRoles(multiRoles);
    const params = {
      keys,
      kdtId,
      multiSamRoles,
      ...options,
    };
    const result = await new DashboardFacade(this.ctx).workbenchOverviewV2(params);
    return this.getResult(keyIdxMap, result);
  }
}

module.exports = EduAdapter;
