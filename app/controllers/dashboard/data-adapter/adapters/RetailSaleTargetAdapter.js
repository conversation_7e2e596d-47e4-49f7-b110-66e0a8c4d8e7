const subDays = require('date-fns/sub_days');
const startOfToday = require('date-fns/start_of_today');
const BaseAdaptor = require('./BaseAdaptor');
const SingleOverviewDataService = require('../../../../services/api/retail/data/SingleOverviewDataService');
const { fromApp } = require('../../../../constants');

/**
 * 零售3.0 营业目标：本月已完成营业额（元），本月目标营业额（元），本月完成进度
 */
class RetailSaleTargetAdapter extends BaseAdaptor {
  constructor(ctx) {
    super(ctx);
  }

  getCommonServiceParams(delay) {
    /**
     * 零售数据入参
     * dateType：日期类型，统一使用natural_day，接口不支持实时，所以所有数据延迟一天
     * channelType：零售3.0店铺使用 all
     */
    const params = {
      dateType: 'natural_day',
      channelType: 'all',
      source: fromApp,
      startTime: subDays(startOfToday(), delay).getTime(),
      endTime: subDays(startOfToday(), delay).getTime(),
      kdtId: this.ctx.kdtId,
      adminId: this.ctx.userId,
    };
    return params;
  }

  // 营业目标：本月已完成营业额（元），本月目标营业额（元），本月完成进度
  async findSingleSalesTargetBySales({ keyIdxMap = {}, delay }) {
    const params = this.getCommonServiceParams(delay);

    const { data } = await new SingleOverviewDataService(this.ctx).findSingleSalesTargetBySales(
      params
    );
    return this.getResult(keyIdxMap, data);
  }
}

module.exports = RetailSaleTargetAdapter;
