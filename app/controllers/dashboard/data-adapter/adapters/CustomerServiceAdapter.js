const startOfToDay = require('date-fns/start_of_today');
const endOfToDay = require('date-fns/end_of_today');
const subDays = require('date-fns/sub_days');
const format = require('date-fns/format');

const BaseAdaptor = require('./BaseAdaptor');
const CsPerformanceService = require('../../../../services/api/saas/courier/CsPerformanceService');
const ReceptionistEvaluateService = require('../../../../services/api/saas/courier/ReceptionistEvaluateService');
const PerformanceSalesService = require('../../../../services/api/saas/courier/PerformanceSalesService');
const { DATA_DATE_TYPE } = require('../constants');
const { WSC_SAM_ROLE } = require('../../../../constants');
const { getBizType, BIZ_TYPE } = require('../../common/biz-type');

const AcceptKey = {
  onlineTime: {
    name: 'onlineTime',
    shopMethod: 'listShopAVGAttendanceResult',
    personalMethod: 'listAttendanceResult',
  },

  receiveAmount: {
    name: 'receiveAmount',
    shopMethod: 'getShopSummaryByKdtIdWithSelectedDate',
    personalMethod: 'getSumPerformanceItemsWithReceptionist',
  },
  // same as above
  firstReplyTime: {
    name: 'firstReplyTime',
  },
  // same as above
  averageTime: {
    name: 'averageTime',
  },
  conversationTotalAmount: {
    name: 'conversationTotalAmount',
  },

  sales: {
    name: 'sales',
    shopMethod: 'listTeamSalesResult',
    personalMethod: 'listPersonalSalesResult',
  },
  // same as above
  salesCustomerNum: {
    name: 'salesCustomerNum',
  },
  // same as above
  inquiryPaymentConversionRate: {
    name: 'inquiryPaymentConversionRate',
  },

  invitePercent: {
    name: 'invitePercent',
    shopMethod: 'getStatisticsAVG',
    personalMethod: 'getStatisticsList',
  },
  qaSolveTimePercent: {
    name: 'qaSolveTimePercent',
  },
  overallSatisfaction: {
    name: 'overallSatisfaction',
  },
};

class CustomerServiceAdapter extends BaseAdaptor {
  constructor(ctx) {
    super(ctx);
    this.ctx = ctx;
  }

  getOrSet(obj, key, defaultValue) {
    if (!obj[key]) {
      obj[key] = defaultValue;
    }
    return obj[key];
  }

  // 教育店铺客服数据兼容多角色
  getIsPersonalData(roleId, multiRoles, bizType) {
    // 售前客服
    const isSaleBeforeAdmin = WSC_SAM_ROLE.SALE_BEFORE_ADMIN === roleId;
    // 普通客服
    const isNormalCustomerAdmin = WSC_SAM_ROLE.NORMAR_CUSTOMER_ADMIN === roleId;
    let isGetPersonalData = isSaleBeforeAdmin || isNormalCustomerAdmin;

    if (bizType === BIZ_TYPE.EDU) {
      const isSuperAdmin = !!multiRoles.find(({ roleId }) => roleId === WSC_SAM_ROLE.SUPER_ADMIN);
      isGetPersonalData = !isSuperAdmin;
    }
    return { isGetPersonalData, isSaleBeforeAdmin, isNormalCustomerAdmin };
  }

  async dispatch({ keyIdxMap = {}, roleId, multiRoles, ...options }) {
    const bizType = await getBizType(this.ctx);
    const keys = Object.keys(keyIdxMap);
    const { isGetPersonalData, isSaleBeforeAdmin } = this.getIsPersonalData(
      roleId,
      multiRoles,
      bizType
    );
    const isGetSalesPersonalData = bizType === BIZ_TYPE.EDU ? isGetPersonalData : isSaleBeforeAdmin;

    const methodMap = {};
    let methodName;

    keys.forEach(key => {
      switch (key) {
        case AcceptKey.onlineTime.name:
          methodName = isGetPersonalData
            ? AcceptKey.onlineTime.personalMethod
            : AcceptKey.onlineTime.shopMethod;
          break;
        case AcceptKey.conversationTotalAmount.name:
        case AcceptKey.averageTime.name:
        case AcceptKey.firstReplyTime.name:
        case AcceptKey.receiveAmount.name:
          methodName = isGetPersonalData
            ? AcceptKey.receiveAmount.personalMethod
            : AcceptKey.receiveAmount.shopMethod;
          break;
        case AcceptKey.sales.name:
        case AcceptKey.salesCustomerNum.name:
        case AcceptKey.inquiryPaymentConversionRate.name:
          methodName = isGetSalesPersonalData
            ? AcceptKey.sales.personalMethod
            : AcceptKey.sales.shopMethod;
          break;
        case AcceptKey.invitePercent.name:
        case AcceptKey.qaSolveTimePercent.name:
        case AcceptKey.overallSatisfaction.name:
          methodName = isGetPersonalData
            ? AcceptKey.invitePercent.personalMethod
            : AcceptKey.invitePercent.shopMethod;
          break;
        default:
          break;
      }
      this.getOrSet(methodMap, methodName, {})[key] = keyIdxMap[key];
    });

    const methods = Object.keys(methodMap);
    const promises = methods.map(method => {
      return this[method]({
        keyIdxMap: methodMap[method],
        ...options,
        // eslint-disable-next-line no-console
      }).catch(e => console.log(e));
    });
    const datas = await Promise.all(promises);
    const result = {};
    datas.forEach(data => {
      Object.assign(result, data);
    });
    return result;
  }

  // 全店 -- 在线时长 pass
  async listShopAVGAttendanceResult({ dateType, keyIdxMap = {}, delay }) {
    let beginTime;
    let endTime;
    switch (dateType) {
      case DATA_DATE_TYPE.DAY:
        beginTime = subDays(startOfToDay(), delay).getTime();
        endTime = endOfToDay().getTime();
        break;
      default:
        beginTime = startOfToDay().getTime();
        endTime = endOfToDay().getTime();
        break;
    }
    const result =
      (await new CsPerformanceService(this.ctx).listShopAVGAttendanceResult(
        String(this.ctx.kdtId),
        beginTime,
        endTime
      )) || {};
    return this.getResult(keyIdxMap, result);
  }

  // 全店 -- 接待人数、平均首次响应时长、平均响应时长、会话总数
  async getShopSummaryByKdtIdWithSelectedDate({ dateType, keyIdxMap = [], delay }) {
    let beginDate;
    let endDate;
    switch (dateType) {
      case DATA_DATE_TYPE.DAY:
        beginDate = format(subDays(startOfToDay(), delay), 'YYYY-MM-DD HH:mm:ss');
        endDate = format(subDays(endOfToDay(), delay), 'YYYY-MM-DD HH:mm:ss');
        break;
      default:
        break;
    }
    const result =
      (await new CsPerformanceService(this.ctx).getShopSummaryByKdtIdWithSelectedDate(
        String(this.ctx.kdtId),
        beginDate,
        endDate
      )) || {};
    return this.getResult(keyIdxMap, result);
  }

  // 全店
  // 服务邀评率 -- invitePercent
  // 问题解决率 -- qaSolveTimePercent
  // 服务满意度 -- overallSatisfaction
  async getStatisticsAVG({ dateType, keyIdxMap = {}, delay }) {
    let beginTime;
    let endTime;
    switch (dateType) {
      case DATA_DATE_TYPE.DAY:
        beginTime = format(subDays(startOfToDay(), delay), 'YYYY-MM-DD');
        endTime = format(subDays(startOfToDay(), delay), 'YYYY-MM-DD');
        break;
      default:
        break;
    }
    const result = await new ReceptionistEvaluateService(this.ctx).getStatisticsAVG({
      kdtId: this.ctx.kdtId,
      beginTime,
      endTime,
    });
    return this.getResult(keyIdxMap, result);
  }

  // 个人
  // 服务邀评率 -- invitePercent
  // 问题解决率 -- qaSolveTimePercent
  // 服务满意度 -- overallSatisfaction
  async getStatisticsList({ dateType, keyIdxMap = {}, delay }) {
    let beginTime;
    let endTime;
    switch (dateType) {
      case DATA_DATE_TYPE.DAY:
        beginTime = format(subDays(startOfToDay(), delay), 'YYYY-MM-DD');
        endTime = format(subDays(startOfToDay(), delay), 'YYYY-MM-DD');
        break;
      default:
        break;
    }

    const results = await new ReceptionistEvaluateService(this.ctx).getStatisticsList({
      kdtId: this.ctx.kdtId,
      beginTime,
      endTime,
      page: 1,
      pageSize: 1,
      sortType: 2,
      receptionistId: this.ctx.userId,
    });

    const { data: customerAdminDataList } = results;
    return this.getResult(keyIdxMap, customerAdminDataList[0]);
  }

  // 全店 -- 销售金额、销售人数、询单付款转化率
  async listTeamSalesResult({ dateType, keyIdxMap = {}, delay }) {
    let beginTime;
    let endTime;
    switch (dateType) {
      case DATA_DATE_TYPE.DAY:
        beginTime = Math.round(subDays(startOfToDay(), delay).getTime() / 1000);
        endTime = Math.round(subDays(endOfToDay(), delay).getTime() / 1000);
        break;
      default:
        break;
    }
    const result = await new PerformanceSalesService(this.ctx).listTeamSalesResult({
      kdtId: this.ctx.kdtId,
      beginTime,
      endTime,
      type: 0,
    });
    return this.getResult(keyIdxMap, result);
  }

  // 个人
  // 平均首次响应firstReplyTime
  // 会话总数conversationTotalAmount
  // 接待人数 receiveAmount
  // 平均响应时长 averageTime
  // 服务邀评率
  async getSumPerformanceItemsWithReceptionist({ dateType, keyIdxMap = {}, delay }) {
    let beginDate;
    let endDate;
    switch (dateType) {
      case DATA_DATE_TYPE.DAY:
        beginDate = format(subDays(startOfToDay(), delay), 'YYYY-MM-DD HH:mm:ss');
        endDate = format(subDays(endOfToDay(), delay), 'YYYY-MM-DD HH:mm:ss');
        break;
      default:
        break;
    }
    const { data: datas } = await new CsPerformanceService(
      this.ctx
    ).getSumPerformanceItemsWithReceptionist(
      String(this.ctx.kdtId),
      String(this.ctx.userId),
      beginDate,
      endDate
    );
    return this.getResult(keyIdxMap, datas[0]);
  }

  // 个人
  // 在线时长 onlineTime
  async listAttendanceResult({ dateType, keyIdxMap = {}, delay }) {
    let beginTime;
    let endTime;
    switch (dateType) {
      case DATA_DATE_TYPE.DAY:
        beginTime = subDays(startOfToDay(), delay).getTime();
        endTime = subDays(endOfToDay(), delay).getTime();
        break;
      default:
        break;
    }
    const result = await new CsPerformanceService(this.ctx).listAttendanceResult(
      String(this.ctx.kdtId),
      String(this.ctx.userId),
      beginTime,
      endTime,
      1,
      1,
      1
    );
    const { data: daysData } = result;
    return this.getResult(keyIdxMap, daysData[0]);
  }

  // 个人
  // sales销售金额
  // salesCustomerNum 销售人数
  // inquiryPaymentConversionRate 询单付款转化率
  async listPersonalSalesResult({ dateType, keyIdxMap = {}, delay }) {
    let beginTime;
    let endTime;
    switch (dateType) {
      case DATA_DATE_TYPE.DAY:
        beginTime = Math.round(subDays(startOfToDay(), delay).getTime() / 1000);
        endTime = Math.round(subDays(endOfToDay(), delay).getTime() / 1000);
        break;
      default:
        break;
    }
    const result = await new CsPerformanceService(this.ctx).listPersonalSalesResult({
      kdtId: this.ctx.kdtId,
      beginTime,
      endTime,
      page: 1,
      pageSize: 1,
      accSales: 1,
      receptionist: this.ctx.userId,
    });
    const { data: daysData } = result;
    return this.getResult(keyIdxMap, daysData[0]);
  }
}

module.exports = CustomerServiceAdapter;
