const get = require('lodash/get');

class BaseAdaptor {
  constructor(ctx) {
    this.ctx = ctx;
  }

  /**
   * isFromOneService：标记当前数据对比数据是否从一个接口获取
   * @param {*} keyIdxMap
   * @param {*} realData 实时数据
   * @param {*} compareData 对比数据
   */
  getResult(keyIdxMap, realData, compareData) {
    const result = {};
    const keys = Object.keys(keyIdxMap);
    keys.forEach(key => {
      result[keyIdxMap[key]] = compareData
        ? {
            isFromOneService: true,
            realValue: get(realData, key),
            compareValue: get(compareData, key),
          }
        : get(realData, key);
    });
    return result;
  }
}

module.exports = BaseAdaptor;
