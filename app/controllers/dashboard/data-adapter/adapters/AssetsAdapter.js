const mapKeys = require('lodash/mapKeys');
const subDays = require('date-fns/sub_days');
const startOfToday = require('date-fns/start_of_today');
const BaseAdaptor = require('./BaseAdaptor');
const IndexService = require('../../../../services/api/pay/customer/IndexService');
const ShopBaseReadService = require('../../../../services/api/shopcenter/shop/ShopBaseReadService');
const SingleOverviewDataService = require('../../../../services/api/retail/data/SingleOverviewDataService');
const { fromApp } = require('../../../../constants');

class AssetsAdapter extends BaseAdaptor {
  constructor(ctx) {
    super(ctx);
  }

  getCommonServiceParams(delay) {
    /**
     * 零售数据入参
     * dateType：日期类型，统一使用natural_day，接口不支持实时，所以所有数据延迟一天
     * channelType：零售3.0店铺使用 all
     */
    const params = {
      dateType: 'natural_day',
      channelType: 'all',
      source: fromApp,
      startTime: subDays(startOfToday(), delay).getTime(),
      endTime: subDays(startOfToday(), delay).getTime(),
      kdtId: this.ctx.kdtId,
      adminId: this.ctx.userId,
    };
    return params;
  }

  // 可用店铺余额、待结算、营销资金、保证金
  // 标记资金、储值账户余额、礼品卡余额、广告投放金余额
  async getCustomerStatistic({ _, keyIdxMap = {} }) {
    const keys = Object.keys(keyIdxMap);
    const { paymentClientId } = await new ShopBaseReadService(this.ctx).getPaymentByKdtId(
      this.ctx.kdtId
    );
    if (paymentClientId) {
      const result = await new IndexService(this.ctx).queryAccountSummary({
        userNo: `${paymentClientId}`,
        accountTypes: Object.keys(keyIdxMap).join(','),
      });
      // 将result数据key转为UpperCase，payInvokeV2中会将result中key转驼峰导致首字母为小写
      // aVAILABLE -> AVAILABLE
      const upperCaseResult = mapKeys(result, (_val, key) => key.toUpperCase());
      const data = {};
      keys.forEach(
        key => (data[keyIdxMap[key]] = upperCaseResult[key] && +upperCaseResult[key].balance * 100)
      );
      return data;
    }
  }

  // 财务数据：新增储值金额（元）
  async findRealTimeOverViewData({ keyIdxMap = {}, delay }) {
    const data = await new SingleOverviewDataService(this.ctx).findRealTimeOverViewData({
      ...this.getCommonServiceParams(delay),
      excludeTrendIndex: true,
    });
    return this.getResult(keyIdxMap, data);
  }
}

module.exports = AssetsAdapter;
