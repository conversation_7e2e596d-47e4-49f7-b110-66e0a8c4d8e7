const startOfMonth = require('date-fns/start_of_month');
const endOfMonth = require('date-fns/end_of_month');
const format = require('date-fns/format');
const divide = require('lodash/divide');
const { checkPureWscSingleStore, checkChainStore } = require('@youzan/utils-shop');

const DataAdapter = require('../index');
const HomePageQuerySynchroService = require('../../../../services/api/bigdata/datacenter/HomePageQuerySynchroService');
const CardIndicatorTargetSetService = require('../../../../services/api/bigdata/jarvis/CardIndicatorTargetSetService');
const { fromApp } = require('../../../../constants');

const DATE_TYPE = {
  TODAY: 0,
  NATURAL_DAY: 1,
  NATURAL_WEEK: 2,
  NATURAL_MONTH: 3,
  NATURAL_QUARTER: 7,
};

class SaleTargetAdapter extends DataAdapter {
  constructor(ctx) {
    super(ctx);
  }

  async dispatch({ keyIdxMap }) {
    const keys = Object.keys(keyIdxMap);
    let methods = [];
    const needComputeRate = keys.some(key => {
      switch (key) {
        case 'saleExpected':
          methods.push(this.listSaleExpectedTargets);
          return false;
        case 'currentPeriod':
          methods.push(this.getSaleTarget);
          return false;
        case 'targetRate':
          methods = [this.listSaleExpectedTargets, this.getSaleTarget];
          return true;
      }
    });
    const promises = methods.map(method => {
      return method.call(this);
    });
    const datas = await Promise.all(promises);
    const result = {};
    datas.forEach(data => {
      Object.assign(result, data);
    });
    if (needComputeRate) {
      const { saleExpected, currentPeriod } = result;
      result.targetRate = saleExpected ? divide(currentPeriod / saleExpected) : null;
    }
    return this.getResult(keyIdxMap, result);
  }

  async getSaleTarget() {
    const hqKdtId = this.getHqKdtId();
    const shopInfo = this.ctx.getState('shopInfo');
    const params = {
      // 取当月的已完成数据dateType需要NATURAL_DAY
      dateType: DATE_TYPE.NATURAL_MONTH,
      indicatorIdList: [1],
      startDay: format(startOfMonth(new Date()), 'YYYY-MM-DD'),
      endDay: format(endOfMonth(new Date()), 'YYYY-MM-DD'),
    };
    const { homeIndicatorDataModelList: result } = await new HomePageQuerySynchroService(this.ctx)
      .doQueryDataByIndicators({
        fromApp,
        kdtId: this.ctx.kdtId,
        hqKdtId: checkChainStore(shopInfo) ? hqKdtId : this.ctx.kdtId,
        userId: this.ctx.userId,
        shopType: checkPureWscSingleStore(shopInfo) ? 0 : shopInfo.shopType,
        ...params,
      })
      .catch(err => {
        this.ctx.logger.error(err.message, err, err.extra);
        return {};
      });
    const { indicatorValue: currentPeriod } = (result || [])[0] || {};
    return { currentPeriod };
  }

  async listSaleExpectedTargets() {
    const now = new Date();
    const hqKdtId = this.getHqKdtId();
    const shopInfo = this.ctx.getState('shopInfo');
    const result = await new CardIndicatorTargetSetService(this.ctx)
      .queryIndicatorTarget({
        fromApp,
        hqKdtId: checkChainStore(shopInfo) ? hqKdtId : this.ctx.kdtId,
        kdtId: this.ctx.kdtId,
        adminId: this.ctx.userId,
        // 支付金额的指标Id
        indicatorId: 1,
        targetDay: +format(startOfMonth(now), 'YYYYMMDD'),
        dateType: DATE_TYPE.NATURAL_MONTH,
      })
      .catch(err => {
        this.ctx.logger.error(err.message, err, err.extra);
      });
    return { saleExpected: result };
  }
}

module.exports = SaleTargetAdapter;
