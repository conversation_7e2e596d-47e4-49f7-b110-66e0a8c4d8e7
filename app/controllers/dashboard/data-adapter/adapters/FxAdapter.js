const BaseAdaptor = require('./BaseAdaptor');
const WscPlatformService = require('../../../../services/api/fx/trade/WscPlatformService');

class FxAdapter extends BaseAdaptor {
  constructor(ctx) {
    super(ctx);
  }

  getOrSet(obj, key, defaultValue) {
    if (!obj[key]) {
      obj[key] = defaultValue;
    }
    return obj[key];
  }

  async getFxSupplierProfit({ keyIdxMap = {}, delay }) {
    const result = await new WscPlatformService(this.ctx).getFxSupplierProfit({
      supplierKdtId: this.ctx.kdtId,
      withRealTimeProfit: !delay,
      withOfflineProfit: !!delay,
    });
    const keys = Object.keys(keyIdxMap);
    const delayKeyMap = {
      0: 'realTimeSupplierProfit',
      1: 'offlineSupplierProfit',
    };

    const data = {};
    keys.forEach(key => {
      data[keyIdxMap[key]] = result[delayKeyMap[delay]];
    });
    return data;
  }
}

module.exports = FxAdapter;
