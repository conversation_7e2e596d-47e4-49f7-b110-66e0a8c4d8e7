const format = require('date-fns/format');
const subDays = require('date-fns/sub_days');
const startOfToday = require('date-fns/start_of_today');
const uuidv4 = require('uuid/v4');
const BaseAdaptor = require('./BaseAdaptor');
const NewCustomerService = require('../../../../services/api/bigdata/datacenter/NewCustomerService');
const OneService = require('../../../../services/api/bigdata/datacenter/OneService');
const CustomerListCrowdService = require('../../../../services/api/scrm/api/CustomerListCrowdService');
const { DATA_DATE_TYPE } = require('../constants');
const { fromApp } = require('../../../../constants');

// 客户数据
class CustomerAdapter extends BaseAdaptor {
  constructor(ctx) {
    super(ctx);
  }

  async getCustomerStatistic({ dateType, keyIdxMap = {}, delay }) {
    const DATE_TYPE = {
      TODAY: 0, // 实时数据
      NATURAL_DAY: 1, // 自然日
      NATURAL_WEEK: 2, // 自然周
      NATURAL_MONTH: 3, // 孜然月
      LAST_SEVEN_DAY: 4,
      LAST_THIRTY_DAY: 5,
      SELF_DEFINE: 6,
      NATURAL_QUARTER: 7,
      NATURAL_DAY_WITH_HOUR: 8,
    };
    const params = {};
    switch (dateType) {
      case DATA_DATE_TYPE.DAY:
        params.dateType = delay ? DATE_TYPE.NATURAL_DAY : DATE_TYPE.TODAY;
        params.timeParam = { currentDay: format(subDays(startOfToday(), delay), 'YYYYMMDD') };
        break;
      default:
        break;
    }

    const result = await new NewCustomerService(this.ctx).getCustomerStatistic({
      canalType: 10,
      fromApp,
      kdtId: this.ctx.kdtId,
      ...params,
    });
    return this.getResult(keyIdxMap, result);
  }

  async getCrowdList({ keyIdxMap = {} }) {
    // if (delay) {
    //   return await this.getCustomerStatistic({ dateType, keyIdxMap, delay });
    // }
    const result = await new CustomerListCrowdService(this.ctx).getCrowdList(this.ctx.kdtId);

    return this.getResult(keyIdxMap, result[0]);
  }

  async getDataByOneService({ keyIdxMap = {}, delay, isCompare }) {
    const requestId = uuidv4();
    const params = {
      kdtId: this.ctx.kdtId,
      date: isCompare ? format(subDays(startOfToday(), delay), 'YYYYMMDD') : null,
    };
    const apiName = isCompare ? 'getOfflineNewMemberUv' : 'getRealtimeTeamData';
    const result = await new OneService(this.ctx).query({
      caller: 'wsc_dashboard',
      apiName,
      requestId,
      params,
    });
    return this.getResult(keyIdxMap, result[0]);
  }
}

module.exports = CustomerAdapter;
