// const format = require('date-fns/format');
const subDays = require('date-fns/sub_days');
const startOfToday = require('date-fns/start_of_today');
const BaseAdaptor = require('./BaseAdaptor');
const CustomerDataService = require('../../../../services/api/retail/data/CustomerDataService');
const { fromApp } = require('../../../../constants');

/**
 * 零售3.0 客户数据
 */
class RetialCustomerAdapter extends BaseAdaptor {
  constructor(ctx) {
    super(ctx);
  }

  formatRetailData(data) {
    const currentData = {};
    const lastData = {};
    if (Array.isArray(data)) {
      data.forEach(item => {
        const { bizType, value, preValue } = item;
        if (bizType) {
          currentData[bizType] = value;
          lastData[bizType] = preValue;
        }
      });
    }
    return { currentData, lastData };
  }

  getCommonServiceParams(delay) {
    /**
     * 零售数据入参
     * dateType：日期类型，统一使用natural_day，接口不支持实时，所以所有数据延迟一天
     * withTrend：设置为true，startTime和endTime会取日期
     * channelType：零售3.0店铺使用 all
     */
    const params = {
      dateType: 'natural_day',
      withTrend: true,
      channelType: 'all',
      source: fromApp,
      startTime: subDays(startOfToday(), delay).getTime(),
      endTime: subDays(startOfToday(), delay).getTime(),
      kdtId: this.ctx.kdtId,
      adminId: this.ctx.userId,
    };

    return params;
  }

  // 新增客户数,新增会员数
  async getCustomerStatisticData({ isCompare, keyIdxMap = {}, delay }) {
    if (!isCompare) {
      const params = this.getCommonServiceParams(delay);

      const { data } = await new CustomerDataService(this.ctx).getCustomerStatisticData(params);
      const { currentData, lastData } = this.formatRetailData(data);
      return this.getResult(keyIdxMap, currentData, lastData);
    }
  }

  // 支付会员数
  async getCustomerDealData({ isCompare, keyIdxMap = {}, delay }) {
    if (!isCompare) {
      const params = this.getCommonServiceParams(delay);

      const { data } = await new CustomerDataService(this.ctx).getCustomerDealData(params);
      const { currentData, lastData } = this.formatRetailData(data);
      return this.getResult(keyIdxMap, currentData, lastData);
    }
  }

  // 累计会员数,累计客户数
  async getTotalCustomerStatisticData({ isCompare, keyIdxMap = {}, delay, storeKdtId = 0 }) {
    if (!isCompare) {
      const params = this.getCommonServiceParams(delay);
      if (storeKdtId) {
        params.storeKdtId = storeKdtId;
      }

      const { data } = await new CustomerDataService(this.ctx).getTotalCustomerStatisticData(
        params
      );
      const { currentData, lastData } = this.formatRetailData(data);
      return this.getResult(keyIdxMap, currentData, lastData);
    }
  }
}

module.exports = RetialCustomerAdapter;
