const BaseAdaptor = require('./BaseAdaptor');
const StatisticService = require('../../../../services/api/bigdata/dspplatform/StatisticService');
const { DATA_DATE_TYPE } = require('../constants');
const format = require('date-fns/format');

class AdAdapter extends BaseAdaptor {
  constructor(ctx) {
    super(ctx);
  }

  // 广告投放中、广告待审核
  async wscGetAdStatusMap({ keyIdxMap = {} }) {
    const result = await new StatisticService(this.ctx).wscGetAdStatusMap(this.ctx.kdtId);
    return this.getResult(keyIdxMap, result);
  }

  // 广告曝光量、广告点击量、广告点击率、点击均价、广告花费
  // 30日下单数、30日下单金额、30日下单ROI
  async wscGlobalAnalyse({ dateType, keyIdxMap = {} }) {
    let day;
    switch (dateType) {
      case DATA_DATE_TYPE.DAY:
        day = format(Date.now(), 'YYYYMMDD');
        break;
      default:
        day = format(Date.now(), 'YYYYMMDD');
        break;
    }
    const result = await new StatisticService(this.ctx).wscGlobalAnalyse({
      kdtId: this.ctx.kdtId,
      startDay: day,
      endDay: day,
    });
    return this.getResult(keyIdxMap, result);
  }
}

module.exports = AdAdapter;
