const BaseAdaptor = require('./BaseAdaptor');
const DataOverViewService = require('../../../../services/api/ebiz/mall/DataOverViewService');

class RetailGoodsAdapter extends BaseAdaptor {
  constructor(ctx) {
    super(ctx);
  }

  // 零售商品&店铺数据
  async getRetailGoodsAndShopOverViewData({ isCompare, keyIdxMap = {} }) {
    if (!isCompare) {
      const { kdtId, userId } = this.ctx;
      const result = await new DataOverViewService(this.ctx).getRetailGoodsAndShopOverViewData({
        kdtId,
        adminId: userId,
      });
      const { todayData, yesterdayData } = result;
      return this.getResult(keyIdxMap, todayData, yesterdayData);
    }
  }
}

module.exports = RetailGoodsAdapter;
