const get = require('lodash/get');
const { DATA_TYPE, DATA_DATE_TYPE } = require('./constants');
const { WSC_SAM_ROLE } = require('../../../constants');
const MultiStoreAdminService = require('@youzan/wsc-pc-base/app/services/multistore/MultiStoreAdminService');
const BaseAdaptor = require('./adapters/BaseAdaptor');

class DataAdapter extends BaseAdaptor {
  constructor(ctx) {
    super();
    this.ctx = ctx;
    this.adapters = {};
  }

  getHqKdtId() {
    const shopInfo = this.ctx.getState('shopInfo') || {};
    const { rootKdtId } = shopInfo;
    return rootKdtId;
  }

  // 根据adapter name获取adapter实例
  getAdapter(name) {
    if (!this.adapters[name]) {
      const AdapterClass = require(`./adapters/${name}`);
      this.adapters[name] = new AdapterClass(this.ctx);
    }
    return this.adapters[name];
  }

  // 合并数据要发起的请求
  generateMethodsMap(types) {
    const nowMethodsMap = {};
    const cmpMethodsMap = {};

    // idx用来标记数据在最终返回的数组中应该存放的位置
    types.forEach((type, idx) => {
      const data = DATA_TYPE[type];
      const {
        key,
        adapter,
        method,
        dateType = DATA_DATE_TYPE.REAL_TIME,
        needCompare,
        compareDateType,
        delay = 0,
      } = data;
      const methodKey = `${adapter}.${method}.${dateType}.${delay}`;
      if (!nowMethodsMap[methodKey]) {
        nowMethodsMap[methodKey] = { [key]: idx };
      } else {
        nowMethodsMap[methodKey][key] = idx;
      }

      const finalNeedCompare =
        typeof needCompare === 'function' ? needCompare(this.ctx) : needCompare;

      if (finalNeedCompare || compareDateType) {
        const cmpMethodKey = `${adapter}.${method}.${compareDateType || dateType}.${delay + 1}`;
        if (!cmpMethodsMap[cmpMethodKey]) {
          cmpMethodsMap[cmpMethodKey] = { [key]: idx };
        } else {
          cmpMethodsMap[cmpMethodKey][key] = idx;
        }
      }
    });

    return {
      nowMethodsMap,
      cmpMethodsMap,
    };
  }

  // 发起数据请求
  async getMethodsResult(methodsMap, methodParams) {
    const nowMethods = Object.keys(methodsMap);

    const promises = nowMethods.map(nowMethodKey => {
      const [adapter, method, dateType, delay] = nowMethodKey.split('.');
      const adapterIns = this.getAdapter(adapter);

      return adapterIns[method]({
        dateType: +dateType,
        keyIdxMap: methodsMap[nowMethodKey],
        delay: +delay,
        ...methodParams,
        // eslint-disable-next-line no-console
      }).catch(e => console.log(e));
    });

    const results = await Promise.all(promises);

    const data = {};
    // result是形如 { idx: value } 的对象
    results.forEach(result => {
      Object.assign(data, result);
    });
    // 合并后的data { 0: value0, 1: value1, ...}
    return data;
  }

  capitalizeFirstLetter(word) {
    return word.charAt(0).toUpperCase() + word.slice(1);
  }

  buildBusinessData(types, business) {
    const data = types.map(type => {
      const { delay = 0, dateType = 0, needCompare, compareDateType, unitType, key } = DATA_TYPE[
        type
      ];

      const typeName = this.capitalizeFirstLetter(key);
      const todayDataName = `today${typeName}`;
      const yesterdayDataName = `yesterday${typeName}`;

      const realValue = this.filterData(get(business, todayDataName));

      const finalNeedCompare =
        typeof needCompare === 'function' ? needCompare(this.ctx) : needCompare;

      // 是否需要对比数据
      const needCompareValue = finalNeedCompare || compareDateType;
      let cmpValue;
      if (needCompareValue) {
        cmpValue = this.filterData(get(business, yesterdayDataName));
      }

      return {
        type,
        delay,
        dateType,
        value: realValue,
        compareDateType,
        cmpValue,
        unitType,
      };
    });
    return data;
  }

  buildUpData(types, needCompareData, nowResult, cmpResult) {
    const data = types.map((type, index) => {
      const { delay = 0, dateType = 0, needCompare, compareDateType, unitType } = DATA_TYPE[type];

      const realData = get(nowResult, `${index}`);
      const isFromOneService = get(realData, 'isFromOneService');

      const realValue = isFromOneService
        ? this.filterData(get(realData, 'realValue'))
        : this.filterData(realData);

      const finalNeedCompare =
        typeof needCompare === 'function' ? needCompare(this.ctx) : needCompare;

      // 是否需要对比数据
      const needCompareValue = needCompareData && (finalNeedCompare || compareDateType);
      let cmpValue;
      if (needCompareValue) {
        cmpValue = isFromOneService
          ? this.filterData(get(realData, 'compareValue'))
          : this.filterData(get(cmpResult, `${index}`));
      }

      return {
        type,
        delay,
        dateType,
        value: realValue,
        compareDateType,
        cmpValue,
        unitType,
      };
    });
    return data;
  }

  // controller直接调用的获取数据的接口
  async getData(types, roleId, multiRoles, needCompareData, storeKdtId) {
    const { nowMethodsMap, cmpMethodsMap } = this.generateMethodsMap(types);
    let storeId = null;
    const { userId } = this.ctx;
    if (roleId === WSC_SAM_ROLE.NODE_ADMIN) {
      storeId = await new MultiStoreAdminService(this.ctx).getStoreIdByAdminIdAndKdtId({
        kdtId: this.ctx.kdtId,
        adminId: userId,
      });
    }

    const commonParams = {
      hqKdtId: this.getHqKdtId(),
      storeId,
      roleId,
      multiRoles,
      userId,
      storeKdtId,
    };

    const promises = [this.getMethodsResult(nowMethodsMap, commonParams)];

    needCompareData &&
      promises.push(
        this.getMethodsResult(cmpMethodsMap, {
          ...commonParams,
          isCompare: true,
        })
      );
    const [nowResult, cmpResult] = await Promise.all(promises);

    // 装配数据
    const result = this.buildUpData(types, needCompareData, nowResult, cmpResult);
    return result;
  }

  filterData(data) {
    const exceptionData = '--';
    if (data === null) {
      return exceptionData;
    }
    data = +data;
    if (!isNaN(data)) {
      return data;
    } else {
      return exceptionData;
    }
  }
}

module.exports = DataAdapter;
