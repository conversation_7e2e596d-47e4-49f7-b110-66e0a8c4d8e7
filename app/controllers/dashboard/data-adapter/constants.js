const { checkRetailSingleStore, checkUnifiedShop } = require('@youzan/utils-shop');

/* eslint-disable camelcase */

// 数据时间类型
const DATA_DATE_TYPE = {
  REAL_TIME: 0, // 今日实时数据
  DAY: 1, // 自然天
  WEEK: 2, // 自然周
  MONTH: 3, // 自然月
  LAST_SEVEN_DAY: 4, // 最近7天
  LAST_THIRTY_DAY: 5, // 最近30天
  SELF_DEFINE: 6, // 自定义日期时间段
  YESTERDAY: 8, // 昨日数据
};

// 数据单位类型
const UNIT_TYPE = {
  CENT: 1, // 分
  PERCENT_RATE: 4, // 比率值
  MILLISECS: 7, // 毫秒
  SECS: 8, // 秒
  BYTE: 10, // 字节
  RATE: 15, // 评分
};

/**
 * 数据信息
 * key为数据的类型，是唯一type，由后端定义
 * value.key为接口返回的该数据的字段名
 * value.dateType为获取该数据的时间维度
 * value.delay 部分接口无法获取实时数据，只能获取历史数据
 *             delay表示以dateType为单位的延期时间
 * needCompare 描述该数据是否需要对比数据
 * adapter和method 为获取该数据的class和对应的方法
 */
const DATA_TYPE = {
  /** 交易数据 */
  // 待发货订单数
  dash_ov_order_back_order: {
    key: 'toSendOrderCount',
    adapter: 'TeamAdapter',
    method: 'getTeamOrderStatFromTrade',
  },
  // 维权订单数
  dash_ov_order_refunds_num: {
    key: 'feedbackOrderCount',
    adapter: 'TeamAdapter',
    method: 'getTeamOrderStatFromTrade',
  },
  // 支付订单数
  dash_ov_order_pay_num: {
    key: 'paidOrderCnt',
    adapter: 'TeamAdapter',
    method: 'getTeamData',
    dateType: DATA_DATE_TYPE.DAY,
    needCompare: true,
  },
  // 支付金额（元）
  dash_ov_order_pay_amount: {
    key: 'paidAmt',
    adapter: 'TeamAdapter',
    method: 'getTeamData',
    dateType: DATA_DATE_TYPE.DAY,
    needCompare: true,
    unitType: UNIT_TYPE.CENT,
  },
  // 支付人数
  dash_ov_order_pay_uv: {
    key: 'paidUv',
    adapter: 'TeamAdapter',
    method: 'getTeamData',
    dateType: DATA_DATE_TYPE.DAY,
    needCompare: true,
  },
  // 客单价（元）
  dash_ov_customer_average: {
    key: 'avgPrice',
    adapter: 'TeamAdapter',
    method: 'getTeamData',
    dateType: DATA_DATE_TYPE.DAY,
    needCompare: true,
    unitType: UNIT_TYPE.CENT,
  },
  // 访问-支付转化率
  dash_ov_conversion_rate: {
    key: 'convertRate',
    adapter: 'TeamAdapter',
    method: 'getTeamData',
    dateType: DATA_DATE_TYPE.DAY,
    needCompare: true,
    unitType: UNIT_TYPE.PERCENT_RATE,
  },
  // 供货金额
  dash_ov_order_fx_supplier_amount: {
    key: 'SupplierProfit',
    adapter: 'FxAdapter',
    method: 'getFxSupplierProfit',
    needCompare: true,
    unitType: UNIT_TYPE.CENT,
  },

  /** 销售目标 */
  // 本月目标（元）
  dash_ov_month_target_: {
    key: 'saleExpected',
    adapter: 'SaleTargetAdapter',
    method: 'dispatch',
    unitType: UNIT_TYPE.CENT,
  },
  // 本月已完成（元）
  dash_ov_month_target_done: {
    key: 'currentPeriod',
    adapter: 'SaleTargetAdapter',
    method: 'dispatch',
    dateType: DATA_DATE_TYPE.DAY,
    unitType: UNIT_TYPE.CENT,
  },
  // 完成进度
  dash_ov_month_target_rate: {
    key: 'targetRate',
    adapter: 'SaleTargetAdapter',
    method: 'dispatch',
    unitType: UNIT_TYPE.PERCENT_RATE,
  },

  /** 店铺数据 */
  // 访客数
  dash_ov_uv: {
    key: 'uv',
    adapter: 'TeamAdapter',
    method: 'getTeamData',
    dateType: DATA_DATE_TYPE.DAY,
    needCompare: true,
  },
  // 浏览量
  dash_ov_pv: {
    key: 'pv',
    adapter: 'TeamAdapter',
    method: 'getTeamData',
    dateType: DATA_DATE_TYPE.DAY,
    needCompare: true,
  },

  /** 商品数据 */
  // 在架商品数
  dash_ov_item_onsale: {
    key: 'onSaleSpuCnt',
    adapter: 'TeamAdapter',
    method: 'getTeamData',
    dateType: DATA_DATE_TYPE.DAY,
    needCompare: true,
  },
  // 被访问商品数
  dash_ov_item_visited_goods: {
    key: 'visitsSpuCnt',
    adapter: 'TeamAdapter',
    method: 'getTeamData',
    dateType: DATA_DATE_TYPE.DAY,
    needCompare: true,
  },
  // 动销商品数
  dash_ov_item_dynamic_cnt: {
    key: 'soldSpuCnt',
    adapter: 'TeamAdapter',
    method: 'getTeamData',
    dateType: DATA_DATE_TYPE.DAY,
    needCompare: true,
  },
  // 商品访客数
  dash_ov_item_visited_uv: {
    key: 'goodsDetailUv',
    adapter: 'TeamAdapter',
    method: 'getTeamData',
    dateType: DATA_DATE_TYPE.DAY,
    needCompare: true,
  },
  // 商品曝光量
  dash_ov_item_expose_cnt: {
    key: 'goodsExposePv',
    adapter: 'TeamAdapter',
    method: 'getTeamData',
    dateType: DATA_DATE_TYPE.DAY,
    needCompare: true,
  },
  // 商品浏览量
  dash_ov_item_pv: {
    key: 'goodsDetailPv',
    adapter: 'TeamAdapter',
    method: 'getTeamData',
    dateType: DATA_DATE_TYPE.DAY,
    needCompare: true,
  },
  // 加购件数
  dash_ov_item_add_cart_cnt: {
    key: 'addCartSkuCnt',
    adapter: 'TeamAdapter',
    method: 'getTeamData',
    dateType: DATA_DATE_TYPE.DAY,
    needCompare: true,
  },
  // 下单件数
  dash_ov_item_order_cnt: {
    key: 'orderSkuCnt',
    adapter: 'TeamAdapter',
    method: 'getTeamData',
    dateType: DATA_DATE_TYPE.DAY,
    needCompare: true,
  },
  // 支付件数
  dash_ov_item_pay_num: {
    key: 'paidSkuCnt',
    adapter: 'TeamAdapter',
    method: 'getTeamData',
    dateType: DATA_DATE_TYPE.DAY,
    needCompare: true,
  },

  /** 客户数据 */
  // 累计粉丝数
  dash_ov_scm_total_fans: {
    key: 'totalFans',
    adapter: 'CustomerAdapter',
    method: 'getCustomerStatistic',
    dateType: DATA_DATE_TYPE.DAY,
    needCompare: true,
    delay: 1,
  },
  // 累计客户数
  dash_ov_total_customer: {
    key: 'customerNum',
    adapter: 'CustomerAdapter',
    method: 'getCrowdList',
    dateType: DATA_DATE_TYPE.DAY,
  },
  // 累计会员数
  dash_ov_total_member: {
    key: 'totalMember',
    adapter: 'CustomerAdapter',
    method: 'getCustomerStatistic',
    dateType: DATA_DATE_TYPE.DAY,
    needCompare: true,
    delay: 1,
  },
  // 新增客户数（成交客户数）
  dash_ov_new_payed_customer: {
    key: 'payedCustomer',
    adapter: 'CustomerAdapter',
    method: 'getCustomerStatistic',
    dateType: DATA_DATE_TYPE.DAY,
    needCompare: true,
    delay: 1,
  },
  // 新增会员数
  dash_ov_new_member: {
    key: 'newMemberUv',
    adapter: 'CustomerAdapter',
    method: 'getDataByOneService',
    dateType: DATA_DATE_TYPE.DAY,
    needCompare: true,
  },

  /** 客服服务  */
  // 在线时长
  dash_ov_im_active_time: {
    key: 'onlineTime',
    adapter: 'CustomerServiceAdapter',
    method: 'dispatch',
    dateType: DATA_DATE_TYPE.DAY,
    delay: 1,
    needCompare: true,
    unitType: UNIT_TYPE.MILLISECS,
  },
  // 接待人数
  dash_ov_im_recept_amount: {
    key: 'receiveAmount',
    adapter: 'CustomerServiceAdapter',
    method: 'dispatch',
    dateType: DATA_DATE_TYPE.DAY,
    delay: 1,
    needCompare: true,
  },
  // 平均首次响应时长
  dash_ov_im_first_reply_time: {
    key: 'firstReplyTime',
    adapter: 'CustomerServiceAdapter',
    method: 'dispatch',
    dateType: DATA_DATE_TYPE.DAY,
    delay: 1,
    needCompare: true,
    unitType: UNIT_TYPE.SECS,
  },
  // 平均响应时长
  dash_ov_im_average_time: {
    key: 'averageTime',
    adapter: 'CustomerServiceAdapter',
    method: 'dispatch',
    dateType: DATA_DATE_TYPE.DAY,
    delay: 1,
    needCompare: true,
    unitType: UNIT_TYPE.SECS,
  },
  // 会话总数
  dash_ov_im_conversation_num: {
    key: 'conversationTotalAmount',
    adapter: 'CustomerServiceAdapter',
    method: 'dispatch',
    dateType: DATA_DATE_TYPE.DAY,
    delay: 1,
    needCompare: true,
  },
  // 服务邀评率
  dash_ov_im_invite_percent: {
    key: 'invitePercent',
    adapter: 'CustomerServiceAdapter',
    method: 'dispatch',
    dateType: DATA_DATE_TYPE.DAY,
    delay: 1,
    needCompare: true,
    unitType: UNIT_TYPE.PERCENT_RATE,
  },
  // 问题解决率
  dash_ov_im_qa_solve_rate: {
    key: 'qaSolveTimePercent',
    adapter: 'CustomerServiceAdapter',
    method: 'dispatch',
    dateType: DATA_DATE_TYPE.DAY,
    delay: 1,
    needCompare: true,
    unitType: UNIT_TYPE.PERCENT_RATE,
  },
  // 服务满意度
  dash_ov_im_overall_static: {
    key: 'overallSatisfaction',
    adapter: 'CustomerServiceAdapter',
    method: 'dispatch',
    dateType: DATA_DATE_TYPE.DAY,
    delay: 1,
    needCompare: true,
    unitType: UNIT_TYPE.RATE,
  },
  // 销售金额
  dash_ov_sales: {
    key: 'sales',
    adapter: 'CustomerServiceAdapter',
    method: 'dispatch',
    dateType: DATA_DATE_TYPE.DAY,
    delay: 1,
    needCompare: true,
    unitType: UNIT_TYPE.CENT,
  },
  // 销售人数
  dash_ov_sales_customers: {
    key: 'salesCustomerNum',
    adapter: 'CustomerServiceAdapter',
    method: 'dispatch',
    dateType: DATA_DATE_TYPE.DAY,
    delay: 1,
    needCompare: true,
  },
  // 询单付款转化率
  dash_ov_inquiry_pay_rate: {
    key: 'inquiryPaymentConversionRate',
    adapter: 'CustomerServiceAdapter',
    method: 'dispatch',
    dateType: DATA_DATE_TYPE.DAY,
    delay: 3,
    needCompare: true,
    unitType: UNIT_TYPE.PERCENT_RATE,
  },

  /** 财务数据 */
  // 可用店铺余额(元)
  dash_ov_asset_available: {
    key: 'AVAILABLE',
    adapter: 'AssetsAdapter',
    method: 'getCustomerStatistic',
    unitType: UNIT_TYPE.CENT,
  },
  // 待结算(元)
  dash_ov_asset_wait_to_settle: {
    key: 'WAIT_TO_SETTLE',
    adapter: 'AssetsAdapter',
    method: 'getCustomerStatistic',
    unitType: UNIT_TYPE.CENT,
  },
  // 营销资金(元)
  dash_ov_asset_marketing: {
    key: 'MARKETING_ACCT',
    adapter: 'AssetsAdapter',
    method: 'getCustomerStatistic',
    unitType: UNIT_TYPE.CENT,
  },
  // 保证金（元）pass
  dash_ov_asset_deposit: {
    key: 'DEPOSIT',
    adapter: 'AssetsAdapter',
    method: 'getCustomerStatistic',
    unitType: UNIT_TYPE.CENT,
  },
  // 标记资金（元）pass
  dash_ov_asset_mark_pay: {
    key: 'MARK_PAY_ACCT',
    adapter: 'AssetsAdapter',
    method: 'getCustomerStatistic',
    unitType: UNIT_TYPE.CENT,
  },
  // 储值账户余额
  dash_ov_value_card: {
    key: 'VALUE_CARD',
    adapter: 'AssetsAdapter',
    method: 'getCustomerStatistic',
    unitType: UNIT_TYPE.CENT,
  },
  // 礼品卡余额(元)
  dash_ov_asset_gift_card: {
    key: 'GIFT_CARD',
    adapter: 'AssetsAdapter',
    method: 'getCustomerStatistic',
    unitType: UNIT_TYPE.CENT,
  },
  // 广告投放金余额(元)
  dash_ov_asset_dsp: {
    key: 'DSP',
    adapter: 'AssetsAdapter',
    method: 'getCustomerStatistic',
    unitType: UNIT_TYPE.CENT,
  },

  /** 美工数据 */
  // 微页面数量
  dash_ov_feature_num: {
    key: 'featureNum',
    adapter: 'ShowcaseAdapter',
    method: 'queryMicropageCount',
  },
  // 图片数量
  dash_ov_material_picture_num: {
    key: 'usedImageCount',
    adapter: 'ShowcaseAdapter',
    method: 'queryNumStatistics',
  },
  // 语音数量
  dash_ov_material_audio_num: {
    key: 'usedAudioCount',
    adapter: 'ShowcaseAdapter',
    method: 'queryNumStatistics',
  },
  // 已上传视频总和
  dash_ov_material_video_num: {
    key: 'videoCount',
    adapter: 'ShowcaseAdapter',
    method: 'queryTencentVideoUsageStatistics',
  },
  // 已使用流量
  dash_ov_material_video_used_traffic: {
    key: 'usedTraffic',
    adapter: 'ShowcaseAdapter',
    method: 'queryTencentVideoUsageStatistics',
    unitType: UNIT_TYPE.BYTE,
  },
  // 已使用存储空间
  dash_ov_material_video_used_capacity: {
    key: 'usedStorage',
    adapter: 'ShowcaseAdapter',
    method: 'queryTencentVideoUsageStatistics',
    unitType: UNIT_TYPE.BYTE,
  },

  /** 广告效果 */
  // 广告投放中
  dash_ov_dsp_popularize: {
    key: '投放中',
    adapter: 'AdAdapter',
    method: 'wscGetAdStatusMap',
  },
  // 广告待审核
  dash_ov_dsp_audit: {
    key: '待审核',
    adapter: 'AdAdapter',
    method: 'wscGetAdStatusMap',
  },
  // 广告投放金余额（元）
  dash_ov_dsp_balance: {
    key: 'DSP',
    adapter: 'AssetsAdapter',
    method: 'getCustomerStatistic',
    unitType: UNIT_TYPE.CENT,
  },
  // 广告曝光量,昨天
  dash_ov_dsp_activation: {
    key: 'impression',
    adapter: 'AdAdapter',
    method: 'wscGlobalAnalyse',
    dateType: DATA_DATE_TYPE.DAY,
  },
  // 广告点击量 昨日
  dash_ov_dsp_click_num: {
    key: 'click',
    adapter: 'AdAdapter',
    method: 'wscGlobalAnalyse',
    dateType: DATA_DATE_TYPE.DAY,
  },
  // 广告点击率
  dash_ov_dsp_click_rate: {
    key: 'ctr',
    adapter: 'AdAdapter',
    method: 'wscGlobalAnalyse',
    dateType: DATA_DATE_TYPE.DAY,
    unitType: UNIT_TYPE.PERCENT_RATE,
  },
  // 点击均价 昨日
  dash_ov_dsp_click_cost: {
    key: 'clickCost',
    adapter: 'AdAdapter',
    method: 'wscGlobalAnalyse',
    dateType: DATA_DATE_TYPE.DAY,
    unitType: UNIT_TYPE.CENT,
  },
  // 广告花费 昨日
  dash_ov_dsp_cost: {
    key: 'cost', // 分
    adapter: 'AdAdapter',
    method: 'wscGlobalAnalyse',
    dateType: DATA_DATE_TYPE.DAY,
    unitType: UNIT_TYPE.CENT,
  },
  // 30日下单数
  dash_ov_dsp_order_num_30: {
    key: 'orderCnt30',
    adapter: 'AdAdapter',
    method: 'wscGlobalAnalyse',
    dateType: DATA_DATE_TYPE.DAY,
  },
  // 30日下单金额
  dash_ov_dsp_order_amount_30: {
    key: 'orderAmount30',
    adapter: 'AdAdapter',
    method: 'wscGlobalAnalyse',
    dateType: DATA_DATE_TYPE.DAY,
    unitType: UNIT_TYPE.CENT,
  },
  dash_ov_dsp_order_roi_30: {
    key: 'orderRoi30',
    adapter: 'AdAdapter',
    method: 'wscGlobalAnalyse',
    dateType: DATA_DATE_TYPE.DAY,
  },

  /** 核销数据 */
  // 优惠码数
  dash_ov_verify_code: {
    key: 'code',
    adapter: 'VerifyAdapter',
    method: 'dispatch',
    dateType: DATA_DATE_TYPE.DAY,
    needCompare: true,
  },
  // 优惠券数
  dash_ov_verify_coupon: {
    key: 'card',
    adapter: 'VerifyAdapter',
    method: 'dispatch',
    dateType: DATA_DATE_TYPE.DAY,
    needCompare: true,
  },
  // 电子卡券数 暂不支持
  dash_ov_verify_ticket: {
    key: 'code',
    adapter: 'VerifyAdapter',
    method: 'dispatch',
    dateType: DATA_DATE_TYPE.DAY,
    needCompare: true,
  },

  /* 分销业务数据 */
  // 支付订单数
  dash_ov_fx_order_pay_num: {
    key: 'payCount',
    adapter: 'FenxiaoAdapter',
    method: 'payCount',
    dateType: DATA_DATE_TYPE.DAY,
    needCompare: true,
  },
  // 支付金额
  dash_ov_fx_order_pay_amount: {
    key: 'payAmount',
    adapter: 'FenxiaoAdapter',
    method: 'payAmount',
    dateType: DATA_DATE_TYPE.DAY,
    needCompare: true,
    unitType: UNIT_TYPE.CENT,
  },
  // 新增分销商数
  dash_ov_fx_new_fxSeller_num: {
    key: 'newFxSeller',
    adapter: 'FenxiaoAdapter',
    method: 'newFxSeller',
    dateType: DATA_DATE_TYPE.DAY,
    needCompare: true,
  },
  // 成交分销商数
  dash_ov_fx_payed_fxSeller_num: {
    key: 'payedFxSeller',
    adapter: 'FenxiaoAdapter',
    method: 'payedFxSeller',
    dateType: DATA_DATE_TYPE.DAY,
    needCompare: true,
  },
  // 年度计分
  dash_ov_fx_credit_point: {
    key: 'score',
    adapter: 'FenxiaoAdapter',
    method: 'creditPoint',
    unitType: UNIT_TYPE.RATE,
  },

  /**
   * dash_ov_edu_clue_new 新增线索数
   * dash_ov_edu_clue_mine 我的线索数
   * dash_ov_edu_clue_new_student 报名学员数
   * dash_ov_edu_clue_all 公海池线索数
   * dash_ov_edu_clue_ongoing 跟进中线索数
   * dash_ov_edu_clue_invited 已邀约线索数
   * dash_ov_edu_clue_experienced 已试听线索数
   * dash_ov_edu_clue_done 已成交线索数
   * dash_ov_edu_clue_expired 已逾期线索数
   * dash_ov_edu_clue_need_callback 待回访线索数
   * dash_ov_edu_clue_pay_rate 线索转化率
   * dash_ov_edu_schedule_quorum 应到人次
   * dash_ov_edu_schedule_unsigned 未签到人次
   * dash_ov_edu_appointment_new 新增预约数
   * dash_ov_edu_schedule_attended 已到人次
   * dash_ov_edu_schedule_leave 请假人次
   * dash_ov_edu_schedule_experienced 试听人次
   * dash_ov_edu_schedule_cost 课时消耗数
   * dash_ov_edu_schedule_today 今日日程数
   * dash_ov_edu_student_all 累计学员数
   * dash_ov_edu_student_completion 结业学员数
   * dash_ov_edu_student_learning 在读学员数
   */
  /* 教育业务数据 */
  // 新增线索数
  dash_ov_edu_clue_new: {
    key: 'dash_ov_edu_clue_new',
    adapter: 'EduAdapter',
    method: 'getOverview',
    compareDateType: DATA_DATE_TYPE.YESTERDAY,
  },
  // 我的线索数
  dash_ov_edu_clue_mine: {
    key: 'dash_ov_edu_clue_mine',
    adapter: 'EduAdapter',
    method: 'getOverview',
  },
  // 报名学员数
  dash_ov_edu_clue_new_student: {
    key: 'dash_ov_edu_clue_new_student',
    adapter: 'EduAdapter',
    method: 'getOverview',
    compareDateType: DATA_DATE_TYPE.YESTERDAY,
  },
  // 公海池线索数
  dash_ov_edu_clue_all: {
    key: 'dash_ov_edu_clue_all',
    adapter: 'EduAdapter',
    method: 'getOverview',
    compareDateType: DATA_DATE_TYPE.YESTERDAY,
  },
  // 跟进中线索数
  dash_ov_edu_clue_ongoing: {
    key: 'dash_ov_edu_clue_ongoing',
    adapter: 'EduAdapter',
    method: 'getOverview',
  },
  // 已邀约线索数
  dash_ov_edu_clue_invited: {
    key: 'dash_ov_edu_clue_invited',
    adapter: 'EduAdapter',
    method: 'getOverview',
  },
  // 已试听线索数
  dash_ov_edu_clue_experienced: {
    key: 'dash_ov_edu_clue_experienced',
    adapter: 'EduAdapter',
    method: 'getOverview',
  },
  // 已成交线索数
  dash_ov_edu_clue_done: {
    key: 'dash_ov_edu_clue_done',
    adapter: 'EduAdapter',
    method: 'getOverview',
  },
  // 已逾期线索数
  dash_ov_edu_clue_expired: {
    key: 'dash_ov_edu_clue_expired',
    adapter: 'EduAdapter',
    method: 'getOverview',
  },
  // 待回访线索数
  dash_ov_edu_clue_need_callback: {
    key: 'dash_ov_edu_clue_need_callback',
    adapter: 'EduAdapter',
    method: 'getOverview',
  },
  // 线索转化率
  dash_ov_edu_clue_pay_rate: {
    key: 'dash_ov_edu_clue_pay_rate',
    adapter: 'EduAdapter',
    method: 'getOverview',
    unitType: UNIT_TYPE.PERCENT_RATE,
  },
  // 应到人次
  dash_ov_edu_schedule_quorum: {
    key: 'dash_ov_edu_schedule_quorum',
    adapter: 'EduAdapter',
    method: 'getOverview',
    compareDateType: DATA_DATE_TYPE.YESTERDAY,
  },
  // 未签到人次
  dash_ov_edu_schedule_unsigned: {
    key: 'dash_ov_edu_schedule_unsigned',
    adapter: 'EduAdapter',
    method: 'getOverview',
  },
  // 新增预约数
  dash_ov_edu_appointment_new: {
    key: 'dash_ov_edu_appointment_new',
    adapter: 'EduAdapter',
    method: 'getOverview',
    compareDateType: DATA_DATE_TYPE.YESTERDAY,
  },
  // 已到人次
  dash_ov_edu_schedule_attended: {
    key: 'dash_ov_edu_schedule_attended',
    adapter: 'EduAdapter',
    method: 'getOverview',
    compareDateType: DATA_DATE_TYPE.YESTERDAY,
  },
  // 请假人次
  dash_ov_edu_schedule_leave: {
    key: 'dash_ov_edu_schedule_leave',
    adapter: 'EduAdapter',
    method: 'getOverview',
    compareDateType: DATA_DATE_TYPE.YESTERDAY,
  },
  // 试听人次
  dash_ov_edu_schedule_experienced: {
    key: 'dash_ov_edu_schedule_experienced',
    adapter: 'EduAdapter',
    method: 'getOverview',
    compareDateType: DATA_DATE_TYPE.YESTERDAY,
  },
  // 课时消耗数
  dash_ov_edu_schedule_cost: {
    key: 'dash_ov_edu_schedule_cost',
    adapter: 'EduAdapter',
    method: 'getOverview',
    compareDateType: DATA_DATE_TYPE.YESTERDAY,
  },
  // 今日日程数
  dash_ov_edu_schedule_today: {
    key: 'dash_ov_edu_schedule_today',
    adapter: 'EduAdapter',
    method: 'getOverview',
    compareDateType: DATA_DATE_TYPE.YESTERDAY,
  },
  // 累计学员数
  dash_ov_edu_student_all: {
    key: 'dash_ov_edu_student_all',
    adapter: 'EduAdapter',
    method: 'getOverview',
    compareDateType: DATA_DATE_TYPE.YESTERDAY,
  },
  // 结业学员数
  dash_ov_edu_student_completion: {
    key: 'dash_ov_edu_student_completion',
    adapter: 'EduAdapter',
    method: 'getOverview',
    compareDateType: DATA_DATE_TYPE.YESTERDAY,
  },
  // 在读学员数
  dash_ov_edu_student_learning: {
    key: 'dash_ov_edu_student_learning',
    adapter: 'EduAdapter',
    method: 'getOverview',
    compareDateType: DATA_DATE_TYPE.YESTERDAY,
  },

  /** 零售3.0配置 */
  /** 交易数据 */
  // 营业额
  retail_dash_ov_turnover: {
    key: 'turnoverAmount',
    adapter: 'RetailTradeAdapter',
    method: 'getRetailTradeOverViewData',
    dateType: DATA_DATE_TYPE.DAY,
    needCompare: true,
    unitType: UNIT_TYPE.CENT,
  },
  // 优惠金额
  retail_dash_ov_discount_amount: {
    key: 'promotionAmount',
    adapter: 'RetailTradeAdapter',
    method: 'getRetailTradeOverViewData',
    dateType: DATA_DATE_TYPE.DAY,
    needCompare: true,
    unitType: UNIT_TYPE.CENT,
  },
  // 营业额不含储值赠送金
  retail_dash_ov_turnover_without_prepaid_bonus: {
    key: 'turnoverAmountWithoutGiftPrepaidCard',
    adapter: 'RetailTradeAdapter',
    method: 'getRetailTradeOverViewData',
    dateType: DATA_DATE_TYPE.DAY,
    needCompare: true,
    unitType: UNIT_TYPE.CENT,
  },
  // 支付订单数
  retail_dash_ov_order_pay_num: {
    key: 'paidOrderNum',
    adapter: 'RetailTradeAdapter',
    method: 'getRetailTradeOverViewData',
    dateType: DATA_DATE_TYPE.DAY,
    needCompare: true,
  },
  // 支付金额（元）
  retail_dash_ov_order_pay_amount: {
    key: 'paidAmount',
    adapter: 'RetailTradeAdapter',
    method: 'getRetailTradeOverViewData',
    dateType: DATA_DATE_TYPE.DAY,
    needCompare: true,
    unitType: UNIT_TYPE.CENT,
  },
  // 支付客户数
  retail_dash_ov_order_payed_customer: {
    key: 'paidCustomerNum',
    adapter: 'RetailTradeAdapter',
    method: 'getRetailTradeOverViewData',
    dateType: DATA_DATE_TYPE.DAY,
    needCompare: true,
  },
  // 客单价（元）
  retail_dash_ov_customer_average: {
    key: 'unitPrice',
    adapter: 'RetailTradeAdapter',
    method: 'getRetailTradeOverViewData',
    dateType: DATA_DATE_TYPE.DAY,
    needCompare: true,
    unitType: UNIT_TYPE.CENT,
  },
  // 成功退款金额（元）
  retail_dash_ov_success_return_amount: {
    key: 'succRefundAmount',
    adapter: 'RetailTradeAdapter',
    method: 'getRetailTradeOverViewData',
    dateType: DATA_DATE_TYPE.DAY,
    needCompare: true,
    unitType: UNIT_TYPE.CENT,
  },
  // 访问-付款转化
  retail_dash_ov_conversion_rate: {
    key: 'paidPvRate',
    adapter: 'RetailTradeAdapter',
    method: 'getRetailTradeOverViewData',
    dateType: DATA_DATE_TYPE.DAY,
    needCompare: true,
    unitType: UNIT_TYPE.PERCENT_RATE,
  },

  // 营业额（元）
  retail_dash_ov_order_total_amount: {
    key: 'totalAmount',
    adapter: 'RetailTradeAdapter',
    method: 'getRetailTradeOverViewData',
    needCompare: true,
    unitType: UNIT_TYPE.CENT,
  },

  /** 商品数据 */
  // 在售商品数
  retail_dash_ov_item_onsale: {
    key: 'itemOnSale',
    adapter: 'RetailGoodsAdapter',
    method: 'getRetailGoodsAndShopOverViewData',
    dateType: DATA_DATE_TYPE.DAY,
    needCompare: true,
  },
  // 商品总销量
  retail_dash_ov_item_total_sales: {
    key: 'saleTotal',
    adapter: 'RetailGoodsAdapter',
    method: 'getRetailGoodsAndShopOverViewData',
    dateType: DATA_DATE_TYPE.DAY,
    needCompare: true,
  },
  // 被访问商品数
  retail_dash_ov_item_visited_goods: {
    key: 'itemViewed',
    adapter: 'RetailGoodsAdapter',
    method: 'getRetailGoodsAndShopOverViewData',
    dateType: DATA_DATE_TYPE.DAY,
    needCompare: true,
  },
  // 动销商品数
  retail_dash_ov_item_dynamic_cnt: {
    key: 'itemSold',
    adapter: 'RetailGoodsAdapter',
    method: 'getRetailGoodsAndShopOverViewData',
    dateType: DATA_DATE_TYPE.DAY,
    needCompare: true,
  },
  // 商品访客数
  retail_dash_ov_item_visited_uv: {
    key: 'itemVisitor',
    adapter: 'RetailGoodsAdapter',
    method: 'getRetailGoodsAndShopOverViewData',
    dateType: DATA_DATE_TYPE.DAY,
    needCompare: true,
  },
  // 商品曝光量
  retail_dash_ov_item_expose_cnt: {
    key: 'exposePv',
    adapter: 'RetailGoodsAdapter',
    method: 'getRetailGoodsAndShopOverViewData',
    dateType: DATA_DATE_TYPE.DAY,
    needCompare: true,
  },
  // 商品浏览量
  retail_dash_ov_item_pv: {
    key: 'itemBrowse',
    adapter: 'RetailGoodsAdapter',
    method: 'getRetailGoodsAndShopOverViewData',
    dateType: DATA_DATE_TYPE.DAY,
    needCompare: true,
  },
  // 商品加购数
  retail_dash_ov_item_add_cart_cnt: {
    key: 'addCartCount',
    adapter: 'RetailGoodsAdapter',
    method: 'getRetailGoodsAndShopOverViewData',
    dateType: DATA_DATE_TYPE.DAY,
    needCompare: true,
  },
  // 下单件数
  retail_dash_ov_item_order_cnt: {
    key: 'orderNum',
    adapter: 'RetailGoodsAdapter',
    method: 'getRetailGoodsAndShopOverViewData',
    dateType: DATA_DATE_TYPE.DAY,
    needCompare: true,
  },

  /** 店铺数据 */
  // 访客数
  retail_dash_ov_uv: {
    key: 'uv',
    adapter: 'RetailGoodsAdapter',
    method: 'getRetailGoodsAndShopOverViewData',
    dateType: DATA_DATE_TYPE.DAY,
    needCompare: true,
  },
  // 浏览量
  retail_dash_ov_pv: {
    key: 'pv',
    adapter: 'RetailGoodsAdapter',
    method: 'getRetailGoodsAndShopOverViewData',
    dateType: DATA_DATE_TYPE.DAY,
    needCompare: true,
  },

  /** 营业目标 */
  // 本月已完成营业额（元）
  retail_dash_ov_month_finish_turnover: {
    key: 'currentMonthHasPaid',
    adapter: 'RetailSaleTargetAdapter',
    method: 'findSingleSalesTargetBySales',
    unitType: UNIT_TYPE.CENT,
  },
  // 本月目标营业额（元）
  retail_dash_ov_month_target_turnover: {
    key: 'currentMonthTarget',
    adapter: 'RetailSaleTargetAdapter',
    method: 'findSingleSalesTargetBySales',
    unitType: UNIT_TYPE.CENT,
  },
  // 本月目标营业额 极简版（元）
  retail_dash_ov_month_target_turnover_sub_shop: {
    key: 'currentMonthTarget',
    adapter: 'RetailSaleTargetAdapter',
    method: 'findSingleSalesTargetBySales',
    unitType: UNIT_TYPE.CENT,
  },
  // 本月完成进度
  retail_dash_ov_month_finish_schedule: {
    key: 'currentMonthProgressDouble',
    adapter: 'RetailSaleTargetAdapter',
    method: 'findSingleSalesTargetBySales',
    unitType: UNIT_TYPE.PERCENT_RATE,
  },

  /** 客户数据 */
  // 新增客户数
  retail_dash_ov_new_customer: {
    key: 'new_customer',
    adapter: 'RetailCustomerAdapter',
    method: 'getCustomerStatisticData',
    dateType: DATA_DATE_TYPE.DAY,
    needCompare: true,
    delay: 1,
  },
  // 新增会员数
  retail_dash_ov_new_member: {
    key: 'new_member',
    adapter: 'RetailCustomerAdapter',
    method: 'getCustomerStatisticData',
    dateType: DATA_DATE_TYPE.DAY,
    needCompare: true,
    delay: 1,
  },
  // 累计客户数
  retail_dash_ov_total_customer: {
    key: 'all_customer',
    adapter: 'RetailCustomerAdapter',
    method: 'getTotalCustomerStatisticData',
    dateType: DATA_DATE_TYPE.DAY,
    needCompare: true,
    delay: 1,
  },
  // 累计会员数
  retail_dash_ov_total_member: {
    key: 'all_member',
    adapter: 'RetailCustomerAdapter',
    method: 'getTotalCustomerStatisticData',
    dateType: DATA_DATE_TYPE.DAY,
    needCompare: true,
    delay: 1,
  },
  // 支付会员数
  retail_dash_ov_tatal_payed_member: {
    key: 'paid_member',
    adapter: 'RetailCustomerAdapter',
    method: 'getCustomerDealData',
    dateType: DATA_DATE_TYPE.DAY,
    needCompare: true,
    delay: 1,
  },

  /** 客服服务 */
  // 在线时长
  retail_dash_ov_im_active_time: {
    key: 'onlineTime',
    adapter: 'CustomerServiceAdapter',
    method: 'dispatch',
    dateType: DATA_DATE_TYPE.DAY,
    delay: 1,
    needCompare: true,
    unitType: UNIT_TYPE.MILLISECS,
  },
  // 接待人数
  retail_dash_ov_im_recept_amount: {
    key: 'receiveAmount',
    adapter: 'CustomerServiceAdapter',
    method: 'dispatch',
    dateType: DATA_DATE_TYPE.DAY,
    delay: 1,
    needCompare: true,
  },
  // 平均首次响应时长
  retail_dash_ov_im_first_reply_time: {
    key: 'firstReplyTime',
    adapter: 'CustomerServiceAdapter',
    method: 'dispatch',
    dateType: DATA_DATE_TYPE.DAY,
    delay: 1,
    needCompare: true,
    unitType: UNIT_TYPE.SECS,
  },
  // 平均响应时长
  retail_dash_ov_im_average_time: {
    key: 'averageTime',
    adapter: 'CustomerServiceAdapter',
    method: 'dispatch',
    dateType: DATA_DATE_TYPE.DAY,
    delay: 1,
    needCompare: true,
    unitType: UNIT_TYPE.SECS,
  },
  // 会话总数
  retail_dash_ov_im_conversation_num: {
    key: 'conversationTotalAmount',
    adapter: 'CustomerServiceAdapter',
    method: 'dispatch',
    dateType: DATA_DATE_TYPE.DAY,
    delay: 1,
    needCompare: true,
  },
  // 服务邀评率
  retail_dash_ov_im_invite_percent: {
    key: 'invitePercent',
    adapter: 'CustomerServiceAdapter',
    method: 'dispatch',
    dateType: DATA_DATE_TYPE.DAY,
    delay: 1,
    needCompare: true,
    unitType: UNIT_TYPE.PERCENT_RATE,
  },
  // 问题解决率
  retail_dash_ov_im_qa_solve_rate: {
    key: 'qaSolveTimePercent',
    adapter: 'CustomerServiceAdapter',
    method: 'dispatch',
    dateType: DATA_DATE_TYPE.DAY,
    delay: 1,
    needCompare: true,
    unitType: UNIT_TYPE.PERCENT_RATE,
  },
  // 综合满意度
  retail_dash_ov_im_overall_static: {
    key: 'overallSatisfaction',
    adapter: 'CustomerServiceAdapter',
    method: 'dispatch',
    dateType: DATA_DATE_TYPE.DAY,
    delay: 1,
    needCompare: true,
    unitType: UNIT_TYPE.RATE,
  },
  // 销售金额
  retail_dash_ov_sales: {
    key: 'sales',
    adapter: 'CustomerServiceAdapter',
    method: 'dispatch',
    dateType: DATA_DATE_TYPE.DAY,
    delay: 1,
    needCompare: true,
    unitType: UNIT_TYPE.CENT,
  },
  // 销售人数
  retail_dash_ov_sales_customers: {
    key: 'salesCustomerNum',
    adapter: 'CustomerServiceAdapter',
    method: 'dispatch',
    dateType: DATA_DATE_TYPE.DAY,
    delay: 1,
    needCompare: true,
  },
  // 询单付款转化率
  retail_dash_ov_inquiry_pay_rate: {
    key: 'inquiryPaymentConversionRate',
    adapter: 'CustomerServiceAdapter',
    method: 'dispatch',
    dateType: DATA_DATE_TYPE.DAY,
    delay: 3,
    needCompare: true,
    unitType: UNIT_TYPE.PERCENT_RATE,
  },

  /** 财务数据 */
  // 可用店铺余额（元）
  retail_dash_ov_asset_available: {
    key: 'AVAILABLE',
    adapter: 'AssetsAdapter',
    method: 'getCustomerStatistic',
    unitType: UNIT_TYPE.CENT,
  },
  // 待结算（元）
  retail_dash_ov_asset_wait_to_settle: {
    key: 'WAIT_TO_SETTLE',
    adapter: 'AssetsAdapter',
    method: 'getCustomerStatistic',
    unitType: UNIT_TYPE.CENT,
  },
  // 不可用余额（元）
  // FIXME
  retail_dash_ov_asset_unavailable: {
    key: 'FREEZE',
    adapter: 'AssetsAdapter',
    method: 'getCustomerStatistic',
    unitType: UNIT_TYPE.CENT,
  },
  // 保证金（元）
  retail_dash_ov_asset_deposit: {
    key: 'DEPOSIT',
    adapter: 'AssetsAdapter',
    method: 'getCustomerStatistic',
    unitType: UNIT_TYPE.CENT,
  },
  // 储值卡资金（元）
  retail_dash_ov_value_card: {
    key: 'VALUE_CARD',
    adapter: 'AssetsAdapter',
    method: 'getCustomerStatistic',
    unitType: UNIT_TYPE.CENT,
  },
  // 新增储值金额（元）
  retail_dash_additional_ov_value_card: {
    key: 'storedAmount',
    adapter: 'RetailTradeAdapter',
    method: 'getRetailTradeOverViewData',
    dateType: DATA_DATE_TYPE.DAY,
    needCompare: ctx => {
      const shopInfo = ctx.getState('shopInfo');
      return checkRetailSingleStore(shopInfo) || checkUnifiedShop(shopInfo);
    },
    unitType: UNIT_TYPE.CENT,
  },

  /** 店铺装修 */
  // 微页面数量
  retail_dash_ov_feature_num: {
    key: 'featureNum',
    adapter: 'ShowcaseAdapter',
    method: 'queryMicropageCount',
  },
  // 图片数量
  retail_dash_ov_material_picture_num: {
    key: 'usedImageCount',
    adapter: 'ShowcaseAdapter',
    method: 'queryNumStatistics',
  },
  // 语音数量
  retail_dash_ov_material_audio_num: {
    key: 'usedAudioCount',
    adapter: 'ShowcaseAdapter',
    method: 'queryNumStatistics',
  },
  // 已上传视频数
  retail_dash_ov_material_video_num: {
    key: 'videoCount',
    adapter: 'ShowcaseAdapter',
    method: 'queryTencentVideoUsageStatistics',
  },
  // 已使用流量
  retail_dash_ov_material_video_used_traffic: {
    key: 'usedTraffic',
    adapter: 'ShowcaseAdapter',
    method: 'queryTencentVideoUsageStatistics',
    unitType: UNIT_TYPE.BYTE,
  },
  // 已使用存储空间
  retail_dash_ov_material_video_used_capacity: {
    key: 'usedStorage',
    adapter: 'ShowcaseAdapter',
    method: 'queryTencentVideoUsageStatistics',
    unitType: UNIT_TYPE.BYTE,
  },

  /** 核销数据 */
  // 优惠券数
  retail_dash_ov_verify_coupon: {
    key: 'card',
    adapter: 'VerifyAdapter',
    method: 'dispatch',
    dateType: DATA_DATE_TYPE.DAY,
    needCompare: true,
  },
  // 优惠码数
  retail_dash_ov_verify_code: {
    key: 'code',
    adapter: 'VerifyAdapter',
    method: 'dispatch',
    dateType: DATA_DATE_TYPE.DAY,
    needCompare: true,
  },
};

module.exports = {
  DATA_DATE_TYPE,
  DATA_TYPE,
  UNIT_TYPE,
};
