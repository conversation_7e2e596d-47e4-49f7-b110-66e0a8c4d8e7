const get = require('lodash/get');
const {
  branchStoreRole,
  ShopAbility,
  checkLiteOnlineStoreManager,
  checkRetailShop,
  checkBranchStore,
} = require('@youzan/utils-shop');
const { ShopAbilityUtil } = require('@youzan/plugin-shop-ability');
const HQStoreSearchService = require('../../services/api/shop/HQStoreSearchService');
const BaseController = require('../base/BaseController');
const {
  getBizType,
  getBizType4ShowCaseCenter,
  getBizType4ShowCaseCenterV1,
} = require('./common/biz-type');
const { getMenuVersion } = require('./common/index');

const SalesOrgCategoryService = require('../../services/api/shopcenter/outer/SalesOrgCategoryService');

/**
 * Project Base Controller
 */
class DashboardBaseController extends BaseController {
  constructor(ctx) {
    super(ctx);
    this.ctx.biz = '工作台';
  }

  errCatch(msg, e) {
    this.ctx.logger.error(msg, e, e.extra);
  }

  async getShopBizType(ctx) {
    const shopBizType = await getBizType(ctx);
    return shopBizType;
  }

  async getBizType4ShowCaseCenter(ctx, options = {}) {
    const { isLiteOnlineManager = false } = options;
    const shopBizType = await getBizType4ShowCaseCenter(ctx, { isLiteOnlineManager });
    return shopBizType;
  }

  // 根据bizType区分获取shopBizType
  async getShopBizType4ShowCaseCenterByBizType(ctx, bizType, options = {}) {
    if (bizType) {
      return this.getBizType4ShowCaseCenter(ctx, options);
    } else {
      return this.getBizType4ShowCaseCenterV1(ctx);
    }
  }

  async getBizType4ShowCaseCenterV1(ctx, options = {}) {
    const { isLiteOnlineManager = false } = options;
    const shopBizType = await getBizType4ShowCaseCenterV1(ctx, { isLiteOnlineManager });
    return shopBizType;
  }

  getHqKdtId(ctx) {
    const shopInfo = ctx.getState('shopInfo') || {};
    const { rootKdtId } = shopInfo;
    return rootKdtId;
  }

  /**
   * 当前店铺员工是否为同城网店管理员
   */
  async checkLiteOnlineManager(ctx) {
    if (
      !(await new ShopAbilityUtil(ctx).checkAbilityValid({
        keys: [ShopAbility.LiteOnlineStoreManageAbility],
      }))
    ) {
      return false;
    }
    const staffRoles = await this.getStaffRoles(ctx);
    return checkLiteOnlineStoreManager(staffRoles);
  }

  /**
   * 获取同城网店管理员管的 lite 网店
   */
  async getLiteStore(ctx) {
    const res = await new HQStoreSearchService(ctx).searchWithDataPermission({
      kdtId: ctx.kdtId,
      adminId: ctx.userId,
      pageSize: 20,
      retailSource: 'wsc-pc-v4',
      shopRoleList: [branchStoreRole],
      isOnlineOpen: true,
      isLiteOnlineStore: true,
      // NOTE: 默认不去获取如下信息，加快接口调用速度
      appendShopLifecycleEndTime: false,
      appendOfflineBusinessHours: false,
      appendPosPointNum: false,
      appendLackInfo: false,
    });
    return res;
  }

  /**
   * 获取 Lite 网店 kdtId
   */
  async getLiteKdtId(ctx) {
    const stores = await this.getLiteStore(ctx);
    return get(stores, 'items[0].storeKdtId', null);
  }

  async getOrgCategory(ctx) {
    return new SalesOrgCategoryService(ctx).queryOne(ctx.kdtId);
  }

  async getMenuVersion(ctx, version) {
    const shopInfo = ctx.getState('shopInfo');
    // 非零售店铺，原封不动
    if (!checkRetailShop(shopInfo)) {
      return version;
    }

    // 非分店，不需要使用组织分类判断
    if (!checkBranchStore(shopInfo)) {
      return getMenuVersion(ctx, version);
    }

    // 分店，使用组织分类判断
    // 不区分多渠道和非多渠道，如果非多渠道，orgCategory 是空
    const result = await this.getOrgCategory(ctx).catch(() => ({}));

    const { orgCategory = '' } = result || {};

    return getMenuVersion(ctx, version, orgCategory);
  }
}

module.exports = DashboardBaseController;
