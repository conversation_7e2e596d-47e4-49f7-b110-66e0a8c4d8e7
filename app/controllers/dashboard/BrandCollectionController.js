const CustomerBrandOperateGatewayService = require('../../services/api/enable/api/CustomerBrandOperateGatewayService');
const BrandQueryGatewayService = require('../../services/api/enable/api/BrandQueryGatewayService');
const BaseController = require('../base/BaseController');

class BrandCollectionController extends BaseController {
  /** @desciption 获取所有类目 */
  async getGetCategoriesJson() {
    const { ctx } = this;
    const result = await new BrandQueryGatewayService(ctx).getCategories();
    ctx.success(result);
  }

  /** @desciption 根据品牌名模糊查询品牌库内已有的品牌词 */
  async getSearchBrandNameJson() {
    const { ctx } = this;
    const { keyword = '' } = ctx.getQueryData();

    const result = await new BrandQueryGatewayService(ctx).searchBrandName({ keyword });
    ctx.success(result);
  }

  /** @desciption 根据品牌名精确查询品牌库内的品牌信息 */
  async getGetBrandByNameJson() {
    const { ctx } = this;
    const { brandName = '' } = ctx.getQueryData();

    const result = await new BrandQueryGatewayService(ctx).getBrandByName({ brandName });
    ctx.success(result);
  }

  /** @desciption 创建或新增客户品牌 */
  async postCreateOrAddByMerchantJson() {
    const { ctx } = this;
    const { companyName, ownBrands = [], proxyBrands = [] } = ctx.getPostData();

    const result = await new CustomerBrandOperateGatewayService(ctx).createOrAddByMerchant({
      companyName,
      ownBrands,
      proxyBrands,

      kdtId: ctx.kdtId,
      sourceApp: 'wsc-pc-v4',
      sourceModule: 'dashboard',
    });
    ctx.success(result);
  }
}

module.exports = BrandCollectionController;
