const DashboardBaseController = require('./DashboardBaseController');
const ShopDetectionService = require('../../services/api/ebiz/mall/ShopDetectionService');
const MultiStoreAdminService = require('@youzan/wsc-pc-base/app/services/multistore/MultiStoreAdminService');
const { getRole, getMultiRoles, getMultiSamRoles } = require('./common/user-role');
const { getBizType } = require('./common/biz-type');
const { WSC_SAM_ROLE } = require('../../constants');
const { roleTypeMap } = require('./constants');

const liteBizType = 29;

const DETECT_STATUS = {
  EXCEPTIION: -1,
  UNDETECTED: 0,
  DETECTING: 1,
  FINISHED: 3,
};

class ShopDetectionController extends DashboardBaseController {
  constructor(ctx) {
    super(ctx);
    this.ctx.biz = '工作台-店铺检测';
  }

  /**
   * 获取网店id
   * @param {*} ctx
   * @memberof IndexController
   */
  async getStoreId(ctx, roleId) {
    if (roleId !== WSC_SAM_ROLE.NODE_ADMIN) {
      return null;
    }
    const { userId } = ctx.getLocalSession('userInfo');
    const param = {
      kdtId: ctx.kdtId,
      adminId: userId,
    };
    const storeId = await new MultiStoreAdminService(ctx).getStoreIdByAdminIdAndKdtId(param);
    return storeId;
  }

  async detect(ctx) {
    const userRole = await getRole(ctx);
    if (!userRole) {
      ctx.successRes();
      return;
    }
    const { roleId: samRoleId } = userRole;
    const storeId = await this.getStoreId(ctx, samRoleId);
    const hqKdtId = this.getHqKdtId(ctx);
    const isLiteOnlineManager = await this.checkLiteOnlineManager(ctx);
    let bizType = await getBizType(ctx, { isLiteOnlineManager });
    let storeKdtId = null;
    if (isLiteOnlineManager) {
      storeKdtId = await this.getLiteKdtId(ctx);
      bizType = liteBizType;
    }

    const result = await new ShopDetectionService(ctx).detect({
      hqKdtId,
      kdtId: ctx.kdtId,
      userId: ctx.userId,
      storeId,
      bizType,
      storeKdtId,
    });
    ctx.successRes(result);
  }

  async getSimpleDetectionResult(ctx) {
    const { userRole, multiRoles } = await getMultiRoles(ctx);
    if (!userRole) {
      ctx.successRes();
      return;
    }
    const { roleId: samRoleId, roleType: samRoleType, biz: samBiz } = userRole;
    const storeId = await this.getStoreId(ctx, samRoleId);
    const isLiteOnlineManager = await this.checkLiteOnlineManager(ctx);
    let bizType = await getBizType(ctx, { isLiteOnlineManager });
    let storeKdtId = null;
    if (isLiteOnlineManager) {
      storeKdtId = await this.getLiteKdtId(ctx);
      bizType = liteBizType;
    }

    try {
      const result = await new ShopDetectionService(ctx).getSimpleDetectionResult({
        kdtId: ctx.kdtId,
        samRole: {
          samBiz,
          samRoleId,
          samRoleType: roleTypeMap[samRoleType] || roleTypeMap.DEFAULT_ROLE,
        },
        multiSamRoles: multiRoles.map(item => {
          return {
            samBiz: item.biz,
            samRoleId: item.roleId,
            samRoleType: roleTypeMap[item.roleType] || roleTypeMap.DEFAULT_ROLE,
          };
        }),
        userId: ctx.userId,
        storeId,
        storeKdtId,
        bizType,
      });
      ctx.successRes(result);
    } catch (e) {
      // 同一用户在同一个店铺3秒内调用改接口不能超过两次
      // 捕捉该异常，返回状态异常
      if (e.code === 347030006) {
        ctx.successRes({
          status: DETECT_STATUS.EXCEPTIION,
        });
      } else {
        throw e;
      }
    }
  }

  async listDetectionResult(ctx) {
    const { detectionTagType, status } = ctx.query;
    const { userRole, multiRoles } = await getMultiRoles(ctx);
    if (!userRole) {
      ctx.successRes();
      return;
    }
    const { roleId: samRoleId, roleType: samRoleType, biz: samBiz } = userRole;
    const storeId = await this.getStoreId(ctx, samRoleId);
    const isLiteOnlineManager = await this.checkLiteOnlineManager(ctx);
    let bizType = await getBizType(ctx, { isLiteOnlineManager });
    let storeKdtId = null;
    if (isLiteOnlineManager) {
      storeKdtId = await this.getLiteKdtId(ctx);
      bizType = liteBizType;
    }

    const result = await new ShopDetectionService(ctx).listDetectionResult({
      kdtId: ctx.kdtId,
      samRole: {
        samBiz,
        samRoleId,
        samRoleType: roleTypeMap[samRoleType] || roleTypeMap.DEFAULT_ROLE,
      },
      multiSamRoles: multiRoles.map(item => {
        return {
          samBiz: item.biz,
          samRoleId: item.roleId,
          samRoleType: roleTypeMap[item.roleType] || roleTypeMap.DEFAULT_ROLE,
        };
      }),
      detectionTagType,
      status,
      userId: ctx.userId,
      storeId,
      storeKdtId,
      bizType,
    });
    ctx.successRes(result);
  }

  async listPendingResultOrderByPriority(ctx) {
    const { userRole, multiRoles } = await getMultiRoles(ctx);
    if (!userRole) {
      ctx.successRes();
      return;
    }
    const { roleId: samRoleId, roleType: samRoleType, biz: samBiz } = userRole;
    const storeId = await this.getStoreId(ctx, samRoleId);
    const multiSamRoles = getMultiSamRoles(multiRoles);
    const isLiteOnlineManager = await this.checkLiteOnlineManager(ctx);
    let bizType = await getBizType(ctx, { isLiteOnlineManager });
    let storeKdtId = null;
    if (isLiteOnlineManager) {
      storeKdtId = await this.getLiteKdtId(ctx);
      bizType = liteBizType;
    }

    const result = await new ShopDetectionService(ctx).listPendingResultOrderByPriority({
      kdtId: ctx.kdtId,
      samRole: {
        samBiz,
        samRoleId,
        samRoleType: roleTypeMap[samRoleType] || roleTypeMap.DEFAULT_ROLE,
      },
      multiSamRoles,
      userId: ctx.userId,
      storeId,
      storeKdtId,
      bizType,
    });
    ctx.successRes(result);
  }

  async editDetectionProject(ctx) {
    const { projectId, status } = ctx.getPostData();
    const userRole = getRole(ctx);
    if (!userRole) {
      ctx.successRes();
      return;
    }
    const { roleId: samRoleId } = userRole;
    const storeId = await this.getStoreId(ctx, samRoleId);
    const isLiteOnlineManager = await this.checkLiteOnlineManager(ctx);
    let bizType = await getBizType(ctx, { isLiteOnlineManager });
    let storeKdtId = null;
    if (isLiteOnlineManager) {
      storeKdtId = await this.getLiteKdtId(ctx);
      bizType = liteBizType;
    }
    const result = await new ShopDetectionService(ctx).editDetectionProject({
      kdtId: ctx.kdtId,
      userId: ctx.userId,
      storeId,
      projectId,
      status,
      bizType,
      storeKdtId,
    });
    ctx.successRes(result);
  }

  async listAllDetectionResult(ctx) {
    const userRole = getRole(ctx);
    if (!userRole) {
      ctx.successRes();
      return;
    }
    const { roleId: samRoleId, roleType: samRoleType, biz: samBiz } = userRole;
    const storeId = await this.getStoreId(ctx, samRoleId);
    const isLiteOnlineManager = await this.checkLiteOnlineManager(ctx);
    let storeKdtId = null;
    if (isLiteOnlineManager) {
      storeKdtId = await this.getLiteKdtId(ctx);
    }
    const result = await new ShopDetectionService(ctx).listAllDetectionResult({
      kdtId: ctx.kdtId,
      storeId,
      storeKdtId,
      samRole: {
        samBiz,
        samRoleId,
        samRoleType: roleTypeMap[samRoleType] || roleTypeMap.DEFAULT_ROLE,
      },
      userId: ctx.userId,
    });
    ctx.successRes(result);
  }

  async listDetectionTag(ctx) {
    const isLiteOnlineManager = await this.checkLiteOnlineManager(ctx);
    let bizType = await getBizType(ctx, { isLiteOnlineManager });
    let storeKdtId = null;
    if (isLiteOnlineManager) {
      storeKdtId = await this.getLiteKdtId(ctx);
      bizType = liteBizType;
    }
    const result = await new ShopDetectionService(ctx).listDetectionTagWithAbility({
      kdtId: ctx.kdtId,
      bizType,
      userId: ctx.userId,
      storeKdtId,
    });

    ctx.successRes(result);
  }
}

module.exports = ShopDetectionController;
