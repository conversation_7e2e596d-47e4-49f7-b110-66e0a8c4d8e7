const DataOverviewService = require('../../services/api/showcase/center/DataOverviewService');
const CommonModuleService = require('../../services/api/showcase/center/CommonModuleService');
const MultiStoreAdminService = require('@youzan/wsc-pc-base/app/services/multistore/MultiStoreAdminService');
const DashboardBaseController = require('./DashboardBaseController');
const CmsService = require('../../services/api/cms/CmsService');
const WscDashboardService = require('../../services/api/datacenter/WscDashboardService');
const RiskWarnService = require('../../services/api/riskwarn/RiskWarnService');
const ConversationService = require('../../services/api/message/ConversationService');
const AppStatusRemoteService = require('../../services/api/yop/AppStatusRemoteService');
const MarketRemoteService = require('../../services/api/yop/MarketRemoteService');
const SecuredService = require('../../services/api/trade/SecuredService');
const SurveyPaperRangeService = require('../../services/api/ebiz/mall/SurveyPaperRangeService');
const SidebarPictureAdService = require('../../services/api/ebiz/mall/SidebarPictureAdService');
const SidebarSubjectService = require('../../services/api/ebiz/mall/SidebarSubjectService');
const EmergencyNoticeService = require('../../services/api/ebiz/mall/EmergencyNoticeService');
const QuestionnaireRemoteService = require('../../services/api/enable/fuwu/QuestionnaireRemoteService');
const ShopRemoteService = require('../../services/api/enable/fuwu/ShopRemoteService');
const DashboardFacade = require('../../services/api/owl/pc/DashboardFacade');
const AbilityService = require('../../services/api/shop-center/AbilityService');
const FeeStatQueryService = require('../../services/api/finance/fee/FeeStatQueryService');
const RealTimeService = require('../../services/api/bigdata/datacenter/RealTimeService');
const WscDashboardRealTimeService = require('../../services/api/datacenter/RealTimeService');
const FeeAlertQueryService = require('../../services/api/finance/fee/FeeAlertQueryService');
const CmsMaterialService = require('../../services/api/common/CmsMaterialService');
const OrderStatisticsService = require('../../services/api/trade/statistics/OrderStatisticsService');
const MerchantCertInfoService = require('../../services/api/pay/customer/MerchantCertInfoService');
const ShopConfigReadService = require('../../services/api/shopcenter/shopconfig/ShopConfigReadService');
const FinProdAlertCheckService = require('../../services/api/pay/fin/FinProdAlertCheckService');
const ProdReadService = require('../../services/api/shopcenter/shopprod/ProdReadService');
const RiskCommonService = require('../../services/api/risk/bifrost/RiskCommonService');
const HQStoreSearchService = require('../../services/api/shop/HQStoreSearchService');

const DataAdapter = require('./data-adapter');
const { getRole } = require('./common/user-role');
const crypto = require('crypto');
const format = require('date-fns/format');
const get = require('lodash/get');
const differenceInDays = require('date-fns/difference_in_days');
const Sam = require('@youzan/sam-sdk');
const { checkEduHqStore, checkPureWscChainStore, checkUnifiedShop } = require('@youzan/utils-shop');
const { getMultiRoles, getMultiSamRoles } = require('./common/user-role');
const { getShopBizTypeName } = require('./common/biz-type');
const { getSuggestCode } = require('./common');

const { SAM_ROLE_TYPE, fromApp } = require('../../constants');
const { PLATFORM_ENUM } = require('./constants');

const { formatCmpListToSupplier } = require('./utils');
// 不同服务版本赠送有赞币的映射关系
const versionToYZCoinMap = {
  10: 300,
  20: 500,
  25: 800,
  31: 800,
};
const oneDay = 24 * 60 * 60 * 1000;
const CERT_SOURCEID_TYPE = 'KDT_ID';
const CERT_UNIFIEDCERT_TYPE = 1;

class NaDashboardController extends DashboardBaseController {
  constructor(ctx) {
    super(ctx);
    this.ctx.biz = '工作台';
  }

  init() {
    super.init();
    super.initUserInfo();
  }

  async getIndexHtml(ctx) {
    await ctx.render('na-dashboard/index.html');
  }

  isSubShop() {
    const shopMetaInfo = this.ctx.getState('shopMetaInfo') || {};
    const { shopTopic = 0, shopRole = 0 } = shopMetaInfo;
    // 是教育店铺或微商城店铺 shopTopic = 0 || shopTopic = 1
    // 是分店 shopRole = 2
    return (shopTopic === 0 || shopTopic === 1) && shopRole === 2;
  }

  isHqShop() {
    const shopMetaInfo = this.ctx.getState('shopMetaInfo') || {};
    const { shopTopic = 0, shopRole = 0 } = shopMetaInfo;
    // 是教育店铺或微商城店铺 shopTopic = 0 || shopTopic = 1
    // 是总部 shopRole = 1
    return (shopTopic === 0 || shopTopic === 1) && shopRole === 1;
  }

  getHqKdtId() {
    const shopMetaInfo = this.ctx.getState('shopMetaInfo') || {};
    const { rootKdtId } = shopMetaInfo;
    // 是教育店铺或微商城店铺 shopTopic = 0 || shopTopic = 1
    // 是总部 shopRole = 1
    return rootKdtId;
  }

  get type() {
    // 教育不是店铺元数据,需要判断一下
    return this.ctx.isYZEdu ? 999 : this.ctx.shopType;
  }

  async isNewDashboard(ctx) {
    const isNewDashboard = await this.callService(
      'wsc-pc-base/common.GrayReleaseService',
      'isInGrayReleaseByKdtId',
      'wsc_new_dashboard',
      ctx.kdtId
    );

    const shopInfo = ctx.getState('shopInfo');
    const shopType = get(shopInfo, 'shopType');
    // 是微商城店铺
    const isSpecialShop = shopType === 0;
    return isNewDashboard && isSpecialShop && !ctx.isYZEdu && !this.__isHqShop && !this.__isSubShop;
  }

  // 获取店铺月账单接口权限
  async getBillApiAuth(ctx) {
    const biz = 'wsc';
    const samConfig = {
      url: process.env.NODE_TETHER || ctx.getConfig('TETHER_URL'),
      biz,
    };

    if (ctx.xServiceChain) {
      samConfig.params = {
        xServiceChain: ctx.xServiceChain,
      };
    }

    try {
      const sam = new Sam(samConfig);
      const { userId } = ctx.getLocalSession('userInfo');
      const api = '/v4/subscribe/bill/getBill.json';

      const reqData = {
        api,
        method: 'GET',
        kdtId: ctx.kdtId,
        adminId: userId,
        biz,
      };

      return await sam.checkApiPermit(reqData);
    } catch (err) {
      return false;
    }
  }

  /**
   * 获取网店id
   * @param {*} ctx
   * @memberof IndexController
   */
  async getStoreId(ctx) {
    const { userId } = ctx.getLocalSession('userInfo');
    const param = {
      kdtId: ctx.kdtId,
      adminId: userId,
    };
    const storeId = await new MultiStoreAdminService(ctx).getStoreIdByAdminIdAndKdtId(param);
    return storeId;
  }

  /**
   * 获取工作台基本展示信息
   * @param {*} ctx
   * @memberof IndexController
   */
  async getOverviewInfo(ctx) {
    const cmsService = new CmsService(ctx);
    const { isNewDashboard } = ctx.query;
    const [
      // 店铺临期提醒
      noticeAlert,
      // 店铺基本信息
      shopInfo,
      // 文案提醒
      notice,
      // 风险店铺文案提醒
      riskNotice,
      // 最新产品提醒
      produceNotice,
      // 经营建议
      suggestions,
      // 零售帮助中心
      helpInfo,
      // 海景房广告
      qrAd,
      // 问卷调查
      questionnaire,
      // 经营概况数据
      summaryData,
      // 产品动态,有赞头条,线下活动
      slideBarSubjects,
    ] = await Promise.all([
      this.getNoticeAlert(ctx).catch(() => null),
      this.getShopBaseInfo(ctx).catch(() => null),
      this.getNotice(ctx).catch(() => null),
      this.getRiskNotice(ctx).catch(() => null),
      cmsService.getLastProduceNotice().catch(() => null),
      this.getCmsSuggest(ctx).catch(() => null),
      this.getRetailHelpCms(ctx).catch(() => null),
      this.getQrAd(ctx).catch(() => null),
      this.getSurvey(ctx).catch(() => null),
      +isNewDashboard
        ? null
        : this.getSummaryData(ctx).catch(_e => {
            return null;
          }),
      this.getSubjectService(ctx).catch(() => null),
    ]);
    const data = {
      noticeAlert,
      shopInfo,
      notice,
      riskNotice,
      summaryData,
      produceNotice,
      suggestions,
      helpInfo,
      qrAd,
      questionnaire,
      ...slideBarSubjects,
    };

    ctx.successRes(data);
  }

  // 零售3.0 帮助中心内容
  async getRetailHelpCms(ctx) {
    const code = 'store.dashboard.help';
    if (ctx.isSuperStore) {
      return new CmsService(ctx).getAllBySectionCode(code).catch(() => null);
    }
  }

  async getCmsSuggest(ctx) {
    const code = getSuggestCode(ctx.isSuperStore);
    return new CmsService(ctx).getAllBySectionCode(code);
  }

  async getFenxiaoCms(ctx) {
    const cmsService = new CmsService(ctx);
    const [fxNotice, fxNews] = await Promise.all([
      cmsService.getAllBySectionCode('fx_dashboard.platform_notice').catch(() => null),
      cmsService.getAllBySectionCode('fx_dashboard.fxproduct_news').catch(() => null),
    ]);
    ctx.successRes({
      fxNotice,
      fxNews,
    });
  }

  async getShopNotice(ctx) {
    const isDashboardNotice = await this.callService(
      'wsc-pc-base/common.GrayReleaseService',
      'isInGrayReleaseByKdtId',
      'dash_forward_alert',
      ctx.kdtId
    );
    if (isDashboardNotice) {
      return ctx.isYZEdu
        ? this.getEduNotice(ctx).catch(() => null)
        : this.getNotice(ctx).catch(() => null);
    }
  }

  /**
   * 获取公告提醒
   * @param {*} ctx
   * @memberof IndexController
   */
  async getNotice(ctx) {
    const param = {
      kdtId: ctx.kdtId,
      shopType: this.type,
      httpSource: 2,
    };
    const data = await new EmergencyNoticeService(ctx).getEffectiveNotice(param);

    return data;
  }

  /**
   * 获取教育公告提醒
   * @param {*} ctx
   * @memberof IndexController
   */
  async getEduNotice(ctx) {
    const ret = await new CmsMaterialService(ctx).query({
      bizCode: 20,
      channelAlias: 'AC1skm20',
      resourceAlias: 'AC1skn20',
      referenceUrl: 'https://www.youzan.com/v4/dashboard',
    });
    const targetData = get(ret, 'data[0]', {});
    // title有字数显示，采用des，这个。。。强行用的真恶心。。
    const { description: title, linkUrl: url } = targetData;
    if (title) {
      return {
        title,
        url,
        isShow: true,
      };
    }
    return null;
  }

  async getVirusInfo(ctx) {
    const result = await this.getBatchQueryVirusInfo(ctx);
    ctx.successRes(result);
  }

  /**
   * 内容投放平台：http://wsccp.prod.qima-inc.com/marketing-cms#/resource/46
   */
  getVirusAlias(ctx) {
    const shopInfo = ctx.getState('shopInfo');
    const channelAlias = 'AC1skm20';
    let businessAlias = 'ACcfml20';
    let extensionAlias = 'ACcfmk20';

    if (ctx.isYZEdu) {
      businessAlias = 'ACcie420';
      extensionAlias = 'ACcie520';
    }

    // 零售连锁3.0
    if (checkUnifiedShop(shopInfo)) {
      businessAlias = 'ACcib020';
      extensionAlias = 'ACcih820';
    }
    return { channelAlias, businessAlias, extensionAlias };
  }

  /**
   * 获取疫情相关推送
   * @param {*} ctx
   * @memberof IndexController
   */
  async getBatchQueryVirusInfo(ctx) {
    const { channelAlias, businessAlias, extensionAlias } = this.getVirusAlias(ctx);

    const ret = await new CmsMaterialService(ctx).batchQuery({
      bizCode: 20,
      channelAlias,
      resourceAliasPageRequests: [
        { resourceSpaceAlias: businessAlias },
        { resourceSpaceAlias: extensionAlias },
      ],
      referenceUrl: 'https://www.youzan.com/v4/dashboard',
    });

    const businessData = get(ret, `${businessAlias}.data`, []);
    const extensionData = get(ret, `${extensionAlias}.data`, []);
    return { businessData, extensionData };
  }

  /**
   * 获取风险店铺文案提醒
   * @param {*} ctx
   * @memberof IndexController
   */
  async getRiskNotice(ctx) {
    const data = await new RiskWarnService(ctx).getRiskWarnMatch({
      group: fromApp,
      tag: 'RISK_SHOP',
      sourceType: 'SHOP_BACKEND',
      contentType: 'KDT_ID',
      identityType: 'SELLER',
      value: `${ctx.kdtId}`,
    });
    return data;
  }

  /**
   * 获取经营概况数据
   * @param {*} ctx
   * @memberof IndexController
   */
  async getSummaryData(ctx) {
    const { userId } = this.ctx.getLocalSession('userInfo');
    const storeId = await this.getStoreId(ctx);

    const { today, yesterday, sellerData } = await this.getWscDashboard(storeId);
    const unReadMessageCnt = await new ConversationService(ctx).getUnreadConversations({
      adminId: userId,
      kdtId: ctx.kdtId,
    });

    // 教育定制数据
    let yzEduData = {};
    if (ctx.isYZEdu) {
      try {
        yzEduData = await new DashboardFacade(ctx).workbenchOverview(ctx.kdtId);
      } catch (e) {
        null;
      }
    }
    // 教育总部定制数据
    // TODO wsc 连锁需要修改
    let hqStoreData = {};
    if (checkEduHqStore(ctx.state.shopInfo)) {
      try {
        const shopList = await this.fetchShopList([2, 4]);
        const kdtIds = shopList.map(item => item.storeKdtId);
        kdtIds.push(ctx.kdtId);
        hqStoreData = await new RealTimeService(ctx).getRealTimeOverView({
          hqKdtId: ctx.kdtId,
          kdtIds,
          canalType: 10,
          shopType: 'edu',
          currentDay: format(Date.now(), 'YYYYMMDD'),
        });
      } catch (error) {
        null;
      }
    }

    const data = {
      today,
      yesterday,
      sellerData: {
        ...sellerData,
        unReadMessageCnt,
      },
      yzEduData,
      hqStoreData,
    };
    return data;
  }

  /**
   * 获取服务通知
   * @param {*} ctx
   * @memberof IndexController
   */
  async getServiceNotice(ctx) {
    const { kdtId } = ctx;
    const getCommonNotice = new AppStatusRemoteService(ctx).getMyDeskMsgNotifyDoc({
      kdtId,
      state: 'ALL',
    });

    const getArrearsNotice = new FeeStatQueryService(ctx).queryArrearsInfo(kdtId);
    const getCommonAlerts = new FeeAlertQueryService(ctx).queryCommonAlerts(kdtId);
    const getQuickSettleNotice = new FinProdAlertCheckService(ctx).quickSettleAlertCheck({
      kdtId: `${kdtId}`,
    });
    const commonInvoke = new RiskCommonService(ctx).commonInvoke({
      serviceCode: 'kdt_warning',
      params: {
        kdtId,
      },
    });

    const noop = e => {
      // console.error(e); // TODO mock error
      ctx.logger.error(e.message, e, e.extra);
    };
    const [
      commonNotice,
      arrearsNotice,
      generalNotice,
      quickSettleNotice,
      riskNotice,
    ] = await Promise.all([
      getCommonNotice.catch(noop),
      getArrearsNotice.catch(noop),
      getCommonAlerts.catch(noop),
      getQuickSettleNotice.catch(noop),
      commonInvoke.catch(noop),
    ]);
    ctx.successRes({
      commonNotice,
      arrearsNotice,
      generalNotice,
      quickSettleNotice,
      riskNotice,
    });
  }

  /**
   * 忽略服务通知
   * @param {*} ctx
   * @memberof IndexController
   */
  async ignoreServiceNotice(ctx) {
    const { appIds, state, notifyType, notifyId } = ctx.getPostData();
    const data = await new AppStatusRemoteService(ctx).customKnown({
      kdtId: ctx.kdtId,
      appIds,
      state,
      notifyType,
      notifyId,
    });
    ctx.successRes(data);
  }

  /**
   * 忽略risk服务通知
   * @param {*} ctx
   * @memberof IndexController
   */
  async cancelWarning(ctx) {
    const { id } = ctx.getPostData();
    const data = await new RiskCommonService(ctx).commonInvoke({
      serviceCode: 'cancel_warning',
      params: { id },
    });
    ctx.successRes(data);
  }

  /**
   * 获取店铺基本信息
   * @param {*} ctx
   * @memberof IndexController
   */
  async getShopBaseInfo(ctx) {
    const { kdtId } = ctx;
    const [
      shopProdInfo,
      shopInfo,
      securedInfo,
      isProviderShop,
      unifiedCertData,
      oldShopInfo,
    ] = await Promise.all([
      new ProdReadService(ctx).queryShopProd(kdtId).catch(() => null),
      new MarketRemoteService(ctx).getServerInfo(kdtId).catch(() => null),
      new SecuredService(ctx).query(kdtId).catch(() => null),
      new ShopRemoteService(ctx).isProviderShop(kdtId).catch(() => null),
      // 店铺认证状态查询
      new MerchantCertInfoService(ctx)
        .queryPrincipalStatus({
          sourceId: `${kdtId}`,
          sourceIdType: CERT_SOURCEID_TYPE,
          unifiedCertType: CERT_UNIFIEDCERT_TYPE,
        })
        .catch(e => ctx.logger.error(e.message, e, e.extra)),
      new ShopConfigReadService(ctx)
        .queryShopConfigs(kdtId, [
          'shop_operate_duration_years',
          'shop_operate_duration_tag_switch',
        ])
        .catch(e => ctx.logger.error(e.message, e, e.extra)),
    ]);

    const data = {
      ...(shopInfo || {}),
      lifecycleStatus: shopProdInfo && shopProdInfo.lifecycleStatus,
      expireTime: shopProdInfo && shopProdInfo.endTime,
      isSecured: securedInfo && securedInfo.joined,
      unifiedCertData,
      isProviderShop,
      oldShopInfo,
    };

    return data;
  }

  async getOrderCount(ctx) {
    try {
      const result = await new OrderStatisticsService(ctx).getOrderCount({
        sourceName: fromApp,
        requestId: `${Date.now()}`,
        kdtId: ctx.kdtId,
        timestamp: Date.now(),
      });
      return result[0].orderCount;
    } catch (e) {
      return null;
    }
  }
  /**
   * 获取首页店铺临期提醒
   * @param {*} ctx
   * @memberof IndexController
   */
  async getNoticeAlert(ctx) {
    const shopInfo = ctx.getState('shopInfo');
    // 是否为连锁3.0（不包含教育）
    const isPureChainStore = checkPureWscChainStore(shopInfo) || checkUnifiedShop(shopInfo);

    const shopBizTypeName = getShopBizTypeName(ctx);
    const { lifecycleStatus, endTime } = await new ProdReadService(ctx).queryShopProd(ctx.kdtId);
    const { hadValidOrder, version, probationGiftDays = 0 } = await new MarketRemoteService(
      ctx
    ).getServerInfo(ctx.kdtId);
    const now = Date.now();
    const lastDays = differenceInDays(endTime, now);
    let data;
    // 有效期
    if (lifecycleStatus === 'valid') {
      if (lastDays > 60) {
        data = null;
      } else if (lastDays >= 30 && lastDays <= 60 && !isPureChainStore) {
        // 微商城连锁(不包含教育))不处理30 ~ 60
        const yzCoin = versionToYZCoinMap[version];
        const message = yzCoin
          ? `店铺即将到期, 提前续费可获得 ${yzCoin} 有赞币`
          : '店铺即将到期，该服务暂未开通线上订购，如需订购请联系客户经理。';
        data = {
          message,
          btnText: '立即续费',
          type: 'waning',
        };
      } else if (lastDays >= 1 && lastDays < 30) {
        data = {
          message: `店铺服务期剩余 ${lastDays} 天；到期未续费将影响正常使用，请及时续订${shopBizTypeName}`,
          btnText: '立即续费',
          type: 'warning',
        };
        // 少于一天
      } else if (lastDays < 1) {
        data = {
          message: `店铺服务期剩余不足 24 小时；到期未续费将影响正常使用，请及时续订${shopBizTypeName}`,
          btnText: '立即续费',
          type: 'warning',
        };
      }
      // 保护期
    } else if (lifecycleStatus === 'protect') {
      data = {
        message: `店铺服务已到期，为了不影响正常营业，请及时续订${shopBizTypeName}。`,
        btnText: '立即续费',
        type: 'danger',
      };
      // 关店
    } else if (lifecycleStatus === 'close') {
      // 区分试用期过期的关店和服务期到期的关店
      const text = hadValidOrder ? '服务到期' : '免费试用结束';
      data = {
        message: `店铺${text}，已打烊。如需恢复正常营业，请订购${shopBizTypeName}。`,
        btnText: '立即续费',
        type: 'danger',
      };
      // 试用期结束时展示奖励发放状态
      // 微商城，零售连锁3.0（不包含教育店铺）不展示奖励发放状态
      if (!hadValidOrder && !isPureChainStore) {
        const status = await new QuestionnaireRemoteService(ctx).getYopQuestionnaireStatus(
          ctx.kdtId
        );
        // 当奖励发放状态为待发放和已发放时展示提示
        if (status === 2 || status === 3) {
          data.status = status;
        }
      }
      // 暂停服务
    } else if (lifecycleStatus === 'pause') {
      data = {
        message: '店铺已暂停服务，无法正常营业。',
        btnText: '立即续费',
        type: 'warning',
      };
      // 试用期
    } else if (lifecycleStatus === 'try') {
      const kdtId = ctx.kdtId;
      const orderTotalPromise = new AbilityService(ctx).queryShopAbilityInfo(
        +kdtId,
        'trade_online_order_rule_query_ability'
      );
      const [orderCount, { validDetail: orderMaxLimit }] = await Promise.all([
        this.getOrderCount(ctx),
        orderTotalPromise.catch(() => {}),
      ]);

      let orderCountWarning = '';
      // 接口异常兼容处理，未获取到数据不展示订单限制相关信息
      if (orderCount !== null && orderMaxLimit) {
        const { count: orderMax } = orderMaxLimit;
        const orderMaxTip = `，最多可试用 ${orderMax < 0 ? 0 : orderMax} 笔订单，`;
        const purchaseWarning = '请尽快订购';
        orderCountWarning =
          orderMax > orderCount
            ? `${orderMaxTip}当前剩余 ${orderMax -
                orderCount} 单，为不影响正常经营，${purchaseWarning}`
            : `${orderMaxTip}当前买家已无法下单，${purchaseWarning}`;
        // 有赞教育orderCountWarning为结尾字符串，不能加上；
        if (!(ctx.isYZEdu || isPureChainStore || probationGiftDays < 1)) {
          orderCountWarning += '；';
        }
      }

      // 剩余试用有效期
      const leftTryDays = lastDays + 1;
      // 有赞教育店铺, wsc连锁3.0不赠送服务期，所以去除教育店铺服务期赠送提醒
      // 赠送礼包服务期小于0 不展示赠送文案
      const purchaseTip =
        ctx.isYZEdu || isPureChainStore || probationGiftDays < 1
          ? '。'
          : `现在订购赠送 ${probationGiftDays} 天服务期。`;

      if (lastDays === 0) {
        const status = await new QuestionnaireRemoteService(ctx).getYopQuestionnaireStatus(kdtId);
        data = {
          message: `店铺免费试用期不足 24 小时${orderCountWarning}${purchaseTip}`,
          btnText: '立即订购',
          type: 'warning',
          status,
        };
      } else {
        const msg = `店铺免费试用期不足 ${leftTryDays} 天${orderCountWarning}${purchaseTip}`;
        data = {
          message: msg,
          btnText: '立即订购',
          type: 'warning',
        };
      }
    }

    return data;
  }

  /**
   * 获取海景房广告
   * @param {*} ctx
   * @memberof IndexController
   */
  async getQrAd(ctx) {
    const param = {
      kdtId: ctx.kdtId,
      shopType: this.type,
      httpSource: 2,
    };
    const data = await new SidebarPictureAdService(ctx).getPictureAdForShop(param);
    const { prLink, jumpLink } = data;

    return {
      hdImg: prLink,
      link: jumpLink,
    };
  }

  /**
   * 获取店铺文章列表
   * @param {*} ctx
   */
  async getSubjectService(ctx) {
    const param = {
      kdtId: ctx.kdtId,
      shopType: this.type,
      httpSource: 2,
    };
    const data = await new SidebarSubjectService(ctx).listSubjectForShop(param);

    return data;
  }

  /**
   * 获取调查问卷
   */
  async getSurvey(ctx) {
    const params = {
      kdtId: ctx.kdtId,
      shopType: this.type,
      httpSource: 2,
    };
    const data = await new SurveyPaperRangeService(ctx).getSurvey(params);
    return data;
  }

  /**
   * 获取店铺账单
   */
  async getShopBill(ctx) {
    // .... 下了就删掉
    const salt = 'lili-brother-pei';
    const md5 = str => {
      return crypto
        .createHash('md5')
        .update(str)
        .digest('hex');
    };
    const encrypto = md5(md5(String(ctx.kdtId)) + salt);
    const result = await new WscDashboardService(ctx).getBill(encrypto);
    ctx.successRes(!!result);
  }

  async getCustomSummaryData(ctx) {
    ctx.successRes(await this.__getCustomSummaryData(ctx));
  }

  /**
   * 获取自定义数据
   */
  async __getCustomSummaryData(ctx) {
    const kdtId = ctx.kdtId;
    const userId = ctx.userId;

    const { userRole, multiRoles } = await getMultiRoles(ctx);
    if (!userRole) {
      ctx.successRes();
      return;
    }
    const { roleId: samRoleId, roleType: samRoleType, biz: samBiz } = userRole;
    const multiSamRoles = getMultiSamRoles(multiRoles);
    const shopBizType = await this.getBizType4ShowCaseCenterV1(ctx);
    const result = await new DataOverviewService(ctx).queryDataOverviewSetting({
      shopBizType,
      samBiz,
      samRoleId: multiSamRoles.length > 1 ? null : samRoleId,
      kdtId,
      samRoleType: SAM_ROLE_TYPE[samRoleType] || SAM_ROLE_TYPE.DEFAULT_ROLE,
      adminId: userId,
      platform: PLATFORM_ENUM.PC,
      multiSamRoles,
    });
    if (!result) {
      ctx.successRes();
      return;
    }
    const { cmpList } = result;

    if (ctx.hostname.indexOf('fx.youzan.com') === 0) {
      result.cmpList = formatCmpListToSupplier(cmpList);
    }

    const types = cmpList.map(item => item.type);
    try {
      const data = await new DataAdapter(ctx).getData(types, samRoleId, multiRoles, true);
      return {
        ...result,
        data,
        updateTime: Date.now(),
      };
    } catch (e) {
      return {
        ...result,
        data: [],
      };
    }
  }

  /**
   * 教育店铺微商城扩展包能力检查
   */
  async eduAbilityCheck(ctx) {
    if (!ctx.isYZEdu) return Promise.resolve();
    return new AbilityService(ctx)
      .queryShopAbilityInfo(+ctx.kdtId, 'wsc_ext_pkg_plugin_ability')
      .then(abilityStatus => abilityStatus.valid)
      .catch(() => false);
  }

  /**
   * 获取总部分店列表，为了获取总部汇总数据，需要拿着分店列表去查，因为会有权限和合伙人机制，数据不吃这一套
   * copy from：http://gitlab.qima-inc.com/wsc-node/wsc-pc-statcenter/blob/hotfix/wsc-chain-0702/app/controllers/base/BaseController.js
   * TODO 让微商城后端包装接口，不要放在前端处理
   */
  async fetchShopList(shopRoles) {
    const SHOP_LIST_PAGE_SIZE = 50;
    const ctx = this.ctx;
    const { kdtId, userId } = ctx;
    let shopList = [];
    const commonParam = {
      adminId: userId,
      kdtId,
      pageSize: SHOP_LIST_PAGE_SIZE,
      retailSource: 'wsc-pc-v4',
      shopRoleList: shopRoles,

      // 只用来查kdtId，去除不必要的查询内容
      appendShopMetaInfo: false,
      appendOfflineBusinessHours: false,
      appendShopLifecycleEndTime: false,
      appendPosPointNum: false,
    };
    const service = new HQStoreSearchService(ctx);
    const result = await service.searchWithDataPermission({
      ...commonParam,
      pageNo: 1,
    });
    shopList = shopList.concat(result.items);

    // 店铺数量超过一次请求的情况
    if (result.paginator.totalCount > SHOP_LIST_PAGE_SIZE) {
      const totalTimes = Math.ceil(result.paginator.totalCount / SHOP_LIST_PAGE_SIZE);
      const reqList = [];
      for (let time = 2; time <= totalTimes; time++) {
        const requestParam = {
          ...commonParam,
          pageNo: time,
        };
        reqList.push(service.searchWithDataPermission(requestParam));
      }
      const resList = await Promise.all(reqList);
      resList.forEach(res => {
        shopList = shopList.concat(res.items);
      });
    }
    return shopList;
  }

  // 兼容连锁情况的概况数据
  async getWscDashboard(storeId) {
    const __isSubShop = this.isSubShop();
    const __isHqShop = this.isHqShop();
    const kdtId = this.ctx.kdtId;
    const now = Date.now();
    const wscDashboardService = new WscDashboardService(this.ctx);
    const todayDate = format(now, 'YYYYMMDD');
    const todayDateKey = format(now, 'YYYY-MM-DD');
    const yesterdayDate = format(now - oneDay, 'YYYYMMDD');
    const yesterdayDateKey = format(now - oneDay, 'YYYY-MM-DD');
    if (__isSubShop || __isHqShop) {
      // 是连锁
      const realTimeService = new WscDashboardRealTimeService(this.ctx);
      const shopMetaInfo = this.ctx.getState('shopMetaInfo') || {};
      const { shopRole = 0 } = shopMetaInfo;
      const hqKdtId = this.getHqKdtId();
      const param = {
        kdtList: [kdtId],
        dateType: 0,
        shopRole,
        hqKdtId,
      };
      if (__isHqShop) {
        // 总部
        const shopList = await this.fetchShopList([2, 4]);
        const kdtIds = shopList.map(item => item.storeKdtId);
        param.kdtList = [kdtId, ...kdtIds];
      }
      const [{ sellerData }, summaryData] = await Promise.all([
        wscDashboardService.getWscDashboard({
          currentDay: todayDate,
          storeId,
          kdtId,
        }),
        realTimeService.getRealTimeOverview({
          ...param,
          timeParam: {
            startDay: yesterdayDate,
            endTDay: todayDate,
          },
        }),
      ]);
      return {
        sellerData,
        today: this.formatOverviewData(summaryData[todayDateKey]),
        yesterday: this.formatOverviewData(summaryData[yesterdayDateKey]),
      };
    } else {
      // 其他
      const param = {
        currentDay: todayDate,
        storeId,
        kdtId,
      };
      // eslint-disable-next-line no-return-await
      return await wscDashboardService.getWscDashboard(param);
    }
  }

  formatOverviewData(data) {
    return {
      payedAmount: data.pay_amount,
      payedCnt: data.pay_count,
      payedUserCnt: data.pay_uv,
      pageUserCnt: data.uv,
    };
  }

  async getNaWscSummaryData(ctx) {
    const userRole = await getRole(ctx);
    if (!userRole) {
      ctx.successRes();
      return;
    }
    const { roleId: samRoleId, biz: samBiz } = userRole;

    const result = await new DataOverviewService(ctx).queryDataOverviewDefaultForBriefWscByRegion({
      samBiz,
      wscType: 'wscNA',
    });
    const { cmpList } = result;
    const types = cmpList.map(item => item.type);
    const data = await new DataAdapter(ctx).getData(types, samRoleId);
    ctx.successRes({
      ...result,
      data,
    });
  }

  async getNaWscCommonModuleSetting(ctx) {
    const userRole = await getRole(ctx);
    if (!userRole) {
      ctx.successRes();
      return;
    }
    const { biz: samBiz } = userRole;
    const result = await new CommonModuleService(ctx).queryCommonModuleForRegionWsc({
      samBiz,
      wscType: 'wscNA',
    });
    ctx.json(0, '', result);
  }
}

module.exports = NaDashboardController;
