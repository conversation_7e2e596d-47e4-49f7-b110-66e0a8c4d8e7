const { assign } = require('lodash');
const {
  checkWscBranchStore,
  checkWscHqStore,
  checkPureWscChainStore,
  checkUnifiedHqStore,
  checkUnifiedOfflineBranchStore,
  checkUnifiedOnlineBranchStore,
  checkUnifiedShop,
  checkEduSingleStore,
  checkRetailMinimalistHqStore,
  checkRetailMinimalistBranchStore,
  checkRetailMinimalistShop,
  checkRetailSingleStore,
  checkEduHqStore,
  checkEduBranchStore,
  checkEduWktStore,
} = require('@youzan/utils-shop');
const MarketRemoteService = require('../../../services/api/yop/MarketRemoteService');
const { WSC_SAM_ROLE, SHOP_VERSION_CODE } = require('../../../constants');

// 这个实际是shopBizType
const BIZ_TYPE = {
  WSC: 1,
  WSC_SUPPLIER: 2,
  SUPPLIER: 3,
  EDU: 4,
  EDU_MICRO_CLASSROOM_VERSION: 21,
  WSC_HQ_STORE: 5, // 微商城连锁总店
  WSC_BRANCH_STORE: 6, // 微商城连锁分店
  RETAIL_HQ_STORE: 7, // 零售连锁总店
  RETAIL_OFFLINE_BRANCH_STORE: 8, // 零售连锁门店
  RETAIL_ONLINE_BRANCH_STORE: 9, // 零售连锁网店
  EDU_HEAD: 14, // 教育连锁总部
  EDU_PART: 15, // 教育连锁网店
  EDU_STANDARD: 16, // 教育普通版
  RETAIL_MINI_HQ: 17, // 零售极简版总部
  RETAIL_MINI_ONLINE_STORE: 19, // 零售极简版网店
  LITE_WSC: 10, // 微商城精简版，
  LITE_ONLINE_STORE: 9, // 同城网店
};

const SHOP_TYPE = {
  WSC: 'wsc',
  RETAIL: 'retail',
  EDU: 'edu',
};

/**
 * show-case-center shopBizType
 * 根据店铺类型获取对应的shopBizType
 * show-case-center专用
 * 区分了微商城店铺教育店铺 type
 * @param {*} ctx
 */
async function getBizType4ShowCaseCenter(ctx, { isLiteOnlineManager = false }) {
  const shopInfo = ctx.getState('shopInfo');
  const version = await getShopVersion(ctx);
  let bizType = BIZ_TYPE.WSC;

  const isInFx = ctx.hostname.indexOf('fx.youzan.com') === 0;

  if (isInFx) {
    bizType = BIZ_TYPE.WSC_SUPPLIER;
  }

  if (ctx.isSupplier) {
    return BIZ_TYPE.SUPPLIER;
  }

  if (isLiteOnlineManager) {
    return BIZ_TYPE.LITE_ONLINE_STORE;
  }

  if (version === SHOP_VERSION_CODE.LITE) {
    return BIZ_TYPE.LITE_WSC;
  }

  if (checkEduMicroClassVersion(ctx)) {
    return BIZ_TYPE.EDU_MICRO_CLASSROOM_VERSION;
  }

  // 教育单店区分普通版，专业版
  if (checkEduSingleStore(shopInfo)) {
    return version === SHOP_VERSION_CODE.STANDARD ? BIZ_TYPE.EDU_STANDARD : BIZ_TYPE.EDU;
  }

  if (checkRetailMinimalistHqStore(shopInfo)) {
    bizType = BIZ_TYPE.RETAIL_MINI_HQ;
  }

  if (checkRetailMinimalistBranchStore(shopInfo)) {
    bizType = BIZ_TYPE.RETAIL_MINI_ONLINE_STORE;
  }

  if (checkWscHqStore(shopInfo)) {
    bizType = BIZ_TYPE.WSC_HQ_STORE;
  }

  if (checkWscBranchStore(shopInfo)) {
    bizType = BIZ_TYPE.WSC_BRANCH_STORE;
  }

  // 零售连锁
  if (checkUnifiedHqStore(shopInfo) || checkRetailSingleStore(shopInfo)) {
    bizType = BIZ_TYPE.RETAIL_HQ_STORE;
  }

  if (checkUnifiedOfflineBranchStore(shopInfo)) {
    bizType = BIZ_TYPE.RETAIL_OFFLINE_BRANCH_STORE;
  }

  if (checkUnifiedOnlineBranchStore(shopInfo)) {
    bizType = BIZ_TYPE.RETAIL_ONLINE_BRANCH_STORE;
  }

  // 教育连锁
  if (checkEduHqStore(shopInfo)) {
    return BIZ_TYPE.EDU_HEAD;
  }

  if (checkEduBranchStore(shopInfo)) {
    return BIZ_TYPE.EDU_PART;
  }

  return bizType;
}

/**
 * show-case-center BizType
 * 老版本biztype方法
 * 与新版本的getBizType4ShowCaseCenter区别 type上会区别 教育店铺的type
 * 实时概况DataOverviewService接口下使用获取shopBizType方法
 * @param {*} ctx
 */
async function getBizType4ShowCaseCenterV1(ctx, options = {}) {
  const { isLiteOnlineManager } = options;
  const shopInfo = ctx.getState('shopInfo');
  // show-case-center中没有区分教育店铺和微商城店铺，教育默认使用wsc type
  let bizType = BIZ_TYPE.WSC;

  if (isLiteOnlineManager) {
    return BIZ_TYPE.LITE_ONLINE_STORE;
  }

  if (checkEduMicroClassVersion(ctx)) {
    return BIZ_TYPE.EDU_MICRO_CLASSROOM_VERSION;
  }
  // 教育单店区分普通版，专业版
  if (checkEduSingleStore(shopInfo)) {
    const version = await getShopVersion(ctx);
    return version === SHOP_VERSION_CODE.STANDARD ? BIZ_TYPE.EDU_STANDARD : BIZ_TYPE.WSC;
  }

  if (checkRetailMinimalistHqStore(shopInfo)) {
    bizType = BIZ_TYPE.RETAIL_MINI_HQ;
  }

  if (checkRetailMinimalistBranchStore(shopInfo)) {
    bizType = BIZ_TYPE.RETAIL_MINI_ONLINE_STORE;
  }

  if (checkWscHqStore(shopInfo)) {
    bizType = BIZ_TYPE.WSC_HQ_STORE;
  }

  if (checkWscBranchStore(shopInfo)) {
    bizType = BIZ_TYPE.WSC_BRANCH_STORE;
  }

  // 零售连锁
  if (checkUnifiedHqStore(shopInfo) || checkRetailSingleStore(shopInfo)) {
    bizType = BIZ_TYPE.RETAIL_HQ_STORE;
  }

  if (checkUnifiedOfflineBranchStore(shopInfo)) {
    bizType = BIZ_TYPE.RETAIL_OFFLINE_BRANCH_STORE;
  }

  if (checkUnifiedOnlineBranchStore(shopInfo)) {
    bizType = BIZ_TYPE.RETAIL_ONLINE_BRANCH_STORE;
  }
  return bizType;
}

/**
 * mall-sop BizType
 * @param {*} ctx
 */
async function getBizType(ctx, options = {}) {
  const { isLiteOnlineManager } = options;
  let bizType = BIZ_TYPE.WSC;
  const shopInfo = ctx.getState('shopInfo');
  const isInFx = ctx.hostname.indexOf('fx.youzan.com') === 0;

  if (isInFx) {
    bizType = BIZ_TYPE.WSC_SUPPLIER;
  }

  if (ctx.isSupplier) {
    return BIZ_TYPE.SUPPLIER;
  }

  if (isLiteOnlineManager) {
    return BIZ_TYPE.LITE_ONLINE_STORE;
  }

  // 教育供货后台使用微商城的配置
  if (ctx.isYZEdu && !isInFx) {
    // 教育单店区分普通版，专业版
    if (checkEduSingleStore(shopInfo)) {
      const version = await getShopVersion(ctx);
      return version === SHOP_VERSION_CODE.STANDARD ? BIZ_TYPE.EDU_STANDARD : BIZ_TYPE.EDU;
    }
    return BIZ_TYPE.EDU;
  }

  if (checkWscHqStore(shopInfo)) {
    return BIZ_TYPE.WSC_HQ_STORE;
  }
  if (checkRetailMinimalistHqStore(shopInfo)) {
    return BIZ_TYPE.RETAIL_MINI_HQ;
  }

  if (checkRetailMinimalistBranchStore(shopInfo)) {
    return BIZ_TYPE.RETAIL_MINI_ONLINE_STORE;
  }
  if (checkWscBranchStore(shopInfo)) {
    return BIZ_TYPE.WSC_BRANCH_STORE;
  }

  // 零售连锁
  if (checkUnifiedHqStore(shopInfo) || checkRetailSingleStore(shopInfo)) {
    return BIZ_TYPE.RETAIL_HQ_STORE;
  }

  if (checkUnifiedOfflineBranchStore(shopInfo)) {
    return BIZ_TYPE.RETAIL_OFFLINE_BRANCH_STORE;
  }

  if (checkUnifiedOnlineBranchStore(shopInfo)) {
    return BIZ_TYPE.RETAIL_ONLINE_BRANCH_STORE;
  }

  return bizType;
}

function getShopType(ctx) {
  const shopType = SHOP_TYPE.WSC;

  if (ctx.isYZEdu) {
    return SHOP_TYPE.EDU;
  }

  if (ctx.isSuperStore) {
    return SHOP_TYPE.RETAIL;
  }
  return shopType;
}

function resetUserRole(userRole, ctx) {
  // 教育供货后台使用微商城高级管理员的配置
  if (ctx.isYZEdu && ctx.hostname.indexOf('fx.youzan.com') === 0) {
    return assign(userRole, {
      roleId: WSC_SAM_ROLE.SUPER_ADMIN,
      roleType: 'DEFAULT_ROLE',
      biz: 'wsc',
    });
  }
  const isInFx = ctx.hostname.indexOf('fx.youzan.com') === 0;
  if (userRole && isInFx) {
    return assign(userRole, {
      roleId: WSC_SAM_ROLE.SUPER_ADMIN,
      roleType: 'DEFAULT_ROLE',
    });
  }
  return userRole;
}

function getShopBizTypeName(ctx) {
  const shopVersionName = '有赞微商城';
  const shopInfo = ctx.getState('shopInfo');

  if (ctx.isYZEdu) {
    return '有赞教育';
  }

  if (checkPureWscChainStore(shopInfo) || checkRetailMinimalistShop(shopInfo)) {
    return '有赞微商城连锁';
  }

  if (checkUnifiedShop(shopInfo)) {
    return '有赞零售连锁';
  }

  return shopVersionName;
}

const checkEduMicroClassVersion = (ctx) => {
  // 微课堂店铺判断
  return checkEduWktStore(ctx.getState('shopInfo'));
};

const getShopVersion = async (ctx) => {
  const { kdtId } = ctx;
  let version;
  try {
    const result = await new MarketRemoteService(ctx).getServerInfo(kdtId);
    version = result && result.version;
  } catch (e) {
    ctx.logger.error('获取店铺版本信息异常', e, e.extra);
  }
  return version;
};

module.exports = {
  getBizType4ShowCaseCenter,
  getBizType,
  resetUserRole,
  getShopType,
  BIZ_TYPE,
  getShopBizTypeName,
  getBizType4ShowCaseCenterV1,
};
