const { resetUserRole } = require('../common/biz-type');
const SingleStaffService = require('../../../services/api/sam/SingleStaffService');
const { roleTypeMap } = require('../constants');
const { checkEduChainStoreV4 } = require('@youzan/utils-shop');

function getSamBiz(ctx) {
  const shopInfo = ctx.getState('shopInfo');
  let samBiz = 'wsc';
  if (ctx.isSuperStore) {
    samBiz = 'retail';
    if (checkEduChainStoreV4(shopInfo)) {
      return samBiz;
    }
  }
  if (ctx.isSupplier) {
    samBiz = 'supplier';
  }
  if (ctx.isYZEdu) {
    samBiz = 'wsc-edu';
  }
  return samBiz;
}

async function getRole(ctx) {
  const samBiz = getSamBiz(ctx);
  try {
    // 获取某个员工详情
    const { roles: userRoles } = await new SingleStaffService(ctx).get({
      kdtId: ctx.kdtId,
      adminId: ctx.userId,
    });
    // 部分微商城店铺脏数据存在有一个以上角色的员工，通过biz过滤
    let userRole = userRoles.find(({ biz }) => biz === samBiz);
    // 处理分销，不影响lite
    userRole = resetUserRole(userRole, ctx);
    return userRole;
  } catch (e) {
    // console.log(e);
    return null;
  }
}

// 比上面多一个。multiRoles
async function getMultiRoles(ctx) {
  const samBiz = getSamBiz(ctx);
  try {
    let { roles: multiRoles } = await new SingleStaffService(ctx).get({
      kdtId: ctx.kdtId,
      adminId: ctx.userId,
    });

    // 部分微商城店铺脏数据存在有一个以上角色的员工，通过biz过滤
    let userRole = multiRoles.find(({ biz }) => biz === samBiz);

    userRole = resetUserRole(userRole, ctx);
    multiRoles = multiRoles.map(item => resetUserRole(item, ctx));

    return { userRole, multiRoles };
  } catch (e) {
    return {};
  }
}

const getMultiSamRoles = (multiRoles = []) => {
  return multiRoles.map(item => {
    const { biz, roleId, roleType } = item;
    return {
      samBiz: biz,
      samRoleId: roleId,
      samRoleType: roleTypeMap[roleType] || roleTypeMap.DEFAULT_ROLE,
    };
  });
};

module.exports = {
  getRole,
  getMultiRoles,
  getMultiSamRoles,
};
