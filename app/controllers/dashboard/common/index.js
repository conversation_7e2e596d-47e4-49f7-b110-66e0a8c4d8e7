import { isEmpty } from 'lodash';

const { SUGGEST_CODE } = require('../constants/index');

const {
  checkFrontWarehouse,
  checkPartnerStore,
  checkRetailShop,
  checkRetailSingleStore,
  checkUnifiedHqStore,
  checkUnifiedOfflineBranchStore,
  checkUnifiedOnlineBranchStore,
  SHOP_ABILITY_STATUS,
  ORG_CATEGORY,
} = require('@youzan/utils-shop');

const getSuggestCode = (isSuperStore) => {
  const code = SUGGEST_CODE.WSC;
  if (isSuperStore) {
    return SUGGEST_CODE.RETAIL;
  }
  return code;
};

function getMenuVersion(ctx, key, orgCategory) {
  const shopInfo = ctx.getState('shopInfo');
  if (!checkRetailShop(shopInfo)) {
    return key;
  }

  const withoutWsc = key.replace('wsc_', '');

  // 总部、高级版同城云店
  if (checkUnifiedHqStore(shopInfo)) {
    return `retail_${withoutWsc}`;
  }

  // 单店
  if (checkRetailSingleStore(shopInfo)) {
    return `retail_single_${withoutWsc}`;
  }

  // 前置仓
  if (checkFrontWarehouse(shopInfo)) {
    return `retail_warehouse_${withoutWsc}`;
  }

  // 合伙人
  if (checkPartnerStore(shopInfo)) {
    return `retail_partner_${withoutWsc}`;
  }

  // 多渠道
  if (orgCategory) {
    if (orgCategory === ORG_CATEGORY.STORE) {
      return `retail_offline_${withoutWsc}`;
    }
  }

  // 非多渠道门店
  if (checkUnifiedOfflineBranchStore(shopInfo)) {
    return `retail_offline_${withoutWsc}`;
  }

  // 区域网店、网店、商城
  if (checkUnifiedOnlineBranchStore(shopInfo)) {
    return `retail_online_${withoutWsc}`;
  }

  return key;
}

function checkMenuItemIsAccessible(item) {
  return item.accessible && checkMenuItemIsAbilityValid(item);
}

function checkMenuItemIsAbilityValid(item) {
  if (item.shopAbilityDTO && !isEmpty(item.shopAbilityDTO)) {
    const { abilityStatus, calcResult } = item.shopAbilityDTO;
    return (
      calcResult ||
      abilityStatus === SHOP_ABILITY_STATUS.AVAILABLE ||
      abilityStatus === SHOP_ABILITY_STATUS.EXPIRED
    );
  }
  return true;
}

function getShopConfigKey(ctx, key) {
  const shopInfo = ctx.getState('shopInfo');
  if (checkRetailShop(shopInfo)) {
    return `retail_${key}`;
  }
  return key;
}

module.exports = {
  getSuggestCode,
  getMenuVersion,
  checkMenuItemIsAccessible,
  checkMenuItemIsAbilityValid,
  getShopConfigKey,
};
