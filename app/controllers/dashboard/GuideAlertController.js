const BaseController = require('../base/BaseController');
const YunRemoteService = require('../../services/api/yop/YunRemoteService');
const OrderRemoteService = require('../../services/api/yop/OrderRemoteService');
const BaiduMpTipService = require('../../services/api/growth/growth/BaiduMpTipService');
const NoticeService = require('../../services/api/shopcenter/shopfront/NoticeService');
const WxAuthFrontQueryService = require('../../services/api/pay/unified/WxAuthFrontQueryService');
const AbilityReadService = require('../../services/api/shop-center/AbilityReadService');
const { checkUnifiedOfflineBranchStore } = require('@youzan/utils-shop');

/**
 * 工作台引导弹窗controller
 * 零时处理，统一收口各个业务方弹窗状态
 */
class GuideAlertController extends BaseController {
  constructor(ctx) {
    super(ctx);
    this.ctx.biz = '工作台-引导弹窗';
  }

  // 获取alert展示状态
  async getNeedGuideAlert(ctx) {
    const { kdtId, userId } = ctx;
    const params = {
      kdtId,
      userId,
      source: 2, // 1: app; 2: pc
    };
    // 前端临时处理引导展示状态，疫情弹窗优先于云服务优先于百度小程序引导。
    let epidemic = null;
    let yun = null;
    let baidu = null;
    let wxpay = null;
    let hasAlert = false;
    try {
      // 微信支付实名认证弹窗，优先展示
      wxpay = await new WxAuthFrontQueryService(ctx).queryRemind(ctx.kdtId);
      hasAlert = wxpay && wxpay.needRemind;

      if (!hasAlert) {
        // FIXME：因为前端引导弹窗没有收敛到一个组件，先控制优先级，优先展示疫情弹窗，若并行请求会出现两个弹窗的情况。
        epidemic = await this.epidemicNotice(ctx);
        hasAlert = hasAlert || (epidemic && epidemic.length > 0);
      }

      if (!hasAlert) {
        yun = await this.isNeedYunUpgrade(ctx, params);
        hasAlert = hasAlert || yun;
      }

      if (!hasAlert) {
        // 百度小程序引导，只要展示一次后面就不需要再次展示（接口会在查询状态后，改变引导展示状态）
        baidu = await new BaiduMpTipService(ctx).displayBaiduMpTip(kdtId, userId);
        hasAlert = hasAlert || baidu;
      }
    } catch (error) {} // eslint-disable-line
    ctx.successRes({ yun, baidu, epidemic });
  }

  /* 疫情弹窗 */
  async epidemicNotice(ctx) {
    const params = {
      kdtId: ctx.kdtId,
      adminId: ctx.userId,
      pageId: 0, // 概况页
      terminalType: 0, // 终端类型 pc
    };
    const data = await new NoticeService(ctx).queryActiveNotice(params);

    return data;
  }

  /* 微信支付实名认证 */
  async getNeedWxPayAuthAlert(ctx) {
    const data = await new WxAuthFrontQueryService(ctx).queryRemind(ctx.kdtId);
    ctx.successRes(data);
  }

  /* 标记已读 */
  async markRead(ctx) {
    const { userId, kdtId } = ctx;
    const { noticeId } = ctx.getPostData();
    const params = {
      kdtId,
      adminId: userId,
      noticeId,
    };
    const data = await new NoticeService(ctx).markRead(params);
    ctx.successRes(data);
  }

  /* 标记忽略 */
  async markIgnore(ctx) {
    const { userId, kdtId } = ctx;
    const { noticeId } = ctx.getPostData();
    const params = {
      kdtId,
      adminId: userId,
      noticeId,
    };
    const data = await new NoticeService(ctx).markIgnore(params);
    ctx.successRes(data);
  }

  /* 云服务 */
  async isNeedYunUpgrade(ctx, params) {
    const { pop } = await new YunRemoteService(ctx).getYunAction(params);
    return pop;
  }

  async checkYunAction(ctx) {
    const ret = await this._checkYunAction(ctx);
    ctx.successRes(ret);
  }

  async _checkYunAction(ctx) {
    const params = {
      kdtId: ctx.kdtId,
    };

    const ret = await new YunRemoteService(ctx).checkYunAction(params);
    return ret;
  }

  async recordYunUpgrade(ctx) {
    const { userId } = this.ctx.getLocalSession('userInfo');
    const params = {
      kdtId: ctx.kdtId,
      source: 2, // 1: app; 2: pc
      userId,
    };

    const ret = await new YunRemoteService(ctx).recordYunAction(params);
    ctx.successRes(ret);
  }

  async buyYunUpgrade(ctx) {
    const { userId } = this.ctx.getLocalSession('userInfo');

    // appId, itemId，在这个场景中写死 @慕苏 商业化平台
    const params = {
      appId: 44533, // 云服务
      itemId: 8022, // 云服务1次
      kdtId: ctx.kdtId,
      userId,
    };

    const ret = await new OrderRemoteService(ctx).autoBuyOrder(params);
    ctx.successRes(ret);
  }
}

module.exports = GuideAlertController;
