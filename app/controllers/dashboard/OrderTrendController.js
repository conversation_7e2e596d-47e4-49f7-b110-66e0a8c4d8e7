const BaseController = require('../base/BaseController');
const TeamOrderDataService = require('../../services/api/bigdata/datacenter/TeamOrderDataService');
const { fromApp } = require('../../constants');

class ShopDetectionController extends BaseController {
  constructor(ctx) {
    super(ctx);
    this.ctx.biz = '工作台-订单趋势';
  }

  async listTeamOrderTrend(ctx) {
    const { startDay, endDay } = ctx.query;
    const result = await new TeamOrderDataService(ctx).listTeamOrderTrend({
      kdtId: ctx.kdtId,
      fromApp,
      startDay: +startDay,
      endDay: +endDay,
    });
    ctx.successRes(result);
  }
}

module.exports = ShopDetectionController;
