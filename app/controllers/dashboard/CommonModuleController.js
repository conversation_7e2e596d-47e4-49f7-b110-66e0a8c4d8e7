const DashboardBaseController = require('./DashboardBaseController');
const CommonModuleService = require('../../services/api/showcase/center/CommonModuleService');
const FxCommonModuleService = require('../../services/api/showcase/center/FxCommonModuleService');
const { getRole, getMultiRoles, getMultiSamRoles } = require('./common/user-role');

const { SAM_ROLE_TYPE, fromApp } = require('../../constants');
const { PLATFORM_ENUM } = require('./constants');

class CommonModuleController extends DashboardBaseController {
  constructor(ctx) {
    super(ctx);
    this.ctx.biz = '工作台-通用模块';
  }

  getCommonModuleService(ctx, bizType) {
    const isFx = ctx.hostname.indexOf('fx.youzan.com') > -1;
    if (isFx && !bizType) {
      return new FxCommonModuleService(ctx);
    }
    return new CommonModuleService(ctx);
  }

  /**
   * 获取可选择的常用模块组件
   */
  async getAvaliableModules(ctx) {
    const { userRole, multiRoles } = await getMultiRoles(ctx);
    if (!userRole) {
      ctx.successRes();
      return;
    }
    const { roleId: samRoleId, roleType: samRoleType, biz: samBiz } = userRole;
    const multiSamRoles = getMultiSamRoles(multiRoles);
    const { title, bizType } = ctx.getRequestData();
    const commonModuleService = this.getCommonModuleService(ctx, bizType);

    const isLiteOnlineManager = await this.checkLiteOnlineManager(ctx);
    const shopBizType = await this.getShopBizType4ShowCaseCenterByBizType(ctx, bizType, {
      isLiteOnlineManager,
    });
    let storeKdtId = null;
    if (isLiteOnlineManager) {
      storeKdtId = await this.getLiteKdtId(ctx);
    }

    const result = await commonModuleService.listCommonModuleCmpsGroupByTag({
      samBiz,
      bizType,
      storeKdtId,
      samRoleId: multiSamRoles.length > 1 ? null : samRoleId,
      samRoleType: SAM_ROLE_TYPE[samRoleType] || SAM_ROLE_TYPE.DEFAULT_ROLE,
      kdtId: ctx.kdtId,
      adminId: ctx.userId,
      title,
      multiSamRoles,
      shopBizType,
    });
    ctx.successRes(result);
  }

  /**
   * 查询当前设置的常用功能
   */
  async getCustomModuleSetting(ctx) {
    const { userRole, multiRoles } = await getMultiRoles(ctx);
    const { bizType } = ctx.query;
    if (!userRole) {
      ctx.successRes();
      return;
    }
    const { roleId: samRoleId, roleType: samRoleType, biz: samBiz } = userRole;
    const multiSamRoles = getMultiSamRoles(multiRoles);
    const commonModuleService = this.getCommonModuleService(ctx, bizType);

    const isLiteOnlineManager = await this.checkLiteOnlineManager(ctx);
    const shopBizType = await this.getShopBizType4ShowCaseCenterByBizType(ctx, bizType, {
      isLiteOnlineManager,
    });
    let storeKdtId = null;
    if (isLiteOnlineManager) {
      storeKdtId = await this.getLiteKdtId(ctx);
    }

    const result = await commonModuleService.queryCommonModuleSetting({
      samBiz,
      bizType,
      samRoleId: multiSamRoles.length > 1 ? null : samRoleId,
      samRoleType: SAM_ROLE_TYPE[samRoleType] || SAM_ROLE_TYPE.DEFAULT_ROLE,
      kdtId: ctx.kdtId,
      adminId: ctx.userId,
      storeKdtId,
      platform: PLATFORM_ENUM.PC,
      multiSamRoles,
      shopBizType,
    });
    ctx.json(0, '', result);
  }

  /**
   * 保存当前设置的常用功能
   */
  async saveCustomModuleSetting(ctx) {
    const { cmpList, showType, bizType } = ctx.getPostData();
    const { userRole, multiRoles } = await getMultiRoles(ctx);
    if (!userRole) {
      ctx.successRes();
      return;
    }
    const { roleId: samRoleId, roleType: samRoleType, biz: samBiz } = userRole;
    const multiSamRoles = getMultiSamRoles(multiRoles);
    const commonModuleService = this.getCommonModuleService(ctx, bizType);

    const isLiteOnlineManager = await this.checkLiteOnlineManager(ctx);
    const shopBizType = await this.getShopBizType4ShowCaseCenterByBizType(ctx, bizType, {
      isLiteOnlineManager,
    });
    let storeKdtId = null;
    if (isLiteOnlineManager) {
      storeKdtId = await this.getLiteKdtId(ctx);
    }

    const result = await commonModuleService.saveCustomCommonModuleSetting({
      samBiz,
      bizType,
      samRoleId,
      samRoleType: SAM_ROLE_TYPE[samRoleType] || SAM_ROLE_TYPE.DEFAULT_ROLE,
      adminId: ctx.userId,
      fromApp,
      kdtId: ctx.kdtId,
      operatorType: 1,
      storeKdtId,
      operatorId: ctx.userId,
      platform: PLATFORM_ENUM.PC,
      cmpList,
      showType,
      shopBizType,
      multiSamRoles,
    });
    ctx.successRes(result);
  }

  // 精简版shopBizType使用老版本
  async getLiteWscCommonModuleSetting(ctx) {
    const userRole = await getRole(ctx);
    if (!userRole) {
      ctx.successRes();
      return;
    }
    const { roleId: samRoleId, roleType: samRoleType, biz: samBiz } = userRole;
    const shopBizType = await this.getBizType4ShowCaseCenterV1(ctx);
    const result = await new CommonModuleService(ctx).queryCommonModuleDefaultForBriefWsc({
      samBiz,
      samRoleId,
      samRoleType: SAM_ROLE_TYPE[samRoleType] || SAM_ROLE_TYPE.DEFAULT_ROLE,
      kdtId: ctx.kdtId,
      adminId: ctx.userId,
      platform: PLATFORM_ENUM.PC,
      shopBizType,
    });
    ctx.json(0, '', result);
  }
}

module.exports = CommonModuleController;
