const BaseController = require('../base/BaseController');
const { checkPureWscSingleStore } = require('@youzan/utils-shop');

class IndexController extends BaseController {
  constructor(ctx) {
    super(ctx);
    this.ctx.biz = '店铺工具-店铺检测';
  }

  init() {
    super.init();
  }

  async getIndexHtml(ctx) {
    // 验证是否要使用新版工作台
    if (await this.checkGray(ctx)) {
      return ctx.redirect('/v4/shop/assistant');
    }
    await ctx.render('shop-detection/index.html');
  }

  /**
   * 新版工作台验证灰度名单
   */
  async checkGray(ctx) {
    const GRAY_KEY = 'wsc_dashborad_gray';
    const shopInfo = ctx.getState('shopInfo');
    const isFx = ctx.hostname.indexOf('fx.youzan.com') === 0;
    const isDsp = +ctx.getState('dspConfig') === 1;

    if (checkPureWscSingleStore(shopInfo)) {
      return !isDsp && !isFx && this.grayRelease(GRAY_KEY, ctx.kdtId);
    }

    return false;
  }
}

module.exports = IndexController;
