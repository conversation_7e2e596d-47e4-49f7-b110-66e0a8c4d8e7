const { checkBranchStore } = require('@youzan/utils-shop');
const BaseController = require('../base/BaseController');
const XhsAppAccountService = require('../../services/api/channels/XhsAppAccountService');
const ContentNoteApiService = require('../../services/api/channels/ContentNoteApiService');
const SharePhotoActivityService = require('../../services/api/channels/SharePhotoActivityService');
const ContentActivityDataService = require('../../services/api/channels/ContentActivityDataService');
const ContentGenerateService = require('../../services/api/channels/ContentGenerateService');
const ContentActivityNoteService = require('../../services/api/channels/ContentActivityNoteService');
const SharePhotoActivityMomentService = require('../../services/api/channels/SharePhotoActivityMomentService');
const ContentThirdNoteService = require('../../services/api/channels/ContentThirdNoteService');
const AgentBuildService = require('../../services/api/channels/AgentBuildService');
const MpVersionService = require('../../services/api/channels/MpVersionService');

class XhsUGCController extends BaseController {
  constructor(ctx) {
    super(ctx);
    this.ctx.biz = '小红书晒图打卡';
  }

  get kdtId() {
    return this.ctx.kdtId;
  }

  get operatorId() {
    return `${this.ctx.userId}`;
  }

  get rootKdtId() {
    const shopMetaInfo = this.ctx.getState('shopMetaInfo') || {};
    const { rootKdtId, kdtId } = shopMetaInfo;
    return rootKdtId || kdtId;
  }

  withKdtId(params = {}) {
    const { kdtId } = this;
    return { ...params, kdtId };
  }

  withRookdtid(params = {}) {
    const { rootKdtId } = this;
    return { ...params, rootKdtId };
  }

  withOperatorId(params = {}) {
    const { operatorId } = this;
    return { ...params, operatorId };
  }

  translatePageInfo(params = {}) {
    const { page, pageSize } = params;
    return {
      ...params,
      page: Number(page) || 1,
      pageSize: Number(pageSize) || 20,
    };
  }

  translateActivityId(params = {}) {
    const { activityId } = params;
    if (activityId) {
      return { ...params, activityId: Number(activityId) };
    }
    return params;
  }

  useResponse(ctx, params, response) {
    return ctx.json(0, 'ok', { response });
  }

  /** 数据白名单 */
  async isDataSwitch(ctx) {
    const response = await new ContentThirdNoteService(ctx)
      .isDataSwitch(this.kdtId)
      .catch(err => err);
    return this.useResponse(ctx, { kdtId: this.kdtId }, response);
  }

  /** get 查询店铺类目 */
  async getShopIndustryType(ctx) {
    const response = await new XhsAppAccountService(ctx)
      .getShopIndustryType(this.kdtId)
      .catch(err => err);

    const templateConfig = ctx.apolloClient.getConfig({
      appId: 'wsc-pc-v4',
      namespace: 'wsc-pc-v4.micro-app',
      key: 'table-card-plan',
    });
    try {
      response.tableCardConfig = templateConfig[response.code] || {};
      // eslint-disable-next-line no-empty
    } catch (error) {}

    return this.useResponse(ctx, { kdtId: this.kdtId }, response);
  }

  /** post 添加笔记范例 */
  async add(ctx) {
    const params = this.withOperatorId(ctx.request.body);
    const response = await new ContentNoteApiService(ctx).add(params).catch(err => err);
    return this.useResponse(ctx, params, response);
  }

  /** get 查询笔记范例 */
  async getExampleNote(ctx) {
    const params = this.withKdtId();
    const response = await new SharePhotoActivityService(ctx)
      .getExampleNote(params)
      .catch(err => err);
    return this.useResponse(ctx, params, response);
  }

  /** post 初始化活动 */
  async initActivity(ctx) {
    let params = this.withOperatorId(ctx.request.body);
    params = this.withRookdtid(params);
    params = this.withKdtId(params);
    const response = await new SharePhotoActivityService(ctx)
      .initActivity(params)
      .catch(err => err);
    return this.useResponse(this.ctx, params, response);
  }

  /** post 初始化活动 */
  async createActivityTest(ctx) {
    let params = this.withOperatorId(ctx.request.body);
    params = this.withRookdtid(params);
    params = this.withKdtId(params);
    const response = await new SharePhotoActivityService(ctx)
      .createActivityTest(params)
      .catch(err => err);
    return this.useResponse(this.ctx, params, response);
  }

  /** post 开启活动 */
  async start(ctx) {
    const params = this.withKdtId(ctx.request.body);
    const response = await new SharePhotoActivityService(ctx).start(params).catch(err => err);
    return this.useResponse(ctx, params, response);
  }

  /** post 更新活动 */
  async update(ctx) {
    const params = this.withKdtId(ctx.request.body);
    const response = await new SharePhotoActivityService(ctx).update(params).catch(err => err);
    return this.useResponse(ctx, params, response);
  }

  /** get 活动列表 */
  async listOfPage(ctx) {
    let params = this.translatePageInfo(ctx.query);
    params = this.withKdtId(params);
    params = this.translateActivityId(params);
    const response = await new SharePhotoActivityService(ctx).listOfPage(params).catch(err => err);
    return this.useResponse(ctx, params, response);
  }

  /** get 活动详情 */
  async getById(ctx) {
    let params = this.withKdtId(ctx.query);
    params = this.translateActivityId(params);
    const response = await new SharePhotoActivityService(ctx).getById(params).catch(err => err);
    return this.useResponse(ctx, params, response);
  }

  /** get 所有活动数据概览 */
  async overviewOfAllActivity(ctx) {
    let params = this.withKdtId(ctx.query);
    params = this.translateActivityId(params);
    const response = await new ContentActivityDataService(ctx)
      .overviewOfAllActivity(params)
      .catch(err => err);
    return this.useResponse(ctx, params, response);
  }

  /** get 活动数据概览 */
  async overviewOfActivity(ctx) {
    let params = this.withKdtId(ctx.query);
    params = this.translateActivityId(params);
    const response = await new ContentActivityDataService(ctx)
      .overviewOfActivity(params)
      .catch(err => err);
    return this.useResponse(ctx, params, response);
  }

  /** post 发布回调 */
  async publishCallback(ctx) {
    let params = this.withKdtId(ctx.request.body);
    params = this.withOperatorId(params);
    const response = await new ContentGenerateService(ctx)
      .publishCallback(params)
      .catch(err => err);
    return this.useResponse(ctx, params, response);
  }

  /** get 作品分页列表 */
  async listOfPageNote(ctx) {
    let params = this.translatePageInfo(ctx.query);
    params = this.withKdtId(params);
    params = this.translateActivityId(params);
    const response = await new ContentActivityNoteService(ctx).listOfPage(params).catch(err => err);
    return this.useResponse(ctx, params, response);
  }

  /** get 活动动态列表 */
  async listOfPageMoment(ctx) {
    let params = this.translatePageInfo(ctx.query);
    params = this.withKdtId(params);
    params = this.translateActivityId(params);
    const response = await new SharePhotoActivityMomentService(ctx)
      .listOfPage(params)
      .catch(err => err);
    return this.useResponse(ctx, params, response);
  }

  /** 异步生成 */
  async asyncGenerate(ctx) {
    const params = this.withOperatorId(ctx.request.body);
    params.params = this.withKdtId(params.params);
    const response = await new ContentGenerateService(ctx).asyncGenerate(params).catch(err => err);
    return this.useResponse(ctx, params, response);
  }

  /** 获取生成结果 */
  async getGenerateResult(ctx) {
    const params = this.withKdtId(ctx.query);
    const response = await new ContentGenerateService(ctx)
      .getGenerateResult(params)
      .catch(err => err);
    return this.useResponse(ctx, params, response);
  }

  async getLatestVersion(ctx) {
    const { kdtId } = this;
    const xhsParams = { businessType: 1, accountType: 16 };
    const mpVersionService = new MpVersionService(ctx);
    let response = await mpVersionService.getMpVersion({ kdtId, ...xhsParams });
    let isUseRootMiniApp = false;

    if (!response) {
      const shopInfo = ctx.getState('shopInfo');
      if (checkBranchStore(shopInfo)) {
        response = await mpVersionService.getMpVersion({
          kdtId: shopInfo.rootKdtId,
          ...xhsParams,
        });
        isUseRootMiniApp = true;
      }
    }

    const { releaseVersion } = response || {};

    return this.useResponse(ctx, {}, { releaseVersion, isUseRootMiniApp });
  }

  /** 智能助手是否已初始化 */
  async isFinished(ctx) {
    const { kdtId } = this;
    const operator = this.getOperatorParams().operator;
    const { userId, nickName } = operator;
    const params = {
      kdtId,
      operator: {
        operatorId: `${userId}`,
        operatorName: nickName,
      },
    };

    const response = await new AgentBuildService(ctx).isFinished(params).catch(err => err);
    return this.useResponse(ctx, params, response);
  }
}

module.exports = XhsUGCController;
