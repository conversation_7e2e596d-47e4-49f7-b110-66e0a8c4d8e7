const BaseController = require('../base/BaseController');
const UploadService = require('../../services/api/ctu-open/UploadService');
const ComplaintService = require('../../services/api/ctu-open/ComplaintService');
const AppealService = require('../../services/api/ctu-open/AppealService');
const StaffQueryOuterService = require('../../services/api/staff/StaffQueryOuterService');
class IprController extends BaseController {
  constructor(ctx) {
    super(ctx);
    this.ctx.biz = '知识产权';
  }

  init() {
    super.init();
    super.initUserInfo();
  }

  async getIndexHtml(ctx) {
    await ctx.render('ipr/index.html');
  }

  async getPublicToken(ctx) {
    const { userId } = ctx;
    const { fileName } = ctx.getPostData();
    const data = await new UploadService(ctx).getPublicToken(fileName, userId);

    ctx.successRes(data);
  }

  async isOnComplaintBlackList(ctx) {
    const { buyUserId } = ctx.query;
    const data = await new ComplaintService(ctx).isOnComplaintBlackList(buyUserId);
    return ctx.json(0, 'ok', data);
  }

  async getComplainRecord(ctx) {
    const { userId } = ctx;
    const data = await new ComplaintService(ctx).logs({
      ...ctx.query,
      userId,
    });

    return ctx.json(0, 'ok', data);
  }

  async getComplainHistory(ctx) {
    const { userId } = ctx;
    const { type, page, pageSize } = ctx.query;
    const data = await new ComplaintService(ctx).complaintedMaterials(userId, type, page, pageSize);

    return ctx.json(0, 'ok', data);
  }

  async getComplainDetail(ctx) {
    const { id } = ctx.query;
    const { userId } = ctx;
    const data = await new ComplaintService(ctx).detail(userId, id);

    return ctx.json(0, 'ok', data);
  }

  async getComplainProgress(ctx) {
    const { id } = ctx.query;
    const { userId } = ctx;
    const data = await new ComplaintService(ctx).progress(userId, id);

    return ctx.json(0, 'ok', data);
  }

  async submitComplain(ctx) {
    const { userId } = ctx;
    const params = ctx.getPostData();
    const data = await new ComplaintService(ctx).submit({
      ...params,
      userId,
    });

    return ctx.json(0, 'ok', data);
  }

  async cancelComplain(ctx) {
    const { id } = ctx.query;
    const { userId } = ctx;
    const data = await new ComplaintService(ctx).cancel(userId, id);

    return ctx.json(0, 'ok', data);
  }

  async validateLink(ctx) {
    const { url } = ctx.query;
    const data = await new ComplaintService(ctx).validLink(url);

    return ctx.json(0, 'ok', data);
  }

  async getAppealRecord(ctx) {
    const { userId, kdtId } = ctx;

    // 查询店铺是否存在该员工,如果没有则报错，解决水平越权问题
    await new StaffQueryOuterService(ctx).queryStaffBaseInfoByKdtId({
      adminId: userId,
      kdtId: ctx.originQuery.kdtId ? ctx.originQuery.kdtId : kdtId,
    });

    const data = await new AppealService(ctx).logs({
      // ctx.query 的 kdtId 优先于 ctx 的 kdtId
      kdtId,
      ...ctx.originQuery,
      userId,
    });

    return ctx.json(0, 'ok', data);
  }

  async getAppealHistory(ctx) {
    const { userId } = ctx;
    const { type, page, pageSize } = ctx.query;
    const data = await new AppealService(ctx).appealedMaterials(userId, type, page, pageSize);

    return ctx.json(0, 'ok', data);
  }

  async getAppealDetail(ctx) {
    const { id } = ctx.query;
    const { kdtId } = ctx;
    const data = await new AppealService(ctx).detail(kdtId, id);

    return ctx.json(0, 'ok', data);
  }

  async getAppealProgress(ctx) {
    const { id } = ctx.query;
    const { kdtId } = ctx;
    const data = await new AppealService(ctx).progress(kdtId, id);

    return ctx.json(0, 'ok', data);
  }

  async submitAppeal(ctx) {
    const { userId, kdtId } = ctx;
    const { nickName } = ctx.getLocalSession('userInfo');
    const params = ctx.getPostData();
    const data = await new AppealService(ctx).submit({
      ...params,
      kdtId,
      userId,
      userName: nickName,
    });

    return ctx.json(0, 'ok', data);
  }

  async checkAppeal(ctx) {
    const { id } = ctx.query;
    const data = await new AppealService(ctx).canAppeal(id);

    return ctx.json(0, 'ok', data);
  }

  async getShopList(ctx) {
    const { userId } = ctx;
    const { page, pageSize } = ctx.query;
    const data = await new AppealService(ctx).getUserShops(userId, page, pageSize);

    return ctx.json(0, 'ok', data);
  }
}

module.exports = IprController;
