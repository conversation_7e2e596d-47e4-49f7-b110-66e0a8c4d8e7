const BaseController = require('../base/BaseController');
const MicroMallAppealService = require('../../services/api/ctu-open/MicroMallAppealService');
const UploadService = require('../../services/api/ctu-open/UploadService');

class IndexController extends BaseController {
  constructor(ctx) {
    super(ctx);
    this.ctx.biz = '申诉';
  }

  init() {
    super.init();
    super.initUserInfo();
  }

  addInfo(info, ctx) {
    const { kdtId } = ctx;
    const { userId } = ctx.getLocalSession('userInfo');
    return {
      ...info,
      kdtId,
      userId,
    };
  }

  async getIndexHtml(ctx) {
    await ctx.render('appeal.html');
  }

  async getInfo(ctx) {
    const { appealCategory, lockTransId, time } = ctx.query;

    const data = await new MicroMallAppealService(ctx).merchantAppeal(
      this.addInfo(
        {
          appealCategory,
          lockTransId,
          time,
        },
        ctx
      )
    );
    ctx.successRes(data);
  }

  async save(ctx) {
    const params = ctx.getPostData();
    const data = await new MicroMallAppealService(ctx).save(this.addInfo(params, ctx));
    ctx.successRes(data);
  }

  async commit(ctx) {
    const { appealCategory, appealId } = ctx.getPostData();
    const params = this.addInfo(
      {
        appealCategory,
        appealId,
      },
      ctx
    );
    const data = await new MicroMallAppealService(ctx).commit(params);
    ctx.successRes(data);
  }

  async getToken(ctx) {
    const { userId } = ctx;
    const { fileName } = ctx.getPostData();
    const data = await new UploadService(ctx).getToken(fileName, userId);
    ctx.successRes(data);
  }
}

module.exports = IndexController;
