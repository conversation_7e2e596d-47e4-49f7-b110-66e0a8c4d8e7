const get = require('lodash/get');
const isPlainObject = require('lodash/isPlainObject');
const { PCBaseController } = require('@youzan/wsc-pc-base');
const {
  checkRetailSingleStore,
  checkRetailChainStore,
  checkRetailMinimalistShop,
} = require('@youzan/utils-shop');
const AuthTokenService = require('../../services/api/authority/AuthTokenService');
const SmsCaptchaService = require('../../services/api/authority/SmsCaptchaService');
const ShopBaseReadService = require('../../services/api/shopcenter/shop/ShopBaseReadService');
const ProdReadService = require('../../services/api/shopcenter/shopprod/ProdReadService');
const StaffServiceV2 = require('../../services/api/sam/StaffServiceV2');
const RoleService = require('../../services/api/rig/RoleService');
const ShopFrontService = require('../../services/api/salesman/ShopFrontService');
const { fromApp } = require('../../constants');

/**
 * Project Base Controller
 */
class BaseController extends PCBaseController {
  APP_NAME = 'wsc-pc-v4';

  async init() {
    await super.init();
  }

  /**
   * 获取通用的操作人参数
   */
  getOperatorParams() {
    const { kdtId, firstXff } = this.ctx;
    const { id: userId, nickName } = this.ctx.getLocalSession('userInfo');

    return {
      fromApp: this.APP_NAME,
      kdtId,
      operator: {
        nickName,
        userId,
        clientIp: firstXff,
        source: this.APP_NAME,
        fromApp: this.APP_NAME,
      },
    };
  }

  /**
   * 获取连锁总部 kdtId
   */
  getHqKdtId() {
    const shopInfo = this.ctx.getState('shopInfo') || {};
    const { rootKdtId } = shopInfo;
    return rootKdtId;
  }

  // 白名单服务
  async grayRelease(key, kdtId) {
    return this.callService(
      'wsc-pc-base/common.GrayReleaseService',
      'isInGrayReleaseByKdtId',
      key,
      kdtId
    );
  }

  grayReleaseByKdtId(ctx, { namespace, key } = { namespace: 'gray' }, kdtId) {
    const apolloConfig = ctx.apolloClient.getConfig({
      appId: 'wsc-pc-v4',
      namespace,
      key,
    });
    return this.matchGrayConfig(apolloConfig, kdtId);
  }

  checkGrayFlowConfig(config, kdtId) {
    const { percent = 0, whitelist = [], blacklist = [] } = config || {};
    // 首先判断是否命中黑名单
    if (blacklist && blacklist.length > 0 && blacklist.indexOf(kdtId) > -1) {
      return false;
    }
    // 判断是否命中百分比规则
    if (typeof percent === 'number') {
      if (percent >= 100) {
        return true;
      }
    }
    // 判断是否命中白名单
    if (whitelist && whitelist.length > 0 && whitelist.indexOf(kdtId) > -1) {
      return true;
    }
    return false;
  }

  matchGrayConfig(config, kdtId) {
    config = String(config || '');
    const kdtIdList = config.split(',');
    // 先判断0或者黑名单的情况
    if (kdtIdList.includes('0') || kdtIdList.includes('0%')) {
      return false;
    } else if (kdtIdList.includes('-' + kdtId)) {
      return false;
    } else if (kdtIdList.includes(String(kdtId))) {
      // kdtId全匹配
      return true;
    } else if (config.indexOf('%') > 0) {
      // 百分比判断
      const percentArr = kdtIdList
        .filter((singleConfig) => {
          return singleConfig.endsWith('%');
        })
        .map((singleConfig) => {
          return singleConfig.slice(0, singleConfig.length - 1);
        });
      if (percentArr && percentArr.length) {
        // 只取第一个百分比配置
        const onlyPercent = Number(percentArr[0]);
        return !!(onlyPercent >= 0 && onlyPercent <= 100 && Number(kdtId) % 100 <= onlyPercent);
      } else {
        return false;
      }
    } else {
      return false;
    }
  }

  /**
   * @description: 单纯的扫码核身校验
   * @param {string} token 扫码返回的 token
   * @param {string} bizType 行为验证码业务线
   */
  async tokenCheck(token, bizType) {
    const { ctx } = this;

    // 1、获取 token 信息
    const { authTokenInfo } = await new AuthTokenService(ctx).getAuthTokenInfo({
      tokenAuthUserInfoDto: {
        sessionId: ctx.sid,
        sessionBizType: 'sso_loginSign',
      },
      authTokenInfo: {
        token,
      },
      scene: {
        ipAddress: ctx.firstXff,
        userAgent: ctx.userAgent,
      },
    });

    // 2、验证 bizType 是否是自己的业务 bizType，防止恶意用户用别的业务生成 token 有效 token 来这个接口校验
    if (authTokenInfo.bizType !== bizType) {
      return false;
    }

    // 3、验证 token 是否有校 （后端会校验 token 是否有校，且 token 中的 kdtId 是否是创建时的 kdtId, 创建时的 kdtId 是从 session 中取的）
    const res = await new AuthTokenService(ctx).tokenCheck({
      authTokenInfo: { token, bizType },
      tokenAuthUserInfoDto: {
        sessionId: ctx.sid,
      },
      scene: {
        userAgent: ctx.userAgent,
        ipAddress: ctx.firstXff,
      },
    });

    return res && res.checkResult;
  }

  /**
   * @description: 通过 kdtId 拿到手机号进行验证码校验
   * @param {number} kdtId
   * @param {number} smsCaptcha 验证码
   * @param {string} biz 短信验证码业务线
   */
  async smsCaptchaCheck(ctx, kdtId, smsCaptcha, biz) {
    const baseInfoRst = await new ShopBaseReadService(ctx).getShopBaseInfoByKdtId(kdtId);
    const { baseResult, shopBaseInfo } = baseInfoRst || {};
    if (!baseResult.isSuccess || !shopBaseInfo) {
      ctx.fail(0, '获取验证信息失败！');
      return false;
    }
    const { contactCountryCode, contactMobile } = shopBaseInfo;
    const mobile = `${contactCountryCode}-${contactMobile}`;
    const smsCheckResult = await new SmsCaptchaService(ctx).validSmsCaptcha({
      biz,
      mobile,
      smsCaptcha,
    });
    if (!smsCheckResult.success) {
      ctx.fail(10200, '短信验证码错误！');
      return false;
    }
    return true;
  }

  /**
   * 获取员工的角色信息
   */
  async getStaffRoles(ctx) {
    const biz = ctx.isSuperStore ? 'retail' : 'wsc';
    const { kdtId, userId } = ctx;
    const staffInfo = await new StaffServiceV2(ctx).getStaff({
      kdtId,
      adminId: userId,
      biz,
    });
    const totalRoleList = await new RoleService(ctx).getRoleList({
      thirdTenantId: `${kdtId}`,
      thirdUserId: `${userId}`,
      namespace: 'np_yz_shop',
      rigSource: 'wsc-pc-v4',
      filter: {
        bizKeyGroup: 'shop_ability',
      },
    });
    if (!isPlainObject(staffInfo) || !Array.isArray(totalRoleList)) {
      return [];
    }
    /**
     * 给当前员工的角色信息增加 extProperties 信息
     */
    const roleId2extProps = totalRoleList.reduce((acc, cur) => {
      acc[cur.roleId] = cur.extProperties || null;
      return acc;
    }, {});
    return get(staffInfo, 'roleList', []).map((role) => ({
      ...role,
      extProperties: roleId2extProps[role.roleId],
    }));
  }

  /**
   * @description: 获取店铺版本
   * @param {number} kdtId
   */
  async getShopProdVersion(ctx) {
    const { kdtId } = ctx;
    const shopProdVersions = await new ProdReadService(ctx).queryShopProdVersions(kdtId);
    // 若返回多个版本，判断当前店铺版本
    shopProdVersions.sort((pre, after) => after.beginTime - pre.beginTime);
    const shopProdVersion =
      shopProdVersions.length < 2
        ? shopProdVersions[0]
        : shopProdVersions.filter((version) => version.lifecycleStatus !== 'prepare')[0];
    return shopProdVersion;
  }

  // 获取导购助手插件能力
  async getGuideShopAbility() {
    const { ctx } = this;
    const { userId, kdtId } = ctx;
    const params = {
      adminId: userId,
      kdtId,
      needStaffCount: false,
      retailSource: fromApp,
      needYopExpiredTime: false,
      supportChainShop: true,
    };
    const shopAbility = await new ShopFrontService(ctx).getShoppingGuideAbility(params);
    return shopAbility;
  }

  /**
   * 用来判断是否为零售店铺（零售单店、连锁 L 版，零售高级版）
   */
  checkIsRetailType(ctx) {
    const shopInfo = ctx.getState('shopInfo');

    return (
      checkRetailSingleStore(shopInfo) ||
      (checkRetailChainStore(shopInfo) && !checkRetailMinimalistShop(shopInfo))
    );
  }
}

module.exports = BaseController;
