const args = require('@youzan/utils/url/args');
const {
  checkSingleStore,
  checkHqStore,
  checkRetailShop,
  checkChainStore,
} = require('@youzan/utils-shop');
const PrePaidAgentQueryService = require('../../services/api/card-voucher/PrePaidAgentQueryService');
const PrePaidAgentOperationService = require('../../services/api/card-voucher/PrePaidAgentOperationService');
const ShortUrlService = require('@youzan/wsc-pc-base/app/services/common/ShortUrlService');
const HomepageService = require('../../services/api/shop/HomepageService');
const BaseController = require('../base/BaseController');
const WeappQrCodeService = require('../../services/api/channels/WeappQrCodeService');
const SnapshotService = require('../../services/api/poster/SnapshotService');
const chalk = require('chalk');
const ShowcasePrepaidAgentService = require('../../services/api/showcase/front/PrepaidAgentService');
const CustomerCrowdManageService = require('../../services/api/scrm/CustomerCrowdManageService');
const ContentAsyncService = require('../../services/api/aigc/ContentAsyncService');
const AsyncResponseService = require('../../services/api/aigc/AsyncResponseService');
const MarketingAgentService = require('../../services/api/scrm/api/MarketingAgentService');
const QiniuImageWriteService = require('../../services/api/material/materialcenter/QiniuImageWriteService');
const BusinessException = require('@youzan/wsc-pc-base/app/exceptions/BusinessException');
const urlib = require('url');
const CategoryReadService = require('../../services/api/material/materialcenter/CategoryReadService');
const constants = require('../../constants');
const ItemGroupQueryService = require('../../services/api/ic/ItemGroupQueryService');
const ItemQueryService = require('../../services/api/ic/ItemQueryService');
const RetailLiteItemQueryService = require('../../services/api/mall/RetailLiteItemQueryService');
const SmsSignatureService = require('../../services/api/scrm/SmsSignatureService');

const Channel = 'youzan_www';

const { NODE_ENV } = process.env;
// const delay = time => {
//   return new Promise(resolve => {
//     setTimeout(() => {
//       resolve();
//     }, time);
//   });
// };

class IndexController extends BaseController {
  constructor(ctx) {
    super(ctx);
    if (NODE_ENV === 'qa') {
      console.log(chalk.blueBright(`接口:[${ctx.path}]:${ctx.traceCtx && ctx.traceCtx.rootId}`));
    }
  }

  async checkIsSupportSalesChannel() {
    /**
     * 抄的wsc-pc-ump
     * 店铺是否支持多渠道 - 在多渠道白名单内的连锁L
     * PS: 在白名单内的一定都是新商品模型
     */
    return this.ctx.matrix.canIUse('ump.sales-channel.base');
  }

  // 获取各种初始化数据
  async agentDetailInit() {
    const { ctx } = this;

    const [canUseAgent, shopType] = await Promise.all([
      this.checkCanUseAgent(ctx),
      this.checkShopType(ctx),
    ]);

    const result = {
      canUseAgent,
      shopType,
    };

    ctx.success(result);
  }

  // 查询店铺签名
  async getCurShopSign() {
    const { ctx } = this;
    const signInfo = await new SmsSignatureService(ctx).getLatestSmsSignature({
      kdtId: ctx.kdtId,
    });

    // 检查signInfo是否存在且有name属性
    if (signInfo && signInfo.name) {
      return signInfo.name;
    }

    // 如果没有获取到签名，尝试获取默认签名
    const defaultSignResult = await new SmsSignatureService(ctx).getDefaultSmsSignature({
      kdtId: ctx.kdtId,
    });

    // 确保返回字符串类型
    return defaultSignResult || '有赞';
  }

  async agentFlowInit() {
    const { ctx } = this;
    const { kdtId } = ctx;
    const shopInfo = ctx.getState('shopInfo') || {};

    const [
      currentShopSign,
      canUseAgent,
      homeDetailTile,
      agentCot,
      isSupportSalesChannel,
      shortUrl,
      noticeChannelCheck,
      prepaidPromotionQrCode,
    ] = await Promise.all([
      this.getCurShopSign(),
      this.checkCanUseAgent(ctx),
      this.getHomeDetail(kdtId),
      this.getCardValueAgentCot(ctx),

      this.checkIsSupportSalesChannel(),
      this.getShortUrl(ctx),
      new MarketingAgentService(ctx).checkMarketingPlanNoticeChannel({
        kdtId,
      }),
      this.getPrepaidPromotionQrCode(),
    ]);

    const result = {
      currentShopSign,
      canUseAgent,
      homeDetailTile,
      agentCot,
      isSupportSalesChannel,
      shortUrl,
      noticeChannelCheck,
      shopName: shopInfo.shopName,
      kdtId,
      prepaidPromotionQrCode,
      shopInfo: ctx.getState('shopInfo'),
    };

    ctx.success(result);
  }

  async getShortUrl() {
    const { ctx } = this;
    const shopInfo = ctx.getState('shopInfo');
    const kdtId = shopInfo.rootKdtId || shopInfo.kdtId;
    const qrCode = {
      url: `https://cashier.youzan.com/pay/new_prepaid_balance_recharge?kdt_id=${kdtId}&type=BALANCE&entry=4`,
    };
    return new ShortUrlService(ctx)
      .shorten(qrCode.url)
      .then(res => {
        return res;
      })
      .catch(err => {
        this.ctx.logger.error('获取短链接失败', err);
        return '';
      });
  }

  async getCardValueAgentCot(ctx) {
    try {
      const doneTopic = ['已分析你的店铺情况', '策划一次储值活动'];
      const { kdtId } = ctx;
      const result = await Promise.all([
        new PrePaidAgentQueryService(ctx).getCardValueAgentCot({
          kdtId,
        }),
      ]);
      return result.map((item, index) => {
        return {
          topic: item.cotTitle,
          doneTopic: doneTopic[index],
          content: item.cotMessage,
        };
      });
    } catch (error) {
      this.ctx.logger.error('获取getCardValueAgentCot失败', error);
      return [];
    }
  }

  async getHomeDetail(kdtId) {
    try {
      const homeResult = await new HomepageService(this.ctx).getDetailByKdtIdForWeapp(kdtId);
      return homeResult.title;
    } catch (error) {
      this.ctx.logger.error('获取首页详情失败', error);
      return '店铺主页';
    }
  }

  checkShopType(ctx) {
    const shopInfo = ctx.getState('shopInfo');
    return {
      isChianStore: checkChainStore(shopInfo),
      isHqStore: checkHqStore(shopInfo),
      isRetailShop: checkRetailShop(shopInfo),
      isSingleStore: checkSingleStore(shopInfo),
    };
  }
  //  检查是否可以使用功能
  async checkCanUseAgent(ctx) {
    const shopInfo = ctx.getState('shopInfo');

    // 零售单店或者零售总部店铺
    if (!checkRetailShop(shopInfo) || !(checkSingleStore(shopInfo) || checkHqStore(shopInfo))) {
      return {
        canShow: false,
        message: '该店铺暂不支持储值智能体功能',
      };
    }
    // 打开智能体前的检查
    return new PrePaidAgentQueryService(ctx).checkBeforeOpenAgent({
      kdtId: ctx.kdtId,
    });
  }

  /**
   * 查询储值智能体运行转态
   * @returns
   */
  async getPrepaidStatus() {
    const { ctx } = this;
    const params = {
      pageSize: 5,
      page: 1,
      status: [10, 20, 30],
      kdtId: ctx.kdtId,
    };
    const result = await new PrePaidAgentQueryService(ctx).queryCardValueAgentList(params);
    return ctx.success({
      status: result.items?.length > 0 ? 1 : 0,
    });
  }

  /**
   * 查询储值智能体列表
   * @returns
   */
  async getPrepaidList() {
    const { ctx } = this;
    const { pageSize, page } = ctx.getQueryData();
    const params = {
      pageSize,
      page,
      kdtId: ctx.kdtId,
    };
    const result = await new PrePaidAgentQueryService(ctx).queryCardValueAgentList(params);
    return ctx.success(result);
  }

  /**
   * 提示词
   * @returns
   */
  async queryCardValueAgentActivity() {
    const { ctx } = this;
    const result = await new PrePaidAgentQueryService(ctx).queryCardValueAgentActivity({
      kdtId: ctx.kdtId,
    });
    // await delay(15000);
    return ctx.success(result);
  }

  /**
   * 储值智能体的操作（启用、暂停、失效）
   * @returns
   */
  async changeStatus() {
    const { ctx } = this;
    const { activityNo, command } = ctx.getPostData();
    const params = {
      activityNo,
      command,
      kdtId: ctx.kdtId,
    };
    const result = await new PrePaidAgentOperationService(ctx).operateAgent(params);
    return ctx.success(result);
  }

  /**
   * 检查智能体活动的时间范围是否合法
   * @returns
   */
  async checkCreateAgentTimeRange() {
    const { ctx } = this;
    const { startTime, endTime } = ctx.getQueryData();
    const params = {
      startTime,
      endTime,
      kdtId: ctx.kdtId,
    };
    const result = await new PrePaidAgentOperationService(ctx).checkCreateAgentTimeRange(params);
    return ctx.success(result);
  }

  /** 储值前置校验 */
  async checkBeforeCreateAgent() {
    const { ctx } = this;
    const { startTime, endTime } = ctx.getQueryData();
    const [checkRule, checkTime] = await Promise.all([
      new PrePaidAgentQueryService(ctx).checkBeforeCreateAgent({ kdtId: ctx.kdtId }),
      new PrePaidAgentOperationService(ctx).checkCreateAgentTimeRange({
        startTime,
        endTime,
        kdtId: ctx.kdtId,
      }),
    ]);
    return ctx.success({
      checkRule,
      checkTime,
    });
  }

  /**
   * 创建储值智能体
   */
  async createAgent() {
    const { ctx } = this;
    const params = {
      ...ctx.getPostData(),
      kdtId: ctx.kdtId,
      operatorId: ctx.userId,
    };
    const result = await new PrePaidAgentOperationService(ctx).createAgent(params);
    return ctx.success(result);
  }

  /**
   * 查询创建储智能体的结果
   */
  async queryCardValueAgentResult() {
    const { ctx } = this;
    const { activityNo, step } = ctx.getQueryData();
    const params = {
      activityNo,
      kdtId: ctx.kdtId,
      noteType: step,
    };

    const result = await new PrePaidAgentQueryService(ctx).queryCardValueAgentResult(params);
    return ctx.success(result);
  }

  /**
   * 物料的压缩包下载
   */
  async queryImageUrl() {
    const { ctx } = this;
    const params = ctx.getQueryData();
    const result = await new PrePaidAgentQueryService(ctx).queryImageUrl(params);
    return ctx.success(result);
  }

  async getQrCode({ hyaLine = 1, width = 400, pagepath = '', kdtId }) {
    const params = {
      hyaLine: !!+hyaLine,
      kdtId,
      path: pagepath,
      width,
    };

    let result = null;
    try {
      result = await new WeappQrCodeService(this.ctx).wxaGetCodeV2(params);
      return {
        type: 'weapp',
        qrcode: result,
      };
    } catch (error) {
      this.ctx.logger.info(`🚀 ~ 获取小程序二维码出错：${params}  ${error}`);
      return {
        type: 'h5',
        qrcode: '',
      };
    }
  }

  /**
   * 获取储值推广二维码
   */
  async getPrepaidPromotionQrCode() {
    const { ctx } = this;

    let pagepath = 'packages/pre-card/home/<USER>';

    const shopInfo = ctx.getState('shopInfo');
    // 分店需要取总店的 kdtId
    const kdtId = shopInfo.rootKdtId || ctx.kdtId;

    pagepath = args.add(pagepath, {
      kdtId,
    });

    return this.getQrCode({
      width: 200,
      pagepath: pagepath,
      kdtId,
    });
  }

  fabricElements2HTML(fabricElements) {
    let htmlString =
      '<style>div,img{position:absolute;}div{z-index:2; font-family: "HarmonyOS Sans SC";}img{z-index:1;} </style> <div class="material-container" style="position: relative;">';

    fabricElements.forEach(element => {
      const commonStyles = `
        top: ${element.top}px;
        left: ${element.left}px;
        width: ${element.width}px;
        height: ${element.height}px;
      `;

      if (element.type === 'text') {
        const textStyles = `
          ${commonStyles}
          font-size: ${element.fontSize};
          font-weight: ${element.fontWeight};
          line-height: ${element.lineHeight};
          color: ${element.color};
          text-align: ${element.textAlign};
        `;

        htmlString += `<div style="${textStyles}">${element.text}</div>`;
      } else if (element.type === 'image') {
        htmlString += `<img src="${element.content}" style="${commonStyles}" />`;
      }
    });

    htmlString += '</div>';
    return htmlString;
  }

  // 获取海报
  async getPoster() {
    const { ctx } = this;
    const snapshotService = new SnapshotService(ctx);
    const { elements, ...params } = ctx.getRequestData();

    const html = this.fabricElements2HTML(JSON.parse(elements));

    const result = await snapshotService
      .snapshot({
        ...params,
        html,
        operatorId: ctx.userId,
      })
      .catch(() => {
        return {
          img: '',
        };
      });
    ctx.success(result);
  }

  async getPreviewQrcode() {
    const { ctx } = this;
    const result = await new ShowcasePrepaidAgentService(ctx).genPreviewUrl({
      operatorId: ctx.userId,
      imgId: Number(ctx.query.imgId),
      kdtId: ctx.kdtId,
    });
    ctx.success(result);
  }

  async estimateV2() {
    const { ctx } = this;
    const params = {
      ...ctx.request.body,
      kdtId: ctx.kdtId,
      adminId: ctx.userId,
    };
    const res = await new CustomerCrowdManageService(ctx).estimate(params);
    ctx.success(res);
  }

  async aigcMiddleId() {
    const { ctx } = this;
    const { kdtId, userId } = ctx;
    const { requestDesc } = ctx.getPostData() || {};
    const hqKdtId = this.getHqKdtId();
    const userInfo = ctx.getState('userInfo');
    ctx.logger.info(`🚀 ~ IndexController ~ aigcMiddleId ~ hqKdtId: ${hqKdtId}`);

    const idRes = await new ContentAsyncService(ctx).queryAiGenerateContentId({
      kdtId,
      hqKdtId: hqKdtId || kdtId,
      userId,
      userPhoneNumber: userInfo.mobile,
      itemType: 3,
      requestDesc,
      requestUrl: '',
      requestUrlHash: 'cg:test:9',
      promoteChannel: 5,
      isEmoji: 0,
      wordNumber: 20,
      contentCount: 1,
      toneStyle: 0,
      requestKeywords: '',
      itemComment: '',
      sourceType: 1,
      sceneTag: 'retail_crowd_manage_strategy',
    });

    return ctx.success(idRes);
  }

  async aigcTextInfo() {
    const { ctx } = this;
    const { responseId } = ctx.getPostData() || {};

    const res = await new AsyncResponseService(ctx).getAiGenerateContentById({
      responseId: responseId || '',
    });

    return ctx.success(res);
  }

  async queryCategoryByCategoryTypeAndMediaType() {
    const { ctx } = this;

    const result = await new CategoryReadService(ctx).queryCategoryByCategoryTypeAndMediaType({
      partnerBizId: ctx.kdtId,
      partnerBizType: 1,
      categoryType: 81,
      mediaType: 1,
    });
    ctx.success(result);
  }

  get operator() {
    const ctx = this.ctx;
    const { fromApp, OPERATOR_TYPE_ENUM } = constants;
    const operator = {
      fromApp,
      operatorType: OPERATOR_TYPE_ENUM.YZ_ACCOUNT,
      operatorId: ctx.userId,
      ip: ctx.firstXff,
      partnerBizType: 1,
      partnerBizId: ctx.kdtId,
    };
    return operator;
  }

  // 抓取远程图片
  // 兼容 https://materials.youzan.com/shop/fetchPubImg.json
  async uploadNetworkImage(ctx) {
    const { channel, categoryId, attachment_url: fetchUrl, title } = ctx.request.body;
    const maxSize = 10 * 1024 * 1024;
    const { fromApp, ip, operatorType, operatorId, partnerBizType, partnerBizId } = this.operator;
    const params = {
      channel: channel || Channel,
      categoryId,
      fetchUrl,
      fromApp,
      ip,
      maxSize,
      operatorType,
      operatorId,
      partnerBizType,
      partnerBizId,
      title,
    };

    let result;
    try {
      result = await new QiniuImageWriteService(ctx).fetchPublicImage(params);
    } catch (err) {
      // 如果请求有返回，优先使用返回的错误信息
      if (err && err.extra && err.extra.response && err.extra.response.message) {
        throw new BusinessException(err.code || 500, err.extra.response.message);
      }
      throw new BusinessException(500, '抓取图片失败，可重试或尝试本地上传');
    }

    const compatResult = {
      attachment_id: result.mediaId,
      attachment_title: result.title,
      attachment_full_url: result.url,
      attachment_url: result.url,
      attachment_size: result.size,
      create_time: result.createdAt,
      file_ext: result.fileExt,
      meta: result.meta,
      thumb_url: result.thumbnailUrl,
      kdt_id: ctx.kdtId,
      width: result.width,
      height: result.height,
    };
    try {
      compatResult.attachment_file = urlib.parse(result.url).pathname.substr(1);
      compatResult.thumb_file = urlib.parse(result.thumbnailUrl).pathname.substr(1);
    } catch (err) {
      // eslint-disable-next-line no-console
      console.error(err);
    }
    if (!channel) {
      this.logNoChannel();
    }
    ctx.success(compatResult);
  }

  // 需要传channel但没有传的，打个日志
  logNoChannel() {
    const ctx = this.ctx;

    // 整理索引信息
    const messages = [
      '[materials-need-channel]',
      `[${ctx.href}]`,
      `ip: ${ctx.firstXff}`,
      `refer: ${ctx.headers.referer}`,
      `ua: ${ctx.headers['user-agent']}`,
    ];

    if (ctx.headers['x-cat-trace']) {
      try {
        const trace = JSON.parse(ctx.headers['x-cat-trace']);
        if (trace._catRootMessageId) {
          messages.push(`rootId: ${trace._catRootMessageId}`);
        }
        if (trace._catParentMessageId) {
          messages.push(`msgId: ${trace._catParentMessageId}`);
        }
        if (trace._catChildMessageId) {
          messages.push(`childId: ${trace._catChildMessageId}`);
        }
      } catch (err) {
        // eslint-disable-next-line no-console
        console.error(err);
      }
    }

    const messageString = messages.join(' ');
    this.ctx.logger.warn(messageString, '', {
      type: 'materials-need-channel',
      method: ctx.method,
      referer: ctx.headers.referer,
      ip: ctx.firstXff,
      query: ctx.getQuery(),
      body: ctx.request.body,
      kdtId: ctx.kdtId,
      trace: ctx.headers['x-cat-trace'],
      env: ctx.app.env,
    });
  }

  /**
   * 操作人信息, 大部分 dubbo 请求需要携带的参数
   */
  getOperator() {
    const userInfo = this.ctx.getLocalSession('userInfo');
    const operator = {
      clientIp: this.firstXff,
      nickName: userInfo.nickName,
      userId: userInfo.id,
    };
    return operator;
  }
  /**
   * 通用 dubbo 调用信息
   */
  getRequestContext() {
    const { kdtId } = this.ctx;

    return {
      fromApp: 'wsc-pc-v4',
      kdtId,
      operator: {
        ...this.getOperator(),
        source: 'wsc-pc-v4',
      },
    };
  }

  async queryGoods() {
    const { ctx } = this;
    const { itemIds } = ctx.getQueryData();

    let result;
    if (await this.checkIsSupportSalesChannel()) {
      result = await new RetailLiteItemQueryService(ctx).querySimpleProducts({
        ...this.getRequestContext(),
        productIds: JSON.parse(itemIds || '[]'),
        withProductPrice: true,
      });
    } else {
      const params = {
        ...this.getRequestContext(),
        showSoldOut: 2,
        oneItemIdList: JSON.parse(itemIds || '[]'),
        option: {
          withSkuInfo: true, // 拿到 sku - stock 相关的信息
        },
      };
      result = await new ItemQueryService(ctx).listItemDetailsByOneIds(params);
    }
    return ctx.success(result);
  }

  async queryGoodsGroup() {
    const { ctx } = this;
    const { groupIds } = ctx.getQueryData();
    const result = await new ItemGroupQueryService(ctx).listGroupByOneIds({
      ...this.getRequestContext(),
      oneGroupIdList: JSON.parse(groupIds || '[]'),
    });
    return ctx.success(result);
  }

  // 获取智能体详情，返回值是一个 json 字符串
  async getAgentDetail() {
    const { ctx } = this;
    const { activityNo } = ctx.getQueryData();
    const result = await new PrePaidAgentQueryService(ctx).getCardValueAgentDetail({
      activityNo,
    });

    ctx.success(result);
  }

  // 写入智能体详情，入参是一个 json 字符串
  async saveAgentDetail() {
    const { ctx } = this;
    const { activityNo, content } = ctx.getPostData();
    const result = await new PrePaidAgentOperationService(ctx).saveCardValueAgentDetail({
      activityNo,
      content,
    });
    ctx.success(result);
  }

  // 查询全部数据
  async queryAgentActivityAllData() {
    const {
      ctx
    } = this;
    const result = await new PrePaidAgentQueryService(ctx).queryAgentActivityAllData({
      kdtId: ctx.kdtId
    });

    ctx.success(result);
  }

  // 查询单个数据
  async queryAgentActivityData() {
    const {
      ctx
    } = this;
    const {
      activityNo
    } = ctx.getQueryData();
    const result = await new PrePaidAgentQueryService(ctx).queryAgentActivityData({
      activityNo,
      kdtId: ctx.kdtId
    });
    ctx.success(result);
  }

  // 查询趋势数据
  async queryAgentActivityTrendData() {
    const {
      ctx
    } = this;
    const {
      activityNo
    } = ctx.getQueryData();
    const result = await new PrePaidAgentQueryService(ctx).queryAgentActivityTrendData({
      activityNo,
      kdtId: ctx.kdtId
    });

    ctx.success(result)
  }
}

module.exports = IndexController;
