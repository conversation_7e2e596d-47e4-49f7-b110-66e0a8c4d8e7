const BaseController = require('../base/BaseController');

class ProtocolController extends BaseController {
  constructor(ctx) {
    super(ctx);
    this.ctx.biz = '服务协议';
  }

  // 微商城服务协议
  async getWscHtml(ctx) {
    await ctx.render('protocol/wsc.html');
  }

  // 用户授权服务协议
  async getUserAuthHtml(ctx) {
    await ctx.render('protocol/user.html');
  }

  // 有赞用户服务协议
  async getUserServiceHtml(ctx) {
    await ctx.render('protocol/user-service.html');
  }
}

module.exports = ProtocolController;
