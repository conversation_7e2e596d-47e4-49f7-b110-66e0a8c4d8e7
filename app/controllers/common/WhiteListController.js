const BaseController = require('../base/BaseController');

class WhiteListController extends BaseController {
  constructor(ctx) {
    super(ctx);
    this.ctx.biz = '白名单';
  }

  async getGrayReleaseJson(ctx) {
    const { kdtId = 0 } = ctx;
    const { key } = ctx.query;

    this.validator.required(key, '白名单 key 不能为空');
    const result = await this.grayRelease(key, kdtId);

    ctx.json(0, 'ok', result);
  }
}

module.exports = WhiteListController;
