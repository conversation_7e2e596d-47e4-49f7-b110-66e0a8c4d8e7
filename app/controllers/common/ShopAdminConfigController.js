const BaseController = require('../base/BaseController');
const ShopAdminConfigReadService = require('../../services/api/shopcenter/shopconfig/ShopAdminConfigReadService');
const ShopAdminConfigWriteService = require('../../services/api/shopcenter/shopconfig/ShopAdminConfigWriteService');
const { fromApp } = require('../../constants');

class ShopAdminConfigController extends BaseController {
  constructor(ctx) {
    super(ctx);
    this.ctx.biz = '店铺管理员配置';
  }

  async getShopAdminConfig(ctx) {
    const { kdtId } = ctx;
    const { userId } = ctx.getLocalSession('userInfo');

    const { key } = ctx.query;
    this.validator.required(key, '配置项不能为空');

    const result = await new ShopAdminConfigReadService(ctx).queryShopAdminConfig(kdtId, userId, key);
    return ctx.json(0, 'OK', result);
  }

  async setShopAdminConfig(ctx) {
    const { kdtId } = ctx;
    const { userId, nickName } = ctx.getLocalSession('userInfo');

    const { key, value } = ctx.request.body;
    this.validator.required(key, '配置项不能为空');
    this.validator.required(value, '配置项值不能为空');

    const params = {
      kdtId,
      adminId: userId,
      operator: { id: userId, name: nickName, type: 1, fromApp },
      key,
      value,
    };
    const result = await new ShopAdminConfigWriteService(ctx).setShopAdminConfig(params);
    return ctx.json(0, 'OK', result);
  }
}

module.exports = ShopAdminConfigController;
