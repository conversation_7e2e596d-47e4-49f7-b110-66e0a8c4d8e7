const BaseController = require('../base/BaseController');
const ShopConfigReadService = require('../../services/api/shop-config/ShopConfigReadService');
const ShopConfigWriteService = require('../../services/api/shop-config/ShopConfigWriteService');
const ShopOperateLogService = require('../../services/api/operate-log/ShopOperateLogService');
const { fromApp } = require('../../constants');

class ShopConfigController extends BaseController {
  constructor(ctx) {
    super(ctx);
    this.ctx.biz = '店铺配置';
  }

  async getShopConfigs(ctx) {
    const { kdtId } = ctx;
    const { configs } = ctx.query;
    this.validator.required(configs, '配置项不能为空');

    const params = JSON.parse(configs);
    const result = await new ShopConfigReadService(ctx).queryShopConfigs(kdtId, params);
    return ctx.json(0, 'OK', result);
  }

  async updateShopConfigs(ctx) {
    const { kdtId } = ctx;
    const userInfo = ctx.getLocalSession('userInfo');
    const { configs } = ctx.request.body;
    this.validator.required(configs, '配置项不能为空');

    const parseConfigs = JSON.parse(configs);

    const params = {
      kdtId,
      operator: {
        id: userInfo.userId,
        name: userInfo.nickName,
        type: 1,
        fromApp,
      },
      configs: parseConfigs,
    };

    // 获取最新的店铺日志
    const homeGray = await new ShopConfigReadService(this.ctx).queryShopConfig(
      this.ctx.kdtId,
      'homepage_gray'
    );

    const result = await new ShopConfigWriteService(ctx).setShopConfigs(params);

    // 记录日志
    await this.logHomeGray(parseConfigs, homeGray, userInfo);

    return ctx.json(0, 'OK', result);
  }

  async logHomeGray(configs, homeGray, userInfo) {
    try {
      if (homeGray.value !== configs.homepage_gray) {
        const getTimeDesc = grayData => {
          if (grayData.isOpen) {
            return `置灰，置灰时间段为 ${grayData?.timeRange?.[0]} 至 ${grayData?.timeRange?.[1]}`;
          }

          return '不置灰';
        };

        const afterRule = JSON.parse(configs.homepage_gray);
        const beforeRule = JSON.parse(homeGray.value);
        const desc = `修改了店铺首页置灰规则，修改后：${getTimeDesc(
          afterRule
        )}；修改前：${getTimeDesc(beforeRule)}`;

        await new ShopOperateLogService(this.ctx).log({
          appName: 'wsc-pc-v4',
          bizCode: 'changeHomeGray',
          bizModule: 'SET',
          kdtId: this.ctx.kdtId,
          logContent: desc,
          operatorType: 'SHOP_STAFF', // 操作人账号类型：1 卖家，2 内部员工cas id，3 未知，4 系统行为（如监听NSQ并主动修改数据）
          operateTime: new Date().getTime(),
          operatorUserId: this.ctx.userId,
          operatorName: userInfo.nickName,
          clientIp: this.ctx.clientIp,
        });
      }
    } catch (error) {
      this.ctx.logger.error('日志记录错误', error);
    }
  }

  async setShopConfig(ctx) {
    const { kdtId } = ctx;
    const userInfo = ctx.getLocalSession('userInfo');
    const { key, value } = ctx.request.body;
    this.validator.required(key, '配置项不能为空');
    this.validator.required(value, '配置项值不能为空');

    const params = {
      kdtId,
      operator: {
        id: userInfo.userId,
        name: userInfo.nickName,
        type: 1,
        fromApp,
      },
      key,
      value,
    };
    const result = await new ShopConfigWriteService(ctx).setShopConfig(params);
    return ctx.json(0, 'OK', result);
  }

  async isLocalDeliveryKdtWhite(ctx) {
    const { kdtId } = ctx;
    const result = await this.grayRelease('local_delivery', kdtId);
    return ctx.json(0, 'OK', result);
  }
}

module.exports = ShopConfigController;
