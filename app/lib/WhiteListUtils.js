/**
 * 解析apollo配置的切流决策
 * apollo的文本配置以半角逗号分隔，百分比值写在第一项，其次白名单，最后黑名单
 * 遇到0或者0%会判为不匹配
 * '%1,491391,603367,-160,-11998'
 * const useNew = isInGrayReleaseByKdtId(ctx, { namespace: '', key: '' }, kdtId);
 */
function matchGrayConfig(config, kdtId) {
  let kdtIdList = [];
  if (Array.isArray(config)) {
    kdtIdList = config;
  } else {
    kdtIdList = String(config).split(',');
  }
  // 先判断0或者黑名单的情况
  if (kdtIdList.includes('0') || kdtIdList.includes('0%') || kdtIdList.includes('-' + kdtId)) {
    return false;
  } else if (kdtIdList.includes(String(kdtId))) {
    // kdtId全匹配
    return true;
  } else if (config.indexOf('%') > 0) {
    // 百分比判断
    const percentArr = kdtIdList
      .filter(singleConfig => {
        return singleConfig.endsWith('%');
      })
      .map(singleConfig => {
        return singleConfig.slice(0, singleConfig.length - 1);
      });
    if (percentArr && percentArr.length) {
      // 只取第一个百分比配置
      const onlyPercent = Number(percentArr[0]);
      return !!(onlyPercent >= 0 && onlyPercent <= 100 && Number(kdtId) % 100 <= onlyPercent);
    } else {
      return false;
    }
  } else {
    return false;
  }
}

/**
 * 解析apollo配置的切流决策
 * @param ctx
 * @param { namespace, key: '只支持单key' }
 * @param kdtId
 */
module.exports = async function isInGrayReleaseByKdtId(ctx, { namespace, key, appId }, kdtId) {
  let matched = false;
  try {
    const configList =
      ctx.apolloClient.getConfig({
        appId,
        namespace,
        key,
      }) || '';
    matched = matchGrayConfig(configList, kdtId);
  } catch (e) {
    // console.log('get apollo config error:', e);
    matched = false;
  }
  // console.log('matched:', matched);

  return matched;
};
