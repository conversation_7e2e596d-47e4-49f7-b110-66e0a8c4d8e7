const BaseService = require('../../base/BaseService');
/**
 * 消息中心Service
 * @class ConversationService
 * @extends {BaseService}
 */
class MessageMarketingSupportSettingService extends BaseService {
  get SERVICE_NAME() {
    return 'com.youzan.saas.message.biz.misc.api.marketing.MessageMarketingSupportSettingService';
  }

  /**
   *  消息推送卡片明细
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/632246
   *
   *  @param {number} kdtId -
   *  @return {Promise}
   */
  async getPushStatus4Manager(kdtId) {
    return this.invoke('getPushStatus4Manager', [kdtId]);
  }
}

module.exports = MessageMarketingSupportSettingService;
