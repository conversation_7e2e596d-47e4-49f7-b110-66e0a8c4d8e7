const BaseService = require('../../base/BaseService');

/**
 * 消息中心Service
 * @class ConversationService
 * @extends {BaseService}
 */
class ConversationService extends BaseService {
  /**
   * 消息中心Service
   * @readonly
   * @memberof ConversationService
   */
  get SERVICE_NAME() {
    return 'com.youzan.platform.courier.api.ConversationService';
  }

  /**
   * 获取未回复的客户消息
   * http://zanapi.qima-inc.com/site/service/view/117193
   * @param {Object} param
   * @param {integer} param.adminId 用户id
   * @param {integer} param.kdtId 店铺id
   * @return {integer} 未回复消息数
   * @memberof ConversationService
   */
  async getUnreadConversations(param) {
    const result = await this.invoke('getUnreadConversations', [param]);
    return result;
  }
}

module.exports = ConversationService;
