const BaseService = require('../../base/BaseService');

/**
 * 有赞担保Service
 * @class SecuredService
 * @extends {BaseService}
 */
class SecuredService extends BaseService {
  /**
   * 有赞担保Service
   * @readonly
   * @memberof SecuredService
   */
  get DOMAIN_NAME() {
    return 'SECURED_URL';
  }

  /**
   * http://zanapi.qima-inc.com/site/api/view/9073
   * 获取有赞担保状态
   * FIXME需要替换dubbo接口
   * @param {integer} kdtId
   * @return {Object}
   * @memberof SecuredService
   */
  async query(kdtId) {
    const result = await this.ajax({
      url: '/secured/query',
      method: 'POST',
      data: {
        kdtId,
      },
      contentType: 'application/json',
    });
    return result;
  }
}

module.exports = SecuredService;
