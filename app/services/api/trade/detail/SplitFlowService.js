const BaseService = require('../../../base/BaseService');

/**
 * SplitFlowService
 */
class SplitFlowService extends BaseService {
  /**
   * @return {string}
   */
  get SERVICE_NAME() {
    return 'com.youzan.ebiz.trade.api.manage.detail.SplitFlowService';
  }

  /**
   * 根据kdtId查询 白名单切流 买家自助修改地址
   * @param {Object} params
   * @param {number} params.kdtId
   * 文档: http://zanapi.qima-inc.com/site/service/view/1255961
   */
  async modifyAddressSplitFlow(params) {
    return this.invoke('modifyAddressSplitFlow', [params]);
  }
}

module.exports = SplitFlowService;
