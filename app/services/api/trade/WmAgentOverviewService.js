const BaseService = require('../../base/BaseService');

/**
 * 外卖托管智能体概况
 */
class WmAgentOverviewService extends BaseService {
  get SERVICE_NAME() {
    return 'com.youzan.retail.trademanager.biz.api.service.agent.wm.WmAgentOverviewService';
  }

  /**
   *  获取外卖托管智能体概况信息
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/1585451
   *
   *  @param {Object} request -
   *  @param {string} request.retailSource - 订单来源
   *  @param {number} request.endDate - 结束日期
   *  @param {number} request.kdtId - 店铺ID
   *  @param {string} request.requestIp - 请求方ip
   *  @param {string} request.source - 来源 如Android iOS web
   *  @param {boolean} request.needOpeButton - 是否需要操作按钮
   *  @param {number} request.buyerId -
   *  @param {string} request.version - 版本号
   *  @param {string} request.apiVersion - carmen接口版本号
   *  @param {string} request.requestId - 请求唯一标识
   *  @param {number} request.adminId -
   *  @param {number} request.startDate - 开始日期
   *  @param {string} request.requestFrom - 请求来源
   *  @return {Promise}
   */
  async getWmAgentOverview(request) {
    return this.invoke('getWmAgentOverview', [request]);
  }
}

module.exports = WmAgentOverviewService;
