const BaseService = require('../../base/BaseService');

/**
 * InvoiceSwitchService
 */
class GeneralSearchService extends BaseService {
  get SERVICE_NAME() {
    return 'com.youzan.trade.search.api.GeneralSearchService';
  }

  /**
   *  订单热状态统计接口
   *  https://doc.qima-inc.com/pages/viewpage.action?pageId=226581827
   *
   *  @param {object} stateOrderCountParam - params
   *  @return {Promise}
   */
  async countStateOrder(stateOrderCountParam) {
    return this.invoke('countStateOrder', [stateOrderCountParam]);
  }
}

module.exports = GeneralSearchService;
