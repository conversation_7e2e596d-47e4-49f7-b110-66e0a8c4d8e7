const BaseService = require('../../base/BaseService');

/**
 * InvoiceSwitchService
 */
class InvoiceSwitchService extends BaseService {
  get SERVICE_NAME() {
    return 'com.youzan.trade.invoice.api.tool.InvoiceSwitchService';
  }

  /**
   *  查询店铺的发票服务是否开启
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/11680
   *
   *  @param {number} kdtId - 店铺id
   *  @return {Promise}
   */
  async isInvoiceServiceOpening(kdtId) {
    return this.invoke('isInvoiceServiceOpening', [kdtId]);
  }
}

module.exports = InvoiceSwitchService;
