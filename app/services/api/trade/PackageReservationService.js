const BaseService = require('../../base/BaseService');

/** com.youzan.ump.manage.api.specific.PackageReservationService -  */
class PackageReservationService extends BaseService {
  get SERVICE_NAME() {
    return 'com.youzan.ump.manage.api.specific.PackageReservationService';
  }

  /**
   *  活动详情
   *  zanAPI文档地址: https://zanapi.qima-inc.com/site/service/view/1595952
   *
   *  @param {Object} param -
   *  @param {number} param.shopId - 店铺id
   *  @return {Promise}
   */
  async getActivityDetails(param) {
    return this.invoke('getActivityDetails', [param]);
  }

  /**
   *  编辑
   *  zanAPI文档地址: https://zanapi.qima-inc.com/site/service/view/1595945
   *
   *  @param {Object} param -
   *  @param {number} param.shopId - 店铺id
   *  @param {number} param.status - 1:开启 2:关闭
   *  @param {number} param.goodsJoinType
   *  @param {number} param.bookingType
   *  @param {number} param.appId
   *  @param {number} param.shortLink
   *  @param {number} param.miniProgramPath
   *  @param {number} param.bootTitle
   *  @param {number} param.buttonCopywriting
   *  @param {number} param.liveCodeDTO
   *  @param {number} param.goodsList
   *  @param {number} param.goodsGroupId
   *  @return {Promise}
   */
  async edit(param) {
    return this.invoke('edit', [param]);
  }

  /**
   *  创建
   *  zanAPI文档地址: https://zanapi.qima-inc.com/site/service/view/1597196
   *
   *  @param {Object} param -
   *  @param {number} param.shopId - 店铺id
   *  @param {number} param.goodsJoinType
   *  @param {number} param.bookingType
   *  @param {number} param.appId
   *  @param {number} param.shortLink
   *  @param {number} param.miniProgramPath
   *  @param {number} param.bootTitle
   *  @param {number} param.buttonCopywriting
   *  @param {number} param.liveCodeDTO
   *  @param {number} param.goodsList
   *  @param {number} param.goodsGroupId
   *  @return {Promise}
   */
  async create(param) {
    return this.invoke('create', [param]);
  }
}

module.exports = PackageReservationService;
