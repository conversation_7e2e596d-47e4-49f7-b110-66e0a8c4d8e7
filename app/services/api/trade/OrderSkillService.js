const BaseService = require('../../base/BaseService');

/**
 * 订单技能服务
 */
class OrderSkillService extends BaseService {
  get SERVICE_NAME() {
    return 'com.youzan.retail.trademanager.biz.api.service.agent.wm.OrderSkillService';
  }

  /**
   *
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/1585176
   *
   *  @param {Object} request -
   *  @param {number} request.skillId -
   *  @param {number} request.agentId -
   *  @param {number} request.kdtId -
   *  @return {Promise}
   */
  async getOrderSkill(request) {
    return this.invoke('getOrderSkill', [request]);
  }

  /**
   *
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/1585175
   *
   *  @param {Object} request -
   *  @param {number} request.agentId - agentId（AI团队提供）
   *  @param {Array.<Object>} request.skillList[] - 智能体技能列表
   *  @param {number} request.rootKdtId - 商家总部Id
   *  @param {number} request.kdtId - 商家店铺Id（如果是总部授权智能体，可以为空）
   *  @param {string} request.agentName - agent名称
   *  @return {Promise}
   */
  async createOrderSkill(request) {
    return this.invoke('createOrderSkill', [request]);
  }
}

module.exports = OrderSkillService;
