const BaseService = require('../../../base/BaseService');

/** com.youzan.trade.business.setting.api.TradeSettingService */
class TradeSettingService extends BaseService {
  get SERVICE_NAME() {
    return 'com.youzan.trade.business.setting.api.TradeSettingService';
  }

  /**
   * 获取极速下单设置
   *  该接口主要是包装店铺配置的逻辑（提供给B端使用，C端如需使用请单独查询店铺配置接口）
   * @link http://zanapi.qima-inc.com/site/service/view/1482859
   * @param {Object} dto - 极速下单配置
   * @param {number} dto.kdtId - 店铺ID
   * @return {Promise}
   */
  async getFastTradeSetting(dto) {
    return this.invoke('getFastTradeSetting', [dto]);
  }

  /**
   * 极速下单设置编辑接口
   * @link http://zanapi.qima-inc.com/site/service/view/1482860
   * @param {Object} dto - 极速下单配置
   * @param {number} dto.settingStatus - 开关状态
   *  0:关闭,1:开启
   * @param {number} dto.kdtId - 店铺ID
   * @param {number} dto.type - 设置值
   * @return {Promise}
   */
  async editFastTradeSetting(dto) {
    return this.invoke('editFastTradeSetting', [dto]);
  }
}

module.exports = TradeSettingService;
