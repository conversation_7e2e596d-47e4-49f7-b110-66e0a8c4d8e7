const BaseService = require('../../../base/BaseService');

/**
 * com.youzan.trade.business.secured.api.SecuredTXQueryService
 */
class SecuredTXQueryService extends BaseService {
  get SERVICE_NAME() {
    return 'com.youzan.trade.business.secured.api.SecuredTXQueryService';
  }

  /**
   *  担保交易状态查询
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/44264
   *
   *  @param {number} kdtId -
   *  @return {Promise}
   */
  async querySecuredTransactionsStatus(kdtId) {
    return this.invoke('querySecuredTransactionsStatus', [kdtId]);
  }

  /**
   *  担保交易相关数据统计查询
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/347701
   *
   *  @param {number} kdtId -
   *  @return {Promise}
   */
  async listSecuredTransactionsStatData(kdtId) {
    return this.invoke('listSecuredTransactionsStatData', [kdtId]);
  }
}

module.exports = SecuredTXQueryService;
