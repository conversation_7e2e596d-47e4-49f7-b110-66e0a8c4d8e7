const BaseService = require('../../base/BaseService');

/**
 * 小票升级相关接口
 */
class PrinterMigrationService extends BaseService {
  get SERVICE_NAME() {
    return 'com.youzan.mall.o2o.api.service.PrinterMigrationService';
  }

  /**
   *  单店铺 打印机迁移到小票中台
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/926950
   *
   *  @param {Object} request -
   *  @param {string} request.operatorHost - 操作人 ip
   *  @param {number} request.kdtId - 店铺id
   *  @param {number} request.operatorId - 操作人id
   *  @return {Promise}
   */
  async migrationToIot(request) {
    return this.invoke('migrationToIot', [request]);
  }
}

module.exports = PrinterMigrationService;
