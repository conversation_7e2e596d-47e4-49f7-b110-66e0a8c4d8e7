const ShopConfigBaseService = require('./ShopConfigBaseService');

/**
 * ShopAdminConfigWriteService
 * 文档: http://zanapi.qima-inc.com/site/service/view/76614
 */
class ShopAdminConfigWriteService extends ShopConfigBaseService {
  /**
   * Using uniform service
   * @return {String}
   */
  get SERVICE_NAME() {
    return 'com.youzan.shopcenter.shopconfig.api.service.ShopAdminConfigWriteService';
  }

  /**
   * 设置提示引导框的状态
   * @param {*} params
   *
   * 文档: http://zanapi.qima-inc.com/site/service/view/155979
   */
  async setShopAdminConfig(params) {
    const result = await this.invoke('setShopAdminConfig', [
      {
        ...params,
      },
    ]);

    return result;
  }
}

module.exports = ShopAdminConfigWriteService;
