const ShopConfigBaseService = require('./ShopConfigBaseService');

/**
 * ShopStyleService
 * 文档: https://doc.qima-inc.com/pages/viewpage.action?pageId=55660071
 */
class ShopConfigService extends ShopConfigBaseService {
  /**
   * Using uniform service
   * @return {String}
   */
  get SERVICE_NAME() {
    return 'com.youzan.mall.shop.api.service.shop.ShopStyleService';
  }

  /**
   * 获取引导框状态
   * @param {*} params
   *
   * 文档: https://doc.qima-inc.com/pages/viewpage.action?pageId=55660071
   */
  async getShopConfig(params) {
    const { kdtId, adminId } = params;
    const result = await this.invoke('isShopEnablePopAdvertisement', [kdtId, adminId]);

    return result;
  }
}

module.exports = ShopConfigService;
