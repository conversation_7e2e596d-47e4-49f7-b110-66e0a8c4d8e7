const BaseService = require('../../base/BaseService');

/**
 * 智能体
 */
class MyAgentService extends BaseService {
  get SERVICE_NAME() {
    return 'com.youzan.agent.api.service.MyAgentService';
  }

  /**
   *  获取我的智能体详情(包含基本信息和运行数据)
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/1541771
   *
   *  @param {Object} request - 请求参数
   *  @param {number} request.kdtId - 店铺ID
   *  @param {number} request.myAgentId - 我的智能体ID
   *  @param {Object} request.operator - 操作人信息
   *  @param {string} request.operator.operatorId -
   *  @param {string} request.operator.operatorName -
   *  @return {Promise}
   */
  async getMyAgentDetail(request) {
    return this.invoke('getMyAgentDetail', [request]);
  }

  /**
   *  分页获取我的智能体执行记录
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/1541772
   *
   *  @param {Object} request - 请求参数
   *  @param {number} request.kdtId - 店铺id
   *  @param {number} request.myAgentId - 智能体id
   *  @param {number} request.pageSize - 每页大小
   *  @param {number} request.page - 页码
   *  @param {Object} request.operator - 操作人信息
   *  @param {string} request.operator.operatorId -
   *  @param {string} request.operator.operatorName -
   *  @return {Promise}
   */
  async pageExecuteRecords(request) {
    return this.invoke('pageExecuteRecords', [request]);
  }
}

module.exports = MyAgentService;
