const BaseService = require('../../base/BaseService');

/**
 * 智能体
 */
class MyAgentSkillService extends BaseService {
  get SERVICE_NAME() {
    return 'com.youzan.agent.api.service.MyAgentSkillService';
  }

  /**
   *
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/1542564
   *
   *  @param {Object} query -
   *  @param {number} query.agentId -
   *  @param {Object} query.operator - 操作人信息
   *  @param {string} query.operator.operatorId -
   *  @param {string} query.operator.operatorName -
   *  @return {Promise}
   */
  async querySkills(query) {
    return this.invoke('querySkills', [query]);
  }

  /**
   *
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/1542563
   *
   *  @param {Object} operate -
   *  @param {number} operate.skillId -
   *  @param {number} operate.agentId -
   *  @param {number} operate.kdtId -
   *  @param {boolean} operate.enable -
   *  @param {Object} operate.config -
   *  @param {Object} operate.operator - 操作人信息
   *  @param {string} operate.operator.operatorId -
   *  @param {string} operate.operator.operatorName -
   *  @return {Promise}
   */
  async save(operate) {
    return this.invoke('save', [operate]);
  }
}

module.exports = MyAgentSkillService;
