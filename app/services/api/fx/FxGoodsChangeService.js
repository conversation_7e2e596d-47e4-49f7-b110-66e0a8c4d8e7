const BaseService = require('../../base/BaseService');

/**
 * com.youzan.fx.goods.service.FxGoodsChangeService
 */
class FxGoodsChangeService extends BaseService {
  get SERVICE_NAME() {
    return 'com.youzan.fx.goods.service.FxGoodsChangeService';
  }

  /**
   *  获取分销商基础信息  by KdtId
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/140275
   *  @param {integer} kdtId - 店铺id
   *  @return {boolean}
   */
  async checkExistChange(kdtId) {
    return this.invoke('checkExistChange', [kdtId]);
  }
}

module.exports = FxGoodsChangeService;
