const BaseService = require('../../base/BaseService');

/**
 * com.youzan.fx.team.api.service.seller.SellerService
 */
class SellerService extends BaseService {
  get SERVICE_NAME() {
    return 'com.youzan.fx.team.api.service.seller.SellerService';
  }

  /**
   *  获取分销商基础信息  by KdtId
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/140275
   *  @param {integer} kdtId - 店铺id
   *  @return {object}
   */
  async getBaseInfo(kdtId) {
    return this.invoke('getBaseInfo', [kdtId]);
  }
}

module.exports = SellerService;
