const BaseService = require('../../base/BaseService');

/**
 * com.youzan.scrm.api.agreement.service.AgreementProdService
 **/
class AgreementProdService extends BaseService {
  get SERVICE_NAME() {
    return 'com.youzan.scrm.api.agreement.service.AgreementProdService';
  }

  /**
   * 根据协议产品类型查询平台维度产品模板
   * zanApi: http://zanapi.qima-inc.com/site/service/view/516488
   * @param {Object} param - 请求数据
   * @return {Promise}
   */
  async queryByType(param) {
    return this.invoke('queryByType', [param]);
  }

  /**
   *  获取大客定制协议
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/1250014
   *
   *  @param {Object} param
   *  @param {string[]} param.bizTypeList - 业务类型
   *  @param {number} param.kdtId - 店铺id
   *  @param {number} [param.currentKdtId] - 当前店铺 ID，比如连锁分店为分店 ID
   *  @param {string} [param.appName] - 应用名
   *  @param {string} [param.payLoad] - 自定义参数透传，比如标签等 shopType 的透传
   *  @param {string} [param.source] - 分配给业务方的source,用来做一些限流等识别业务的标识
   *  @return {Promise<any[]>}
   */
  async listByBizType(param) {
    return this.invoke('listByBizType', [param]);
  }

  /**
   *  根据协议产品标识 批量查询
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/1182273
   *
   *  @param {Object} param -
   *  @param {Array.<Array>} param.agProdTypeList[] - 协议产品模版产品类型
   *  @param {Array} param.agProdTypeList[] -
   *  @param {string} param.payLoad - 自定义参数透传，比如标签等shopType的透传
   *  @param {string} param.appName - 应用名
   *  @param {string} param.source - 分配给业务方的source,用来做一些限流等识别业务的标识
   *  @return {Promise}
   */
  async batchQuery(param) {
    return this.invoke('batchQuery', [param]);
  }
}

module.exports = AgreementProdService;
