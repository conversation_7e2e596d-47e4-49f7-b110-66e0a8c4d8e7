const BaseService = require('../../base/BaseService');

/**
 * https://doc.qima-inc.com/pages/viewpage.action?pageId=15167471
 * 认证Service
 * @class CertService
 * @extends {BaseService}
 */
class CertService extends BaseService {
  /**
   * 获取主体认证状态
   * @param {integer} kdtId
   * @return {Object}
   * @memberof CertService
   */
  async getGroupCertification(kdtId) {
    const result = await this.apiCall({
      url: '/shop/cert/certification/getGroupCertification',
      data: {
        // eslint-disable-next-line camelcase
        kdt_id: kdtId,
      },
    });
    return this.toCamel(result);
  }

  /**
   * 获取认证主体
   * @param {number} kdtId 店铺id
   */
  async getGroupSubjectNameCertified(kdtId) {
    const result = await this.apiCall({
      url: '/shop/cert/certification/getGroupSubjectNameCertified',
      data: {
        // eslint-disable-next-line camelcase
        kdt_id: kdtId,
      },
    });
    return this.toCamel(result);
  }

  /**
   * 获取店铺认证状态
   * @param {integer} kdtId
   * @return {Object}
   * @memberof CertService
   */
  async getOnlineShopCertification(kdtId) {
    const result = await this.apiCall({
      url: '/shop/cert/certification/getOnlineShopCertification',
      data: {
        // eslint-disable-next-line camelcase
        kdt_id: kdtId,
      },
    });

    return this.toCamel(result);
  }
}

module.exports = CertService;
