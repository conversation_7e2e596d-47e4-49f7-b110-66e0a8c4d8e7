const BaseService = require('../../base/BaseService');

/**
 * 商品分组
 *
 * @class ItemGroupService
 * @extends {BaseService}
 */
class ItemGroupService extends BaseService {
  /**
   *
   *
   * @readonly
   * @memberof ItemGroupService
   */
  get SERVICE_NAME() {
    return 'com.youzan.api.shop.api.sevice.item.ItemGroupService';
  }

  /**
   * 获取商品分组
   *
   * @param {number} kdtId
   * @param {number} page
   * @param {number} size
   * @return {Promise}
   * @memberof ItemGroupService
   */
  async getGoodsGroup(kdtId, page, size) {
    return await this.goodsGroupJavaService('listPageByKdtId', [
      {
        kdtId,
        page,
        size,
      },
    ]);
  }
}

module.exports = ItemGroupService;
