const BaseService = require('../../base/BaseService');
const pageSize = 1000;

const timeoutSafe = 4000;
const invokeOptions = {
  timeout: timeoutSafe,
  headers: { 'x-timeout': timeoutSafe },
};

/**
 * 获取店铺列表（连锁）
 *
 * @class HQStoreSearchService
 * @extends {BaseService}
 */
class HQStoreSearchService extends BaseService {
  /**
   * @memberof HQStoreSearchService
   */
  get SERVICE_NAME() {
    return 'com.youzan.retail.shop.api.hq.service.HQStoreSearchService';
  }

  /**
   * 获取店铺列表（无数据权限）
   *
   * @param {number} kdtId
   * @param {number} hqKdtId
   * @param {Array} shopRoleList
   * @param {number} pageSize
   * @param {number} adminId
   * @param {string} retailSource
   * @return {Promise}
   * @memberof ItemGroupService
   */
  async search({ kdtId, hqKdtId, shopRoleList, pageSize, adminId, retailSource, ...restParams }) {
    // eslint-disable-next-line no-return-await
    return await this.invoke(
      'search',
      [
        {
          kdtId,
          hqKdtId,
          shopRoleList,
          pageSize,
          adminId,
          retailSource,
          ...restParams,
        },
      ],
      invokeOptions
    );
  }
  /**
   * 获取店铺列表（有数据权限）
   * zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/366747
   *
   * @param {number} kdtId
   * @param {number} hqKdtId
   * @param {Array} shopRoleList
   * @param {number} pageSize
   * @param {number} adminId
   * @param {string} retailSource
   * @return {Promise}
   * @memberof ItemGroupService
   */
  async searchWithDataPermission({
    kdtId,
    hqKdtId,
    shopRoleList,
    pageSize,
    adminId,
    retailSource,
    ...restParams
  }) {
    // eslint-disable-next-line no-return-await
    return await this.invoke(
      'searchWithDataPermission',
      [
        {
          kdtId,
          hqKdtId,
          shopRoleList,
          pageSize,
          adminId,
          retailSource,
          ...restParams,
        },
      ],
      invokeOptions
    );
  }

  /**
   * 获取全部店铺列表
   * @param {object} params
   * @param {bool} isPerm
   * @return {Promise}
   * @memberof ItemGroupService
   */
  async getAllStores(params, isPerm = false) {
    let isDone = false;
    let shopCollect = [];
    const handledParams = Object.assign(
      {
        appendShopLifecycleEndTime: false,
        appendOfflineBusinessHours: false,
        appendPosPointNum: false,
        appendLackInfo: false,
      },
      params
    );
    const api = isPerm ? this.searchWithDataPermission.bind(this) : this.search.bind(this);
    const setParams = {
      openSearchAfter: true,
      searchAfter: [0, 0],
    };
    while (!isDone) {
      const respone = await api(
        Object.assign(
          {
            pageSize,
            pageNo: 1,
            ...setParams,
          },
          handledParams
        )
      );
      const shopList = respone.items;
      if (shopList.length) {
        const { shopSort, storeKdtId } = shopList[shopList.length - 1];
        setParams.searchAfter = [shopSort, storeKdtId];
        shopCollect = [...shopCollect, ...shopList];
      } else {
        isDone = true;
      }
    }

    return shopCollect;
  }

  /**
   *  门店管理检索(ES）,过滤敏感信息
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/1120651
   *  @return {Promise}
   */
  async searchFilterSensitiveInfo(request) {
    return this.invoke('searchFilterSensitiveInfo', [request]);
  }
}

module.exports = HQStoreSearchService;
