const BaseService = require('../../base/BaseService');

/**
 * com.youzan.shopcenter.shop.service.ShopContactService
 * @class ShopBaseReadService
 * @extends {BaseService}
 */
class FxInfoService extends BaseService {
  get SERVICE_NAME() {
    return 'com.youzan.fx.team.api.service.seller.SellerContactService';
  }

  /**
   *  查询分销信息
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/893320
   *
   *  @param {number} kdtId -
   *  @return {Promise}
   */
  async getFxInfo(kdtId) {
    return this.invoke('queryTeamInfo', [kdtId]);
  }

  /**
   *  更新分销信息
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/893319
   * @param {object} params
   *  @return {Promise}
   */
  async updateFxInfo(params) {
    return this.invoke('updateSellerContact', [params]);
  }
}

module.exports = FxInfoService;
