const BaseService = require('../../base/BaseService');

/**
 * 获取老应用授权店铺列表
 *
 * @class AppBindingShopService
 * @extends {BaseService}
 */
class AppBindingShopService extends BaseService {
  get SERVICE_NAME() {
    return 'com.youzan.baymax.api.AppBindingShopService';
  }

  /**
   *  店铺后台获取有容器自用型应用信息
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/387605
   *
   *  @param {Object} request - 查询入参
   *  @param {number} request.kdtId -
   *  @param {number} request.appType -
   *  @param {number} request.env -
   *  @return {Promise}
   */
  async queryKdtAppBindingByAppType(request) {
    return this.invoke('queryKdtAppBindingByAppType', [request]);
  }

  /**
   *  店铺后台授权应用
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/387371
   *
   *  @param {Object} request - 授权入参
   *  @param {number} request.kdtId - kdtId
   *  @param {number} request.appId - appId
   *  @param {number} request.env -
   *  @param {number} request.userId - 操作人id
   *  @return {Promise}
   */
  async authorize(request) {
    return this.invoke('authorize', [request]);
  }

  /**
   *  店铺后台解除授权应用
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/387372
   *
   *  @param {Object} request - 解除授权入参
   *  @param {number} request.kdtId - kdtId
   *  @param {number} request.appId - appId
   *  @param {number} request.env -
   *  @param {number} request.userId - 操作人id
   *  @return {Promise}
   */
  async deAuthroize(request) {
    return this.invoke('deAuthroize', [request]);
  }
}

module.exports = AppBindingShopService;
