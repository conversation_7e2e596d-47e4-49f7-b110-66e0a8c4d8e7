const BaseService = require('../../base/BaseService');

/** com.youzan.ic.service.ItemGroupQueryService -  */
class ItemGroupQueryService extends BaseService {
  get SERVICE_NAME() {
    return 'com.youzan.ic.service.ItemGroupQueryService';
  }

  /**
   *  根据指定的分组id列表查询分组详情
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/277628
   *
   *  @param {Object} param - 查询分组详情参数
   *  @param {number} param.kdtId - 店铺id
   *  @param {string} param.fromApp - 请求来源
   *  @param {string} param.requestId - UUID
   *  @param {Array.<Array>} param.groupIds[] - 分组id列表
   *  @param {Array} param.groupIds[] -
   *  @param {number} param.channel - 渠道
   *  @param {string} param.operator - 操作人信息,
   *      json 格式 ['user_id' => $userId, 'nick_name' => $nickName, 'client_ip' => $clientIp]
   *  @return {Promise}
   */
  async queryGroupDetailListByIdsParam(param) {
    return this.invoke('queryGroupDetailListByIdsParam', [param]);
  }

  /**
   *  分页条件查询分组列表
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/300701
   *
   *  @param {Object} queryParam - 分页查询分组列表参数
   *  @param {number} queryParam.kdtId - 店铺id
   *  @param {string} queryParam.fromApp - 请求来源
   *  @param {number} queryParam.startId - 开始分组id 默认为0
   *  @param {string} queryParam.requestId - UUID
   *  @param {number} queryParam.channel - 渠道 0-网店 1-门店
   *  @param {number} queryParam.pageSize - 查询分页大小
   *  @param {string} queryParam.updateTime - 开始更新时间
   *  @param {number} queryParam.page - 查询页码
   *  @param {Array.<Array>} queryParam.includeIsDefaultList[] - 分组类型
   *  @param {string} queryParam.operator - 操作人信息, json 格式
   *  @return {Promise}
   */
  async queryGroupListByParam(queryParam) {
    return this.invoke('queryGroupListByParam', [queryParam]);
  }

  /**
   *  分页条件查询分组列表
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/300701
   *
   *  @param {Object} queryParam - 分页查询分组列表参数
   *  @param {number} queryParam.kdtId - 店铺id
   *  @param {string} queryParam.fromApp - 请求来源
   *  @param {number} queryParam.startId - 开始分组id 默认为0
   *  @param {string} queryParam.requestId - UUID
   *  @param {number} queryParam.channel - 渠道 0-网店 1-门店
   *  @param {number} queryParam.pageSize - 查询分页大小
   *  @param {string} queryParam.updateTime - 开始更新时间
   *  @param {number} queryParam.page - 查询页码
   *  @param {Array.<Array>} queryParam.includeIsDefaultList[] - 分组类型
   *  @param {string} queryParam.operator - 操作人信息, json 格式
   *  @return {Promise}
   */
  async listGroupByOneIds(queryParam) {
    return this.invoke('listGroupByOneIds', [queryParam]);
  }
}

module.exports = ItemGroupQueryService;
