const BaseService = require('../../base/BaseService');

/**
 * AcctransService
 */
class AcctransService extends BaseService {
  /**
   * 查询账户信息
   * @param {Object} params
   * @param {string} params.userNo 商户号
   * @param {number} params.acctType 查询店铺余额为10
   */
  async getUserInfo(params) {
    return this.payInvokeV2({
      service: 'youzan.pay.acctrans.account',
      method: 'get',
      version: '1.0.0',
      data: params,
    });
  }
}

module.exports = AcctransService;
