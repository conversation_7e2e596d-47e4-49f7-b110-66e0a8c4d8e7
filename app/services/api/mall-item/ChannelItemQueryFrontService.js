const BaseService = require('../../base/BaseService');

/**
 * 外卖托管技能
 */
class ChannelItemQueryFrontService extends BaseService {
  get SERVICE_NAME() {
    return 'com.youzan.mall.item.api.channelItem.ChannelItemQueryFrontService';
  }

  /**
   *  查询外卖托管技能
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/1586537
   *
   *  @param {Object} param -
   *  @param {number} param.agentId -
   *  @param {string} param.retailSource - app版本号
   *  @param {number} param.kdtId -
   *  @param {string} param.fromApp - 请求来源
   *  @param {string} param.requestId - UUID
   *  @param {Object} param.operator - 操作人信息
   *  @return {Promise}
   */
  async queryHostAgentSkill(param) {
    return this.invoke('queryHostAgentSkill', [param]);
  }
}

module.exports = ChannelItemQueryFrontService;
