const BaseService = require('../../base/BaseService');

/**
 * 商家申诉service
 * @class MicroMallAppealService
 * @extends {BaseService}
 */
class MicroMallAppealService extends BaseService {
  /**
   * 商家申诉Service
   * @readonly
   * @memberof MicroMallAppealService
   */
  get SERVICE_NAME() {
    return 'com.youzan.ctu.open.api.service.MicroMallAppealService';
  }

  /**
   *  商家申诉入口
   *  http://zanapi.qima-inc.com/site/service/view/184737
   *  @param {object} params - 参数
   *  @param {number} params.lockTransId - 锁定id
   *  @param {number} params.time - 申诉次数
   *  @param {number} params.kdtId - 店铺id
   *  @param {number} params.userId - 用户id
   *  @return {object}
   */
  async merchantAppeal(params) {
    const data = await this.invoke('merchantAppeal', [params]);
    return data;
  }

  /**
   *  保存申诉单 分为草稿和提交2种状态
   *  http://zanapi.qima-inc.com/site/service/view/184738
   *  @param {object} microMallAppeal - 申诉信息
   *  @return {object}
   */
  async save(microMallAppeal) {
    return this.invoke('save', [microMallAppeal]);
  }

  /**
   *  提交申诉单
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/209100
   *
   *  @param {object} params - 参数
   *  @param {number} params.appealId - 申诉单id
   *  @param {number} params.kdtId - 店铺id
   *  @param {number} params.userId - 用户id
   *  @return {object}
   */
  async commit(params) {
    return this.invoke('commit', [params]);
  }

  /**
   *  撤销待审核
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/671216
   *
   *  @param {Object} baseInfo
   *  @param {number} baseInfo.kdtId - 店铺id
   *  @param {number} baseInfo.userId - 用户id
   *  @param {number} baseInfo.appealId - 申诉单id
   *  @return {Promise}
   */
  async cancel(baseInfo) {
    return this.invoke('cancel', [baseInfo]);
  }

  /**
   *  撤回申诉
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/694079
   *
   *  @param {Object} baseInfo
   *  @param {number} baseInfo.kdtId - 店铺id
   *  @param {number} baseInfo.userId - 用户id
   *  @param {number} baseInfo.appealId - 申诉单id
   *  @return {Promise}
   */
  async withdrawAppeal(baseInfo) {
    return this.invoke('withdrawAppeal', [baseInfo]);
  }

  /**
   *  获取申述时间节点
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/1148765
   *
   *  @param {number} lockTransId -
   *  @param {number} kdtId -
   *  @return {Promise}
   */
  async listAppealRecord(lockTransId, kdtId) {
    return this.invoke('listAppealRecord', [lockTransId, kdtId]);
  }
}

module.exports = MicroMallAppealService;
