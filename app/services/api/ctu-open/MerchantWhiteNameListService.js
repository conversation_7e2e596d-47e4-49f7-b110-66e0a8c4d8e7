const BaseService = require('../../base/BaseService');

/**
 * @class MerchantWhiteNameListService
 * @extends {BaseService}
 */
class MerchantWhiteNameListService extends BaseService {
  get SERVICE_NAME() {
    return 'com.youzan.ctu.open.api.service.MerchantWhiteNameListService';
  }

  /**
   *  查询白名单
   *  zanAPI文档地址: 
   *  @param {Object} query
   *  @param {string} query.type - 必填，数据类型，手机号传”phone“
   *  @param {string} query.data - 手机号
   *  @param {number} query.status - 
   *  @param {string} query.createdStartTime - 创建时间-开始-选填
   *  @param {string} query.createdEndTime - 创建时间-结束-选填
   *  @param {string} query.enableStartTime - 启用时间-开始-选填
   *  @param {string} query.enableEndTime - 启用时间-结束-选填
   *  @param {number} query.pageSize - 每页数量
   *  @param {number} query.pageNum - 页码
   *  @return {Promise}
   */
  async merchantQueryYxWhite(query) {
    return this.invoke('merchantQueryYxWhite', [query]);
  }

  /**
  *  新增白名单
  *  zanAPI文档地址: 
  *  @param {Object} query
  *  @param {number} query.kdtId -
  *  @param {number} query.type - 必填，数据类型，手机号传”phone“
  *  @param {number} query.data - 必填，名单值
  *  @return {Promise}
  */
  async merchantAddYxWhite(query) {
    return this.invoke('merchantAddYxWhite', [query]);
  }

  /**
  *  启用/禁用/删除白名单
  *  zanAPI文档地址: 
  *  @param {Object} query
  *  @param {number} query.id - 白名单ID
  *  @param {number} query.operateType - //必填，操作类型: 0:启用，1:删除，2:禁用
  *  @return {Promise}
  */
  async merchantUpdateYxWhite(query) {
    return this.invoke('merchantUpdateYxWhite', [query]);
  }
}

module.exports = MerchantWhiteNameListService;
