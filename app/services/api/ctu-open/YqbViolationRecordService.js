const BaseService = require('../../base/BaseService');

/**
 * @class YqbViolationRecordService
 * @extends {BaseService}
 */
class YqbViolationRecordService extends BaseService {
  get SERVICE_NAME() {
    return 'com.youzan.ctu.open.api.service.YqbViolationRecordService';
  }

  /**
   *  查询所有申诉状态枚举值
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/1427828
   *
   *  @return {Promise}
   */
  async queryAllAppealStatus() {
    return this.invoke('queryAllAppealStatus', []);
  }

  /**
   *  查询所有风险类型枚举值
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/1427827
   *
   *  @return {Promise}
   */
  async queryAllRiskType() {
    return this.invoke('queryAllRiskType', []);
  }

  /**
   *  查询所有处置方式枚举
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/1433305
   *  @return {Promise}
   */
  async queryAllDisposal() {
    return this.invoke('queryAllDisposal', []);
  }

  /**
   *  条件查询平安付违规列表
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/1427825
   *
   *  @param {Object} params
   *  @param {string} params.appealStatus - 申述状态
   *  @param {string} params.disposal - 处置方式
   *  @param {string} params.kdtId - 店铺id
   *  @param {string} params.orderBy - 排序方式
   *  @param {string} params.pageSize - 每页条数
   *  @param {string} params.pageNum - 页码
   *  @param {string} params.punishReason - 处置原因
   *  @param {string} params.riskType - 风险类型
   *  @param {string} params.sortField - 排序字段
   *  @param {string} params.violationAtStart - 违规事件，开始时间
   *  @param {string} params.violationAtEnd - 违规事件，截止时间
   *  @return {Promise}
   */
  async queryByCondition(params) {
    return this.invoke('queryByCondition', [params]);
  }

  /**
   * 通过id查询违规详情
   * zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/1433302
   * @param {number} kdtId - 店铺id
   * @param {number} id - 申述详情id
   * @return {Promise}
   */
  async queryDetail(kdtId, id) {
    return this.invoke('queryDetail', [kdtId, id]);
  }

  /**
   * 申诉提交
   * zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/1427830
   *
   * @param {Object} params
   * @param {number} params.kdtId - 店铺id
   * @param {string} params.id - 记录id
   * @param {string} params.appealContent - 申诉文本材料
   * @param {string} params.yqbAttachmentInfoDTOList - 申诉附件列表
   * @return {Promise}
   */
  async appealCommit(params) {
    return this.invoke('appealCommit', [params]);
  }

  /**
   * 查询案件申诉历史记录
   * zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/1427821
   * @param {number} kdtId - 店铺id
   * @param {string} id - 申诉id
   * @return {Promise}
   */
  async queryLogs(kdtId, id) {
    return this.invoke('queryLogs', [kdtId, id]);
  }

  /**
   * 查询案件申诉历史记录详情
   * zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/1427833
   * @param {number} kdtId - 店铺id
   * @param {string} id - 历史记录id
   * @return {Promise}
   */
  async queryLogDetail(kdtId, id) {
    return this.invoke('queryLogDetail', [kdtId, id]);
  }

  /**
   * 通过id查询巡检材料上报详情
   * zanAPI文档地址: https://zanapi.qima-inc.com/site/service/view/1462572
   * @param {number} kdtId - 店铺id
   * @param {number} id - 申诉详情id
   * @return {Promise}
   */
  async queryInspectionDetail(kdtId, id) {
    return this.invoke('queryInspectionDetail', [kdtId, id]);
  }

  /**
   * 提交巡检材料
   * zanAPI文档地址: https://zanapi.qima-inc.com/site/service/view/1462234
   *
   * @param {Object} params
   * @param {Object} params.commitDetailDTO - 表单
   * @return {Promise}
   */
  async inspectionAppealCommit(params) {
    return this.invoke('inspectionAppealCommit', [params], {
      timeout: 10000,
    });
  }

  /**
   *  查询所有申诉状态枚举值
   *  zanAPI文档地址: https://zanapi.qima-inc.com/site/service/view/1463150
   *
   *  @return {Promise}
   */
  async queryAllInspectionTypes() {
    return this.invoke('queryAllInspectionTypes', []);
  }

  /**
   * 通过id查询巡检材料上报历史详情
   * zanAPI文档地址: https://zanapi.qima-inc.com/site/service/view/1462573
   * @param {number} kdtId - 店铺id
   * @param {number} id - 申诉详情id
   * @return {Promise}
   */
  async queryInspectionLogDetail(kdtId, id) {
    return this.invoke('queryInspectionLogDetail', [kdtId, id]);
  }

  /**
   * 查询违规记录通知消息历史记录
   * zanAPI文档地址: https://zanapi.qima-inc.com/site/service/view/1467096
   * @param {number} kdtId - 店铺id
   * @param {string} id - 申诉id
   * @return {Promise}
   */
  async queryYqbViolationMsgHistory(kdtId, id) {
    return this.invoke('queryYqbViolationMsgHistory', [kdtId, id]);
  }
}

module.exports = YqbViolationRecordService;
