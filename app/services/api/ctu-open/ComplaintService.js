const BaseService = require('../../base/BaseService');

/**
 * @class ComplaintService
 * @extends {BaseService}
 */
class ComplaintService extends BaseService {
  get SERVICE_NAME() {
    return 'com.youzan.ctu.open.api.intellectual.service.ComplaintService';
  }

  /**
   *  当前投诉人是否在投诉黑名单
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/1164918
   *
   *  @param {number} buyUserId - 投诉人userId
   *  @return {Promise}
   */
  async isOnComplaintBlackList(buyUserId) {
    return this.invoke('isOnComplaintBlackList', [buyUserId]);
  }

  /**
   *  投诉历史记录
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/559100
   *
   *  @param {Object} param
   *  @param {number} param.pageSize - 查询分页大小
   *  @param {number} param.page - 查询页码
   *  @param {number} param.userId - 用户 id
   *  @param {string} param.status - 状态
   *  @return {Promise}
   */
  async logs(param) {
    return this.invoke('logs', [param]);
  }

  /**
   *  历史的投诉资料
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/559103
   *
   *  @param {number} userId - 用户 id
   *  @param {string} caseType - 投诉类型
   *  @param {number} pageNum - 页码
   *  @param {number} pageSize - 每页数量
   *  @return {Promise}
   */
  async complaintedMaterials(userId, caseType, pageNum, pageSize) {
    return this.invoke('complaintedMaterials', [userId, caseType, pageNum, pageSize]);
  }

  /**
   *  投诉详情
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/559101
   */
  async detail(userId, id) {
    return this.invoke('detail', [userId, id]);
  }

  /**
   *  案件投诉进度
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/559104
   */
  async progress(userId, id) {
    return this.invoke('progress', [userId, id]);
  }

  /**
   *  投诉提交
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/559098
   *
   *  @param {Object} dto
   *  @param {Array.<Object>} dto.idPhotos[] - 身份证照
   *  @param {string} dto.orgName - 企业名称/组织名称
   *  @param {string} dto.address - 地址
   *  @param {Array.<Object>} dto.materials[] - 材料
   *  @param {string} dto.memo - 补充说明
   *  @param {Array.<Object>} dto.links[] - 侵权页码链接
   *  @param {string} dto.tel - 电话
   *  @param {number} dto.userType - 投诉主体
   *  @param {string} dto.type - 投诉类型
   *  @param {string} dto.userName - 姓名
   *  @param {number} dto.userId - 用户 id
   *  @param {string} dto.email - 邮箱
   *  @param {string} dto.appletsMemo - 小程序的案件投诉说明
   *  @param {Array.<Array>} dto.appletsPictures[] - 小程序的截图
   *  @param {number} dto.complaintCarrier - 知识产权投诉案件的载体 0-链接 1-小程序
   *  @return {boolean}
   */
  async submit(dto) {
    return this.invoke('submit', [dto]);
  }

  /**
   *  撤诉
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/559102
   */
  async cancel(userId, id) {
    return this.invoke('cancel', [userId, id]);
  }

  /**
   *  检验有效的侵权链接
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/559099
   *
   *  @param {string} url - 链接地址
   *  @return {Promise}
   */
  async validLink(url) {
    return this.invoke('validLink', [url]);
  }
}

module.exports = ComplaintService;
