const BaseService = require('../../base/BaseService');

/**
 * @class LocksDubboService
 * @extends {BaseService}
 */
class LocksDubboService extends BaseService {
  get SERVICE_NAME() {
    return 'com.youzan.ctu.open.api.service.LocksDubboService';
  }

  /**
   *  根据店铺条件查询被锁记录
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/490454
   *
   *  @param {Object} locksQueryDTO
   *  @param {string} locksQueryDTO.createStartTime - 处罚开始时间
   *  @param {number} locksQueryDTO.kdtId - 店铺id
   *  @param {number} locksQueryDTO.appealStatus - 申诉状态 1=待审核 2=申述成功 3=申述失败 4=草稿 5=待申诉
   *  @param {string} locksQueryDTO.endStartTime - 处罚结束时间
   *  @param {string} locksQueryDTO.violateType - 违规类型
   *  @param {number} locksQueryDTO.pageSize - 每页数量
   *  @param {number} locksQueryDTO.pageNum - 页码
   *  @return {Promise}
   */
  async listByKtdId(locksQueryDTO) {
    return this.invoke('listByKtdId', [locksQueryDTO]);
  }

  /**
   *  根据kdtId和lockTransId查询违规详情
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/491198
   *
   *  @param {Object} locksQueryDTO
   *  @param {string} locksQueryDTO.createStartTime - 处罚开始时间
   *  @param {number} locksQueryDTO.kdtId - 店铺id
   *  @param {number} locksQueryDTO.appealStatus - 申诉状态 申诉状态 1=待审核 2=申述成功 3=申述失败 4=草稿 5=待申诉
   *  @param {string} locksQueryDTO.endStartTime - 处罚结束时间
   *  @param {string} locksQueryDTO.violateType - 违规类型
   *  @param {number} locksQueryDTO.pageSize - 每页数量
   *  @param {number} locksQueryDTO.pageNum - 页码
   *  @param {number} locksQueryDTO.lockTransId - 锁定记录transId
   *  @return {Promise}
   */
  async getLockByLockTransId(locksQueryDTO) {
    return this.invoke('getLockByLockTransId', [locksQueryDTO]);
  }

  /**
   *  根据店铺条件查询被锁记录（区分是否为预警）
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/908876
   *
   *  @param {Object} locksQueryDTO -
   *  @param {string} locksQueryDTO.createStartTime - 处罚开始时间
   *  @param {boolean} locksQueryDTO.isWarning - 处罚类型，用于区分还是预警还是其他 true 是 false 不是
   *  @param {number} locksQueryDTO.kdtId - 店铺id
   *  @param {number} locksQueryDTO.appealStatus - 申诉状态 申诉状态 1=待审核 2=申述成功 3=申述失败 4=草稿 5=待申诉 6=已解锁 -2=不予申诉 -1=无需申诉
   *  @param {string} locksQueryDTO.endStartTime - 处罚结束时间
   *  @param {string} locksQueryDTO.violateType - 违规类型
   *  @param {number} locksQueryDTO.pageSize - 每页数量
   *  @param {number} locksQueryDTO.pageNum - 页码
   *  @param {number} locksQueryDTO.lockTransId - 锁定记录transId
   *  @return {Promise}
   */
  async listByKdtId4App(locksQueryDTO) {
    return this.invoke('listByKdtId4App', [locksQueryDTO]);
  }

  /**
   *  分页查询预警单内的预警商品
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/1182662
   *
   *  @param {Object} query -
   *  @param {string} query.sortField - 排序字段
   *  @param {number} query.pageSize - 每页数
   *  @param {string} query.orderBy - 排序模式 asc-升序，desc-降序
   *  @param {number} query.pageNum - 当前页
   *  @param {number} query.lockTransId -
   *  @return {Promise}
   */
  async queryWarnItems(query) {
    return this.invoke('queryWarnItems', [query]);
  }

  /**
   *  查询当前受限处罚
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/1149729
   *
   *  @param {number} kdtId -
   *  @return {Promise}
   */
  async getLockByKdtId(kdtId) {
    return this.invoke('getLockByKdtId', [kdtId]);
  }

  /**
   *  查询商品是否还存在
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/1164858
   *
   *  @param {number} sourceId -
   *  @param {number} kdtId -
   *  @return {Promise}
   */
  async queryGoodsExist(sourceId, kdtId) {
    return this.invoke('queryGoodsExist', [sourceId, kdtId]);
  }

  /**
   *  分页查询违规单内的商品
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/1185948
   *
   *  @param {Object} query -
   *  @param {number} query.kdtId -
   *  @param {string} query.sortField - 排序字段
   *  @param {number} query.pageSize - 每页数
   *  @param {string} query.orderBy - 排序模式 asc-升序，desc-降序
   *  @param {number} query.pageNum - 当前页
   *  @param {number} query.lockTransId -
   *  @return {Promise}
   */
  async queryItems(query) {
    return this.invoke('queryItems', [query]);
  }

  /**
   *  获取违规概览
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/1277382
   *
   *  @param {number} kdtId -
   *  @return {Promise}
   */
  async getViolationOverview(kdtId) {
    return this.invoke('violationOverview', [kdtId]);
  }
}

module.exports = LocksDubboService;
