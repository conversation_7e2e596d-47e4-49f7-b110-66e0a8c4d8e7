const BaseService = require('../../base/BaseService');

/**
 * @class UploadService
 * @extends {BaseService}
 */
class UploadService extends BaseService {
  get SERVICE_NAME() {
    return 'com.youzan.ctu.open.api.service.UploadService';
  }

  /**
   *  返回token
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/757935
   *
   *  @param {string} fileName - 文件名
   *  @param {string} userId - 用户id
   *  @return {Promise}
   */
  async getToken(fileName, userId) {
    return this.invoke('getToken', [fileName, userId]);
  }

  /**
   *  返回pub token
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/757936
   *
   *  @param {string} fileName - 文件名
   *  @param {string} userId - 用户id
   *  @return {Promise}
   */
  async getPublicToken(fileName, userId) {
    return this.invoke('getPublicToken', [fileName, userId]);
  }
}

module.exports = UploadService;
