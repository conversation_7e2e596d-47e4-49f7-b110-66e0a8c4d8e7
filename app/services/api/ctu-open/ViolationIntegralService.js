const BaseService = require('../../base/BaseService');

/**
 * @class ViolationIntegralService
 * @extends {BaseService}
 */
class ViolationIntegralService extends BaseService {
  get SERVICE_NAME() {
    return 'com.youzan.ctu.open.api.service.integral.ViolationIntegralService';
  }

  /**
   *  根据店铺Id查询 积分汇总
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/1277372
   *
   *  @param {number} kdtId -
   *  @return {Promise}
   */
  async queryTotalByKdtId(kdtId) {
    return this.invoke('queryTotalByKdtId', [kdtId]);
  }
}

module.exports = ViolationIntegralService;
