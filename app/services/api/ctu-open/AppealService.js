const BaseService = require('../../base/BaseService');

/**
 * @class AppealService
 * @extends {BaseService}
 */
class AppealService extends BaseService {
  get SERVICE_NAME() {
    return 'com.youzan.ctu.open.api.intellectual.service.AppealService';
  }

  /**
   *  需要申诉案件列表
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/559091
   *
   *  @param {Object} param
   *  @param {number} param.kdtId - 店铺 id
   *  @param {number} param.pageSize - 查询分页大小
   *  @param {string} param.startTime - 投诉起始时间
   *  @param {string} param.endTime - 投诉结束时间
   *  @param {number} param.page - 查询页码
   *  @param {string} param.type - 投诉类型
   *  @param {number} param.userId - 用户 id
   *  @param {string} param.status - 投诉状态
   *  @return {Promise}
   */
  async logs(param) {
    return this.invoke('logs', [param]);
  }

  /**
   *  历史申诉材料
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/559094
   *
   *  @param {number} userId - 用户 id
   *  @param {string} caseType - 投诉类型
   *  @param {number} pageNum - 页码
   *  @param {number} pageSize - 每页数量
   *  @return {Promise}
   */
  async appealedMaterials(userId, caseType, pageNum, pageSize) {
    return this.invoke('appealedMaterials', [userId, caseType, pageNum, pageSize]);
  }

  /**
   *  申诉案件详情
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/559092
   */
  async detail(kdtId, id) {
    return this.invoke('detail', [kdtId, id]);
  }

  /**
   *  申诉进度
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/559095
   */
  async progress(kdtId, id) {
    return this.invoke('progress', [kdtId, id]);
  }

  /**
   *  申诉举证
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/559093
   */
  async submit(dto) {
    return this.invoke('submit', [dto]);
  }

  /**
   *  是否可以再次申诉
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/559096
   *
   *  @param {number} id - id
   *  @return {boolean}
   */
  async canAppeal(id) {
    return this.invoke('canAppeal', [id]);
  }

  /**
   *  获取用户店铺
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/563547
   *
   *  @param {number} userId - 用户 id
   *  @param {number} page - 页码
   *  @param {number} pageSize - 每页数量
   *  @return {Promise}
   */
  async getUserShops(userId, page, pageSize) {
    return this.invoke('getUserShops', [userId, page, pageSize]);
  }
}

module.exports = AppealService;
