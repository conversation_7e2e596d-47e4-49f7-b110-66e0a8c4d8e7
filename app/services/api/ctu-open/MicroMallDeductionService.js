const BaseService = require('../../base/BaseService');

/**
 * @class MicroMallDeductionService
 * @extends {BaseService}
 */
class MicroMallDeductionService extends BaseService {
  get SERVICE_NAME() {
    return 'com.youzan.ctu.open.api.service.MicroMallDeductionService';
  }

  /**
   *  扣分申诉列表
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/672943
   *
   *  @param {Object} query
   *  @param {string} query.violationType - 问题类型
   *  @param {number} query.deductionType - 违规类型
   *  @param {string} query.sortField - 排序字段
   *  @param {number} query.pageSize - 每页数
   *  @param {string} query.orderBy - 排序模式 asc-升序，desc-降序
   *  @param {string} query.startTime - 扣分开始事件
   *  @param {string} query.endTime - 扣分筛选结束事件
   *  @param {number} query.pageNum - 当前页
   *  @param {number} query.status - 申诉状态
   *  @return {Promise}
   */
  async searchDeductionDetail(query) {
    return this.invoke('searchDeductionDetail', [query]);
  }

  /**
   *  店铺扣分
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/671219
   *
   *  @param {number} kdtId
   *  @return {Promise}
   */
  async getKdtDeduction(kdtId) {
    return this.invoke('getKdtDeduction', [kdtId]);
  }

  /**
   *  扣分详情
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/671221
   *
   *  @param {number} id
   *  @param {number} kdtId
   *  @return {Promise}
   */
  async detail(id, kdtId) {
    return this.invoke('detail', [id, kdtId]);
  }

  /**
   *  申诉历史记录
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/671220
   *
   *  @param {number} lockTransId
   *  @return {Promise}
   */
  async listDeductionAppeal(lockTransId) {
    return this.invoke('listDeductionAppeal', [lockTransId]);
  }
}

module.exports = MicroMallDeductionService;
