const BaseService = require('../../base/BaseService');

/**
 * 企微关联
 * @class CorpCoreKVService
 * @extends {BaseService}
 */
class CorpCoreKVService extends BaseService {
  SERVICE_NAME = 'com.youzan.wecom.helper.core.api.corp.CorpCoreKVService';

  /**
   *  KV查询关联的店铺
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/1281957
   *
   *  @param {Object} request - 查询
   *  @param {number} request.yzKdtid - 查询店铺的yzKdtid
   *  @return {Promise}
   */
  async queryBoundShop(request) {
    return this.invoke('queryBoundShop', [request], { timeout: 300 });
  }

  /**
   * 根据微信商城ID查询企业微信的KDTID
   * @param {Object} params - 查询参数
   * @param {number} params.wechatKdtId - 微信商城ID
   * @return {Promise}
   */
  async getWecomKdtIdByWechatMallId(params) {
    return this.invoke('getWecomKdtIdByWechatMallId', [params]);
  }
}

module.exports = CorpCoreKVService;
