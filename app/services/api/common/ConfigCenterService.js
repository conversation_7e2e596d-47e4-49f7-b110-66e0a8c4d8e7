const BaseService = require('../../base/BaseService');

/**
 * 配置中心Service
 * @class ConfigCenterService
 * @extends {BaseService}
 */
class ConfigCenterService extends BaseService {
  /**
   * 通过配置的key获取配置信息
   * @param {*} key
   * @return {Object} 配置中心配置的notice信息
   * @memberof ConfigCenterService
   */
  async get(key) {
    const result = await this.apiCall({
      url: '/common/configcenter/get',
      data: {
        key,
      },
    });

    return this.toCamel(result);
  }
}

module.exports = ConfigCenterService;
