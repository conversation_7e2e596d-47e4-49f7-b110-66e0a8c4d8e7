const BaseService = require('../../base/BaseService');

/**
 * com.youzan.shopcenter.shop.service.ShopBaseWriteService
 */
class ShopBaseWriteService extends BaseService {
  get SERVICE_NAME() {
    return 'com.youzan.shopcenter.shop.service.ShopBaseWriteService';
  }

  /**
   *
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/67886
   *
   *  @param {Object} request -
   *  @param {string} request.contactMobile -
   *  @param {number} request.__isset_bitfield -
   *  @param {string} request.business -
   *  @param {number} request.kdtId -
   *  @param {string} request.contactName -
   *  @param {string} request.countryCode -
   *  @param {string} request.intro -
   *  @param {string} request.shopName -
   *  @param {string} request.logo -
   *  @param {string} request.contactQQ -
   *  @param {number} accountId -
   *  @return {Promise}
   */
  async updateShopBaseInfo(request, accountId) {
    return this.invoke('updateShopBaseInfo', [request, accountId], {
      processCb: response => {
        return {
          code: 200,
          data: response,
        };
      },
    });
  }
}

module.exports = ShopBaseWriteService;
