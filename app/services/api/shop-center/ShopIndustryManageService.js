const BaseService = require('../../base/BaseService');

/**
 * 行业解决方案
 * @class ShopIndustryManageService
 * @extends {BaseService}
 */
class ShopIndustryManageService extends BaseService {
  get SERVICE_NAME() {
    return 'com.youzan.shopcenter.shopfront.api.service.shop.ShopIndustryManageService';
  }
  /**
   * 查询行业标
   * http://zanapi.qima-inc.com/site/service/view/1350970
   *  @param {number} kdtId -
   *  @return {Promise}
   */
  async queryShopIndustry(kdtId) {
    const result = await this.invoke('queryShopIndustry', [kdtId]);
    return result;
  }
}

module.exports = ShopIndustryManageService;
