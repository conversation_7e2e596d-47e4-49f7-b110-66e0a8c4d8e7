const BaseService = require('../../base/BaseService');

/**
 * 店铺营业时间查询服务
 *
 * https://doc.qima-inc.com/pages/viewpage.action?pageId=96176024
 */
class ShopBusinessHoursReadOuterService extends BaseService {
  get SERVICE_NAME() {
    return 'com.youzan.shopcenter.outer.service.businesshours.ShopBusinessHoursReadOuterService';
  }

  /**
   * 根据店铺 id 查询店铺的营业时间设置
   * @param {number} kdtId 店铺 id
   */
  async queryShopBusinessHours(kdtId) {
    return this.invoke('queryShopBusinessHours', [kdtId]);
  }
}

module.exports = ShopBusinessHoursReadOuterService;
