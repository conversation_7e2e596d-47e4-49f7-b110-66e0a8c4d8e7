const BaseService = require('../../base/BaseService');

class OperationRecordService extends BaseService {
  get SERVICE_NAME() {
    return 'com.youzan.shopcenter.shopfront.api.service.operate.OperationRecordService';
  }

  /**
   *  创建操作记录
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/1354314
   *
   *  @param {Object} request -
   *  @param {number} request.kdtId -
   *  @param {string} request.operateObjectId - 操作的对象标识
   *  @param {string} request.operationType - 操作类型
   *  @param {number} request.operatorId -
   *  @return {Promise}
   */
  async createRecord(request) {
    return this.invoke('createRecord', [request]);
  }

  /**
   *  查询操作记录
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/1354315
   *
   *  @param {Object} request -
   *  @param {number} request.kdtId -
   *  @param {string} request.operateObjectId - 操作的数据标识
   *  @param {number} request.pageSize -
   *  @param {string} request.operationType - 操作类型
   *  @param {number} request.page -
   *  @return {Promise}
   */
  async listRecords(request) {
    return this.invoke('listRecords', [request]);
  }
}

module.exports = OperationRecordService;
