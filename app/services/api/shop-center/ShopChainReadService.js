const BaseNovaService = require('../../base/NovaBaseService');
/**
 * 网店获取总店信息
 */
class ShopChainReadService extends BaseNovaService {
  get SERVICE_NAME() {
    return 'com.youzan.shopcenter.shop.service.chain.ShopChainReadService';
  }

  /**
   *
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/67813
   *
   *  @param {number} kdtId -
   *  @return {Promise}
   */
  async queryShopNodeInfo(kdtId) {
    return this.invoke('queryShopNodeInfo', [kdtId], { key: 'shopNodeInfo' });
  }
}

module.exports = ShopChainReadService;
