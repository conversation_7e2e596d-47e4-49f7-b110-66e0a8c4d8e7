const BaseService = require('../../base/BaseService');

/**
 * AbilityReadService
 */
class AbilityReadService extends BaseService {
  /**
   * @return {string} SERVICE_NAME
   */
  get SERVICE_NAME() {
    return 'com.youzan.shopcenter.shopprod.api.service.ability.AbilityReadService';
  }

  /**
   * 查询店铺能力信息
   * @param {number} kdtId 店铺id
   * @param {string} abilityCode 能力编码
   * @return {Promise}
   */
  queryShopAbilityInfo(kdtId, abilityCode) {
    return this.invoke('queryShopAbilityInfo', [kdtId, abilityCode]);
  }
}

module.exports = AbilityReadService;
