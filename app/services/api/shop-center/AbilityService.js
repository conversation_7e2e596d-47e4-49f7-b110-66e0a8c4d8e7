const BaseService = require('../../base/BaseService');

/**
 * 查询店铺能力
 * @class AbilityService
 * @extends {BaseService}
 */
class AbilityService extends BaseService {
  // 查询店铺能力新服务，老服务会多走一次shopcenter
  get SERVICE_NAME() {
    return 'com.youzan.shopcenter.shopprod.api.service.ability.AbilityReadService';
  }

  /**
   * 查询店铺能力信息
   * @param {number} kdtId 店铺id
   * @param {string} abilityCode 能力编码
   * @return {Promise}
   *
   * 文档: https://doc.qima-inc.com/pages/viewpage.action?pageId=46094595
   * http://zanapi.qima-inc.com/site/service/view/127865
   */
  async queryShopAbilityInfo(kdtId, abilityCode) {
    const result = await this.invoke('queryShopAbilityInfo', [kdtId, abilityCode]);
    return result;
  }
}

module.exports = AbilityService;
