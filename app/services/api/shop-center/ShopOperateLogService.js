const BaseService = require('../../base/BaseService');

/**
 * 查询操作日志（wsc、retail通用）
 * @class ShopOperateLogService
 * @extends {BaseService}
 */
class ShopOperateLogService extends BaseService {
  get SERVICE_NAME() {
    return 'com.youzan.shopcenter.shopfront.api.shop.operate.log.service.ShopOperateLogService';
  }
  /**
   * 获取操作模块
   * @param {string} type
   * 文档: http://zanapi.qima-inc.com/site/service/view/469946
   */
  async getBizModuleList(type) {
    const result = await this.invoke('getBizModuleList', [type]);
    return result;
  }
  /**
   * 获取操作日志
   * @param {object} params
   * 文档: http://zanapi.qima-inc.com/site/service/view/472314
   */
  async queryLogList(params) {
    // eslint-disable-next-line no-console
    console.log('queryLogList', params);
    const result = await this.invoke('queryLogList', [params]);
    return result;
  }
}

module.exports = ShopOperateLogService;
