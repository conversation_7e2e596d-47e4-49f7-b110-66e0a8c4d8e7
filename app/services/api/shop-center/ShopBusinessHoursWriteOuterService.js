const BaseService = require('../../base/BaseService');

/**
 * 店铺营业时间更新服务
 *
 * https://doc.qima-inc.com/pages/viewpage.action?pageId=96176024
 */
class ShopBusinessHoursWriteOuterService extends BaseService {
  get SERVICE_NAME() {
    return 'com.youzan.shopcenter.outer.service.businesshours.ShopBusinessHoursWriteOuterService';
  }

  /**
   *  设置店铺营业时间
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/244750
   *
   *  @param {Object} params 营业时间设置
   *  @param {number} params.mode - 营业设置 1使用暂停营业设置； 2使用营业设置
   *  @param {number} params.kdtId - 店铺kdtId
   *  @param {string} params.appName - 调shop-center应用名称(调用方在ops平台登记的应用名)
   *  @param {string} params.entryAppName - 入口appName(调用方在ops平台登记的应用名)
   *  @param {string} params.requestId - 请求id
   *  @param {string} params.ipAddress - 操作入口来源ip地址
   *  @param {Object} params.suspendSetting - 暂停营业设置，仅当mode=1时用
   *  @param {number} params.operatorType - 操作人账号类型：1 卖家，2 内部员工cas id，3 未知，4 系统行为（如监听NSQ并主动修改数据）
   *  @param {Object} params.businessHoursSetting - 营业设置，仅当mode=2时用
   *  @param {number} params.operatorId - 操作人账号id
   *  @return {Object}
   */
  async setShopBusinessHours(params) {
    return this.invoke('setShopBusinessHours', [params]);
  }
}

module.exports = ShopBusinessHoursWriteOuterService;
