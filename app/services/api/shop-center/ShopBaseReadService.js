const NovaBaseService = require('../../base/NovaBaseService');
/**
 * 店铺信息
 * @class ShopBaseReadService
 */
class ShopBaseReadService extends NovaBaseService {
  get SERVICE_NAME() {
    return 'com.youzan.shopcenter.shop.service.ShopBaseReadService';
  }

  /**
   *
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/8988
   *
   *  @param {number} kdtId -
   *  @return {Promise}
   */
  async getShopBaseInfoByKdtId(kdtId) {
    return this.invoke('getShopBaseInfoByKdtId', [kdtId], { key: 'shopBaseInfo' });
  }

  /**
   *
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/9006
   *
   *  @param {number} kdtId -
   *  @return {Promise}
   */
  async queryShopDisplayInfo(kdtId) {
    return this.invoke('queryShopDisplayInfo', [kdtId], { key: 'shopDisplayInfo' });
  }
}

module.exports = ShopBaseReadService;
