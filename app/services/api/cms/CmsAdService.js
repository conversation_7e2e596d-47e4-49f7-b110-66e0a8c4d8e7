const BaseService = require('../../base/BaseService');

/**
 * cms广告配置信息Service
 * @class CmsAdService
 * @extends {BaseService}
 */
class CmsAdService extends BaseService {
  get SERVICE_NAME() {
    return 'com.youzan.ebiz.mall.gemini.api.service.cms.CmsAdService';
  }

  /**
   *  搜索广告
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/494819
   *
   *  @param {Object} cmsAdPageQueryRequest -
   *  @param {number} cmsAdPageQueryRequest.materialGroupId - 素材组ID
   *  @param {string} cmsAdPageQueryRequest.orderBy -
   *  @param {number} cmsAdPageQueryRequest.pageSize - 页数 默认20
   *  @param {number} cmsAdPageQueryRequest.page - 页码 默认1
   *  @param {string} cmsAdPageQueryRequest.title - 广告名称
   *  @param {string} cmsAdPageQueryRequest.order -
   *  @return {Promise}
   */
  async search(cmsAdPageQueryRequest) {
    return this.invoke('search', [cmsAdPageQueryRequest]);
  }
}

module.exports = CmsAdService;
