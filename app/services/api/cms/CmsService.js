const BaseService = require('../../base/BaseService');

/**
 * cms配置信息Service
 * @class CmsService
 * @extends {BaseService}
 */
class CmsService extends BaseService {
  /**
   * 获取最新的产品提醒
   * @return {Object}
   * @memberof CmsService
   */
  async getLastProduceNotice() {
    const result = await this.apiCall({
      url: '/cms/subject/getLastProduceNotice',
    });
    return result;
  }

  /**
   * 通过配置的code获取cms信息
   * @param {string} code
   * @return {*}
   * @memberof CmsService
   */
  async getAllBySectionCode(code) {
    const result = await this.apiCall({
      url: '/cms/subject/getAllBySectionCode',
      data: {
        code,
      },
    });

    return this.toCamel(result);
  }
}

module.exports = CmsService;
