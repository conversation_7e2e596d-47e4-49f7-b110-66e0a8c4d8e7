const BaseService = require('../../base/BaseService');

/**
 * 微商城连锁概况页数据概览Service
 * @class WscOrderService
 * @extends {BaseService}
 */
class RealTimeService extends BaseService {
  /**
   * 概况页数据概览Service
   * @readonly
   * @memberof WscOrderService
   */
  get SERVICE_NAME() {
    return 'com.youzan.bigdata.datacenter.proxy.api.service.realtime.RealTimeService';
  }

  /**
   * 数据概览--实时概况
   * http://zanapi.qima-inc.com/site/service/view/436742
   * @param {Object} param
   * @return {Object}
   * @memberof WscOrderService
   */
  async getRealTimeOverview(param) {
    const result = await this.invoke('getRealTimeOverview', [param]);
    return result;
  }
}

module.exports = RealTimeService;
