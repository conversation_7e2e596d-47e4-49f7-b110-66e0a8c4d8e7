const BaseService = require('../../base/BaseService');

/**
 * 概况页数据概览Service
 * @class WscOrderService
 * @extends {BaseService}
 */
class WscOrderService extends BaseService {
  /**
   * 概况页数据概览Service
   * @readonly
   * @memberof WscOrderService
   */
  get SERVICE_NAME() {
    return 'com.youzan.bigdata.datacenter.wsc.api.service.WscDashboardService';
  }

  /**
   * 获取概况页数据统计
   * https://doc.qima-inc.com/pages/viewpage.action?pageId=57102250
   * @param {Object} param
   * @return {Object}
   * @memberof WscOrderService
   */
  async getWscDashboard(param) {
    const result = await this.invoke('getWscDashboard', [param]);
    return result;
  }

  /**
   * 获取店铺年度账单
   * @see {http://zanapi.qima-inc.com/site/service/view/327215}
   * @param {string} password
   * @memberof WscOrderService
   */
  async getBill(password) {
    const result = await this.invoke('getWscBillYear', [{ password }]);
    return result;
  }
}

module.exports = WscOrderService;
