const BaseService = require('../../base/BaseService');

/**
 * TrustService
 */
class TrustService extends BaseService {
  /**
   * domain name
   */
  get DOMAIN_NAME() {
    return 'trust';
  }

  /**
   * 获取最近事件
   * @param {object} params
   * @param {string} params.bizTypes bizType string split
   */
  async getRecentAction(params = {}) {
    return this.ajax({
      url: '/trust/getRecentActions',
      method: 'POST',
      contentType: 'application/json',
      data: params, // spring boot 对于post请求请求体不能为空，后端暂未处理
    });
  }

  /**
   * 获取历史记录
   * @param {Object} params
   * @param {string} params.month
   * @param {number} params.kdtId
   * @param {number} params.appId
   */
  async getHistoryEvents(params) {
    return this.ajax({
      url: '/trust/getHistoryEvents',
      method: 'POST',
      contentType: 'application/json',
      data: params,
    });
  }

  /**
   * 获取赔付激励
   * @param {Object} params
   * @param {string} params.year
   * @param {number} params.kdtId
   * @param {number} params.appId
   */
  async getCompensateRecords(params) {
    return this.ajax({
      url: '/trust/getCompensateRecords',
      method: 'POST',
      contentType: 'application/json',
      data: params,
    });
  }

  /**
   * 获取安全护航所有状态
   * @param {Object} params
   */
  async getSafeEscort(params) {
    return this.ajax({
      url: '/trust/statistics',
      method: 'POST',
      contentType: 'application/json',
      data: params,
    });
  }
}

module.exports = TrustService;
