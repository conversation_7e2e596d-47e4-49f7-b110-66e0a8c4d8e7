const BaseService = require('../../base/BaseService');

/**
 * com.youzan.sam.gateway.api.service.staff.StaffService
 */
class StaffService extends BaseService {
  get SERVICE_NAME() {
    return 'com.youzan.sam.gateway.api.service.staff.StaffService';
  }

  /**
   *  批量调用findStaffDeleteHooks方法,用于批量删除员工前查询是否有活动通知人关系
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/1011687
   *
   *  @param {Object} request -
   *  @param {string} request.biz - 业务类型
   *  @param {Array.<Array>} request.adminIds[] - 有赞账号id
   *  @param {Array} request.adminIds[] -
   *  @param {number} request.kdtId -
   *  @param {string} request.source - 请求来源
   *  @param {number} request.operatorId - 操作人id
   *  @param {string} request.operator -
   *  @return {Promise}
   */
  async listStaffDeleteHooks(request) {
    return this.invoke('listStaffDeleteHooks', [request]);
  }
}

module.exports = StaffService;
