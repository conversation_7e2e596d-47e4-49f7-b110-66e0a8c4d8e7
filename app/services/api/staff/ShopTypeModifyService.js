const BaseService = require('../../base/BaseService');

/**
 *
 *
 * @class ShopTypeModifyService
 * @extends {BaseService}
 */
class ShopTypeModifyService extends BaseService {
  get SERVICE_NAME() {
    return 'com.youzan.shopcenter.shop.service.ShopTypeModifyService';
  }

  /**
   *
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/68018
   *
   *  @param {number} kdtId - 店铺kdtId
   *  @return {Promise}
   */
  async queryLatestModifiedTime4Retail(kdtId) {
    return await this.invoke('queryLatestModifiedTime4Retail', [kdtId]);
  }
}

module.exports = ShopTypeModifyService;
