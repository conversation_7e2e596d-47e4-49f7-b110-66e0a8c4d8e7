const BaseService = require('../../base/BaseService');

/**
 * @class SingleManageService
 * @extends {BaseService}
 */
class SingleManageService extends BaseService {
  get SERVICE_NAME() {
    return 'com.youzan.staff.core.api.v2.manage.SingleManageService';
  }

  /**
   *  http://zanapi.qima-inc.com/site/service/view/940774
   *
   *  @param {object} param - 参数
   *  @return {Promise}
   */
  async getShopKeeper(param) {
    return await this.invoke('getShopKeeper', [param]);
  }
}

module.exports = SingleManageService;
