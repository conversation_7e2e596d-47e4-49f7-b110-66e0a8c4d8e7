const BaseService = require('../../base/BaseService');

/**
 *
 *
 * @class ChainRoleService
 * @extends {BaseService}
 */
class ChainRoleService extends BaseService {
  get SERVICE_NAME() {
    return 'com.youzan.sam.gateway.api.service.perm.ChainRoleService';
  }

  /**
   *  获取角色列表
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/415890
   *  @param {Object} param -
   *  @param {number} param.kdtId 店铺kdtId
   *  @param {boolean} param.withStaffCount
   *
   *  @return {Promise}
   */
  async getRoleList(param) {
    return await this.invoke('getRoleList', [param]);
  }

  /**
   *  获取自定义角色的权限树
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/415887
   *  @param {Object} param -
   *  @param {number} param.kdtId 店铺kdtId
   *  @param {boolean} param.roleId
   *  @param {boolean} param.roleType
   *
   *  @return {Promise}
   */
  async getRolePerm(param) {
    return await this.invoke('getRolePerm', [param]);
  }

  /**
   *  新增 更新自定义角色
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/415889
   *  @param {Object} param -
   *  @param {number} param.kdtId 店铺kdtId
   *  @param {number} param.operatorId
   *  @param {number} param.roleId
   *  @param {number} param.roleType
   *  @param {number} param.shopRole
   *
   *  @return {Promise}
   */
  async addCustomizeRole(param) {
    return await this.invoke('addCustomizeRole', [param]);
  }

  /**
   *  删除自定义角色
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/415888
   *  @param {Object} param -
   *  @param {number} param.kdtId 店铺kdtId
   *  @param {number} param.roleId
   *
   *  @return {Promise}
   */
  async deleteCustomRole(param) {
    return await this.invoke('deleteCustomRole', [param]);
  }
}

module.exports = ChainRoleService;
