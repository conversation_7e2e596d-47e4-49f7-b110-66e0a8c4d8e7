const BaseService = require('../../base/BaseService');
/** com.youzan.staff.core.api.v2.service.StaffQueryOuterService -  */
class StaffQueryOuterService extends BaseService {
  get SERVICE_NAME() {
    return 'com.youzan.staff.core.api.v2.service.StaffQueryOuterService';
  }

  /**
   *  获取用户信息
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/943298
   *
   *  @param {Object} chainQueryManageParamDTO -
   *  @param {string} chainQueryManageParamDTO.mode - 店铺模式，标识店铺查询范围（CHAIN_SHOP总部内查询员工所有所属店铺、SELF_SHOP查询kdtId对应的店铺员工）
   *  @param {boolean} chainQueryManageParamDTO.withDelete - 是否返回已删除员工-默认不返回
   *  @param {Array.<Array>} chainQueryManageParamDTO.roleIds[] - 查询符合角色的员工，角色之间为or关系
   *  @param {number} chainQueryManageParamDTO.kdtId - 店铺id
   *  @param {number} chainQueryManageParamDTO.pageNo -
   *  @param {number} chainQueryManageParamDTO.adminId -
   *  @param {number} chainQueryManageParamDTO.pageSize -
   *  @param {boolean} chainQueryManageParamDTO.withShadowAdmin - 是否返回隐形管理员-默认不返回
   *  @return {Promise}
   */
  async getStaffDetailInfo(chainQueryManageParamDTO) {
    return this.invoke('getStaffDetailInfo', [chainQueryManageParamDTO]);
  }

  /**
   *  按店铺维度查询员工信息
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/969037
   *
   *  @param {Object} queryShopStaffInfoParamDTO -
   *  @param {boolean} queryShopStaffInfoParamDTO.withRole - 是否返回角色信息
   *  @param {string} queryShopStaffInfoParamDTO.mode - 店铺模式，标识店铺查询范围（CHAIN_SHOP总部内查询员工所有所属店铺、SELF_SHOP查询kdtId对应的店铺员工）
   *  @param {boolean} queryShopStaffInfoParamDTO.withDelete - 是否返回已删除员工-默认不返回
   *  @param {Array.<Array>} queryShopStaffInfoParamDTO.roleIds[] - 查询符合角色的员工，角色之间为or关系
   *  @param {number} queryShopStaffInfoParamDTO.deptIdV2 - 部门id
   *  @param {number} queryShopStaffInfoParamDTO.kdtId - 店铺id
   *  @param {Array.<Array>} queryShopStaffInfoParamDTO.adminIds[] - 符合的员工adminIds
   *  @param {number} queryShopStaffInfoParamDTO.pageNo -
   *  @param {number} queryShopStaffInfoParamDTO.pageSize -
   *  @param {boolean} queryShopStaffInfoParamDTO.withDeptTree - 是否根据部门树筛选员工
   *  @param {boolean} queryShopStaffInfoParamDTO.withShadowAdmin - 是否返回隐形管理员-默认不返回
   *  @return {Promise}
   */
  async batchShopStaffInfo(queryShopStaffInfoParamDTO) {
    return this.invoke('batchShopStaffInfo', [queryShopStaffInfoParamDTO]);
  }

  /**
             *  查询员工归属店铺的员工基础信息（员工1 对 店铺1）
*  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/943296 
*
             *  @param {Object} staffBaseParamDTO - 
             *  @param {string} staffBaseParamDTO.mode - 店铺模式，标识店铺查询范围（CHAIN_SHOP总部内查询员工所有所属店铺、SELF_SHOP查询kdtId对应的店铺员工）
             *  @param {boolean} staffBaseParamDTO.withDelete - 是否需要返回删除的员工，默认false不返回
             *  @param {Array.<Array>} staffBaseParamDTO.statusList[] - 查询相应状态员工  status or status
             *  @param {Array} staffBaseParamDTO.statusList[] - 
             *  @param {number} staffBaseParamDTO.kdtId - 店铺id
             *  @param {number} staffBaseParamDTO.adminId - 有赞账号id
             *  @param {string} staffBaseParamDTO.platformTenant - 租户标识（queryStaffBaseInfoByKdtId支持rpc缓存，rpc缓存不支持租户标隔离，新增带缓存接口，此字段不向下传递，内部业务处理任从rpc上线文）
 带缓存接口（queryStaffBaseInfoByKdtIdWithCache）必传此参数，否则YZ租户与SCRM租户数据互查缓存
 用以区分
             *  @param {boolean} staffBaseParamDTO.withShadowAdmin - 是否包含隐形管理员，默认false不包含
             *  @return {Promise}
             */
  async queryStaffBaseInfoByKdtId(staffBaseParamDTO) {
    return this.invoke('queryStaffBaseInfoByKdtId', [staffBaseParamDTO]);
  }
}

module.exports = StaffQueryOuterService;
