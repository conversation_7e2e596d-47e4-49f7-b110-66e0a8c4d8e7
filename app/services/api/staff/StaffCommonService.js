const BaseService = require('../../base/BaseService');

/**
 *
 *
 * @class StaffCommonService
 * @extends {BaseService}
 */
class StaffCommonService extends BaseService {
  get SERVICE_NAME() {
    return 'com.youzan.sam.gateway.api.service.staff.StaffCommonService';
  }

  /**
   *  获取店铺能力
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/431531
   *  @param {Object} param -
   *  @param {number} param.kdtId 店铺kdtId
   *  @return {Promise}
   */
  async getStaffAbility(param) {
    return this.invoke('getStaffAbility', [param]);
  }

  /**
   *  分页获取店铺能力
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/1015202
   *
   *  @param {Object} pageStaffAbilityRequest -
   *  @param {string} pageStaffAbilityRequest.biz - 业务类型
   *  @param {number} pageStaffAbilityRequest.kdtId -
   *  @param {number} pageStaffAbilityRequest.pageNo -
   *  @param {number} pageStaffAbilityRequest.adminId - 有赞账号id
   *  @param {number} pageStaffAbilityRequest.pageSize -
   *  @param {string} pageStaffAbilityRequest.source - 请求来源
   *  @param {number} pageStaffAbilityRequest.operatorId - 操作人id
   *  @param {string} pageStaffAbilityRequest.operator -
   *  @return {Promise}
   */
  async pageStaffAbility(pageStaffAbilityRequest) {
    return this.invoke('pageStaffAbility', [pageStaffAbilityRequest]);
  }
}

module.exports = StaffCommonService;
