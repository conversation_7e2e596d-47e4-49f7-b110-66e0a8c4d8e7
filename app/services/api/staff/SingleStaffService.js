/* eslint-disable */
const BaseService = require('../../base/BaseService');

/**
 *
 *
 * @class SingleStaffService
 * @extends {BaseService}
 */
class SingleStaffService extends BaseService {
  get SERVICE_NAME() {
    return 'com.youzan.sam.gateway.api.service.staff.SingleStaffService';
  }

  /**
   *  分页获取员工
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/416265
   *  @param {Object} param -
   *  @param {number} param.kdtId 店铺kdtId
   *  @param {number} param.pageNo 页码
   *  @param {number} param.pageSize 每页大小(不大于200)
   *  @param {string} param.keyword 关键词查询
   *  @param {Array} param.roleIds 角色查询
   *  @param {number} param.status 状态查询
   *
   *  @return {Promise}
   */
  async find(param) {
    return await this.invoke('find', [param]);
  }

  /**
   *  获取免费角色
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/419628
   *  @param {Object} param -
   *  @param {number} param.kdtId 店铺kdtId
   *  @return {Promise}
   */
  async getFreeRoles(param) {
    return await this.invoke('getFreeRoles', [param]);
  }

  /**
   *  启用员工
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/419626
   *  @param {Object} param -
   *  @param {number} param.kdtId 店铺kdtId
   *  @param {Array} param.adminIds 员工id
   *  @param {number} param.operatorId 操作人id
   *  @param {string} param.smsCaptcha 短信验证码
   *  @param {string} param.smsPhone 短信验证码手机号
   *  @return {Promise}
   */
  async enable(param) {
    return await this.invoke('enable', [param]);
  }

  /**
   *  停用员工
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/419627
   *  @param {Object} param -
   *  @param {number} param.kdtId 店铺kdtId
   *  @param {Array} param.adminIds 员工id
   *  @param {number} param.operatorId 操作人id
   *  @param {string} param.smsCaptcha 短信验证码
   *  @param {string} param.smsPhone 短信验证码手机号
   *  @return {Promise}
   */
  async disable(param) {
    return await this.invoke('disable', [param]);
  }

  /**
   *  删除员工
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/416264
   *  @param {Object} param -
   *  @param {number} param.kdtId 店铺kdtId
   *  @param {number} param.adminId 员工id(如果传了account,以account为准)
   *  @param {string} param.account 账号
   *  @param {number} param.operatorId 操作人id
   *  @param {string} param.smsPhone 短信验证码手机号
   *  @param {string} param.smsCaptcha 短信验证码
   *  @return {Promise}
   */
  async delete(param) {
    return await this.invoke('delete', [param]);
  }

  /**
   *  获取员工删除hook
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/419629
   *  @param {Object} param -
   *  @param {number} param.kdtId 店铺kdtId
   *  @param {number} param.adminId 员工id
   *  @return {Promise}
   */
  async findStaffDeleteHooks(param) {
    return await this.invoke('findStaffDeleteHooks', [param]);
  }

  /**
   *  更换负责人
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/419625
   *  @param {Object} param -
   *  @param {number} param.kdtId 店铺kdtId
   *  @param {number} param.operatorId 操作人id
   *  @param {string} param.name 新负责人姓名
   *  @param {string} param.account 新负责人有赞账号
   *  @param {string} param.linkPhone 新负责人联系方式
   *  @param {string} param.smsPhone 短信验证码手机号
   *  @param {string} param.smsCaptcha 短信验证码
   *  @return {Promise}
   */
  async changeShopKeeper(param) {
    return await this.invoke('changeShopKeeper', [param]);
  }

  /**
   *  获取单个员工详情
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/416266
   *
   *  @param {Object} querySingleStaffRequest -
   *  @param {number} querySingleStaffRequest.kdtId -
   *  @param {Object} querySingleStaffRequest.needExtra -
   *  @param {string} querySingleStaffRequest.staffExtType -
   *  @param {number} querySingleStaffRequest.pageSize -
   *  @param {Array.<Array>} querySingleStaffRequest.virtualRoleIds[] -
   *  @param {string} querySingleStaffRequest.source - 请求来源
   *  @param {string} querySingleStaffRequest.operator -
   *  @param {string} querySingleStaffRequest.biz - 业务类型
   *  @param {Array.<Array>} querySingleStaffRequest.roleIds[] -
   *  @param {number} querySingleStaffRequest.pageNo -
   *  @param {number} querySingleStaffRequest.adminId - 有赞账号id
   *  @param {string} querySingleStaffRequest.keyword -
   *  @param {number} querySingleStaffRequest.operatorId - 操作人id
   *  @param {string} querySingleStaffRequest.status -
   *  @return {Promise}
   */
  async get(querySingleStaffRequest) {
    return this.invoke('get', [querySingleStaffRequest]);
  }

  /**
   *  创建员工
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/416262
   *  @param {Object} param -
   *  @param {number} param.kdtId 店铺kdtId
   *  @param {number} param.operatorId 操作人id
   *  @param {string} param.name 姓名
   *  @param {string} param.account 有赞账号
   *  @param {string} param.staffNo 员工编号
   *  @param {string} param.linkPhone 联系方式
   *  @param {Array} param.roles 员工角色
   *  @param {Object} param.extraInfo 额外信息
   *  @param {string} param.staffExtType 需要返回的扩展信息
   *  @param {object} param.teacherInfo 老师信息
   *  @param {string} param.smsPhone 接收验证码的手机号
   *  @param {string} param.smsCaptcha 验证码
   */
  async create(param) {
    return await this.invoke('create', [param]);
  }

  /**
   *  更新员工
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/416263
   *  @param {Object} param -
   *  @param {number} param.kdtId 店铺kdtId
   *  @param {number} param.operatorId 操作人id
   *  @param {string} param.name 姓名
   *  @param {string} param.account 有赞账号
   *  @param {string} param.staffNo 员工编号
   *  @param {string} param.linkPhone 联系方式
   *  @param {Array} param.roles 员工角色
   *  @param {Object} param.extraInfo 额外信息
   *  @param {string} param.staffExtType 需要返回的扩展信息
   *  @param {object} param.teacherInfo 老师信息
   *  @param {string} param.smsPhone 接收验证码的手机号
   *  @param {string} param.smsCaptcha 验证码
   */
  async update(param) {
    return await this.invoke('update', [param]);
  }

  /**
   *  批量删除单店员工
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/1012537
   *
   *  @param {Object} request -
   *  @param {string} request.biz -
   *  @param {number} request.kdtId -
   *  @param {Array.<Array>} request.adminIds[] -
   *  @param {Array} request.adminIds[] -
   *  @return {Promise}
   */
  async batchDeleteAsync(request) {
    return this.invoke('batchDeleteAsync', [request]);
  }

  /**
   *  查询批量删除员工结果
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/1021412
   *
   *  @param {Object} request -
   *  @param {number} request.kdtId -
   *  @param {number} request.operatorId -
   *  @param {number} request.taskId -
   *  @return {Promise}
   */
  async queryBatchDeleteResult(request) {
    return this.invoke('queryBatchDeleteResult', [request]);
  }
}

module.exports = SingleStaffService;
