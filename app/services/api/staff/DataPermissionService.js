const BaseService = require('../../base/BaseService');

/**
 * @class DataPermissionService
 * @extends {BaseService}
 */
class DataPermissionService extends BaseService {
  get SERVICE_NAME() {
    return 'com.youzan.rig.core.api.perm.service.DataPermissionService';
  }

  /**
   *
   *  roleId 为角色 id
   *  bizCode 你传 retail_verify_record
   *  namespace 还是传 np_yz_shop
   *  scope 传 0 代表需要看到其他人的核销记录，1 代表只看自己
   *  thirdTenantId 为操作用户的 UserId
   */
  async updateScopeOfDataPermission(request) {
    return this.invoke('updateScopeOfDataPermission', [request]);
  }

  /**
   * 获取数据权限
   */
  async getScopeOfDataPermissionV2(request) {
    return this.invoke('getScopeOfDataPermissionV2', [request]);
  }
}

module.exports = DataPermissionService;
