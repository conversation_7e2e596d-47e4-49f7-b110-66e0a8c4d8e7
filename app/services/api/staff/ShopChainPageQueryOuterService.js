const BaseService = require('../../base/BaseService');

/**
 * @class ShopChainPageQueryOuterService
 * @extends {BaseService}
 */
class ShopChainPageQueryOuterService extends BaseService {
  get SERVICE_NAME() {
    return 'com.youzan.shopcenter.outer.service.chain.ShopChainPageQueryOuterService';
  }

  /**
   *  分页查询某店铺下所有的后代节点
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/197922
   *
   *  @param {Object} request - 店铺kdtId（总部）
   *  @param {number} request.kdtId -
   *  @param {Object} request.operator -
   *  @return {Promise}
   */
  async queryDescendentShopNodes(request) {
    return await this.invoke('queryDescendentShopNodes', [request]);
  }
}

module.exports = ShopChainPageQueryOuterService;
