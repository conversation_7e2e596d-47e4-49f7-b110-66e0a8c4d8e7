const BaseService = require('../../base/BaseService');

/**
 * @class StaffQueryService
 * @extends {BaseService}
 */
class StaffQueryService extends BaseService {
  get SERVICE_NAME() {
    return 'com.youzan.staff.core.api.staff.service.baseservice.StaffQueryService';
  }

  /**
   *  校验员工是否是店铺创建者
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/1329743 
   *  @param {Object} staffBaseParamDTO - 
   *  @param {string} staffBaseParamDTO.mode - 店铺模式，标识店铺查询范围（CHAIN_SHOP总部内查询员工所有所属店铺、SELF_SHOP查询kdtId对应的店铺员工）
   *  @param {boolean} staffBaseParamDTO.withDelete - 是否需要返回删除的员工，默认false不返回
   *  @param {Array.<Array>} staffBaseParamDTO.statusList[] - 查询相应状态员工  status or status
   *  @param {Array} staffBaseParamDTO.statusList[] - 
   *  @param {number} staffBaseParamDTO.kdtId - 店铺id
   *  @param {number} staffBaseParamDTO.adminId - 有赞账号id
   *  @param {string} staffBaseParamDTO.platformTenant - 租户标识（queryStaffBaseInfoByKdtId支持rpc缓存，rpc缓存不支持租户标隔离，新增带缓存接口，此字段不向下传递，内部业务处理任从rpc上线文）
带缓存接口（queryStaffBaseInfoByKdtIdWithCache）必传此参数，否则YZ租户与SCRM租户数据互查缓存
用以区分
   *  @param {boolean} staffBaseParamDTO.withShadowAdmin - 是否包含隐形管理员，默认false不包含
   *  @return {Promise}
   */
  async isShopKeeper(staffBaseParamDTO) {
    return this.invoke('isShopKeeper', [staffBaseParamDTO]);
  }
}

module.exports = StaffQueryService;
