const BaseService = require('../../base/BaseService');

/**
 *
 *
 * @class StaffServiceV2
 * @extends {BaseService}
 */
class StaffServiceV2 extends BaseService {
  get SERVICE_NAME() {
    return 'com.youzan.sam.service.StaffServiceV2';
  }

  /**
   *  店铺是否因为插件或者版本到期停用过全部员工
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/321498
   *  @param {Object} param -
   *  @param {number} param.kdtId 店铺kdtId
   *  @return {Promise}
   */
  async isShopDisabledAllStaffsForExpire(param) {
    return await this.invoke('isShopDisabledAllStaffsForExpire', [param]);
  }

  /**
   *  清除店铺因为插件、版本到期停用员工的记录
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/321499
   *  @param {Object} param -
   *  @param {number} param.kdtId 店铺kdtId
   *  @return {Promise}
   */
  async clearShopDisabledAllStaffsForExpireRecord(param) {
    return await this.invoke('clearShopDisabledAllStaffsForExpireRecord', [param]);
  }
}

module.exports = StaffServiceV2;
