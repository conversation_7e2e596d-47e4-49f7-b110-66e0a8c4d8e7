const BaseService = require('../../base/BaseService');

/**
 * StaffOpenService
 */
class StaffOpenService extends BaseService {
  get SERVICE_NAME() {
    return 'com.youzan.sam.thrift.service.StaffOpenService';
  }

  /**
   *  获取用户信息
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/130315
   *
   *  @param {Object} staffTTO -
   *  @param {string} staffTTO.linkPhone - 联系电话 长度：12
   *  @param {number} staffTTO.__isset_bitfield -
   *  @param {number} staffTTO.kdtId -
   *  @param {string} staffTTO.operator - 添加人
   *  @param {Object} staffTTO.staffRoleTTO -
   *  @param {string} staffTTO.biz -
   *  @param {boolean} staffTTO.pubNsq -
   *  @param {string} staffTTO.identity - 身份
   *  @param {number} staffTTO.adminId - 有赞账户id
   *  @param {string} staffTTO.name - 名称	string	长度：不大于8
   *  @param {number} staffTTO.shopId -
   *  @param {number} staffTTO.operatorId - 添加人有赞账户id
   *  @param {string} staffTTO.account - 有赞账户
   *  @param {string} staffTTO.status -
   *  @return {Promise}
   */
  async get(staffTTO) {
    return await this.invoke('get', [staffTTO]);
  }
}

StaffOpenService.rightResponseCode = 10000;

module.exports = StaffOpenService;
