const BaseService = require('../../base/BaseService');

/**
 * @class ChainManageService
 * @extends {BaseService}
 */
class ChainManageService extends BaseService {
  get SERVICE_NAME() {
    return 'com.youzan.staff.core.api.v2.manage.ChainManageService';
  }

  /**
   *
   *  http://zanapi.qima-inc.com/site/service/view/938413
   *
   *  @param {object} param - 参数
   *  @return {Promise}
   */
  async getStaffDetailInfo(param) {
    return await this.invoke('getStaffDetailInfo', [param]);
  }

  /**
   *
   *  https://zanapi.qima-inc.com/site/service/view/1264915
   *
   *  @param {object} param - 参数
   *  @return {Promise}
   */
  async findForManage(param) {
    return await this.invoke('findForManage', [param], {
      processCb: response => {
        return {
          code: 200,
          data: {
            items: response.data,
            paginator: { totalCount: response.totalCount, page: +param.pageNo },
          },
        };
      },
    });
  }
}

module.exports = ChainManageService;
