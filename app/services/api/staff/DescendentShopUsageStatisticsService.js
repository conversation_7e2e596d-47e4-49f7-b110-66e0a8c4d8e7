const BaseService = require('../../base/BaseService');

/**
 * @class DescendentShopUsageStatisticsService
 * @extends {BaseService}
 */
class DescendentShopUsageStatisticsService extends BaseService {
  get SERVICE_NAME() {
    return 'com.youzan.shopcenter.shopfront.api.service.chain.DescendentShopUsageStatisticsService';
  }

  /**
   *  查询总部创建门店校验信息
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/884549
   *
   *  @param {Object} request - 店铺kdtId（总部）
   *  @param {number} request.kdtId -
   *  @param {Object} request.operator -
   *  @return {Promise}
   */
  async queryDescendentShopUsageStatistics(request) {
    return await this.invoke('queryDescendentShopUsageStatistics', [request]);
  }
}

module.exports = DescendentShopUsageStatisticsService;
