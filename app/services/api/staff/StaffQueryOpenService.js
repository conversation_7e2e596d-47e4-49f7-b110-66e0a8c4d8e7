const BaseService = require('../../base/BaseService');

/**
 * @class StaffQueryOpenService
 * @extends {BaseService}
 */
class StaffQueryOpenService extends BaseService {
  get SERVICE_NAME() {
    return 'com.youzan.staff.core.api.staff.service.application.open.StaffQueryOpenService';
  }

  /**
             *  分页查询店铺下的员工，可以模糊搜索员工名称和账号。pageSize不建议超过50
 总部店铺返回总部下的所有员工，合伙人/门店/单店返回店铺自身下的员工。
*  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/1407806 
*
             *  @param {Object} PageQueryShopStaffParamDTO - 
             *  @param {number} PageQueryShopStaffParamDTO.kdtId - 
             *  @param {number} PageQueryShopStaffParamDTO.pageNo - 
             *  @param {number} PageQueryShopStaffParamDTO.pageSize - 
             *  @param {string} PageQueryShopStaffParamDTO.source - 调用来源
 注意：shop-center-create 标识特定店铺创建来源请求，员工默认设置为店铺负责人，其他业务方禁止使用此标识
 video-initial 表示视频号初始化店铺负责人，会删除原有的虚拟负责人，其他业务方勿用此标识
             *  @param {number} PageQueryShopStaffParamDTO.operatorKdtId - 当前操作人kdtId
 连锁总部操作分店的场景，有接口只传入了需操作的kdtId，但是某些场景（操作日志）需要使用当前操作所在店铺的信息
 所以这里支持传入当前操作人所属店铺kdtId
             *  @param {string} PageQueryShopStaffParamDTO.operatorType - 
             *  @param {string} PageQueryShopStaffParamDTO.keyword - 员工名称或员工手机号(模糊搜索)
             *  @param {number} PageQueryShopStaffParamDTO.operatorId - 
             *  @param {boolean} PageQueryShopStaffParamDTO.withDeleted - 是否包含删除的员工
             *  @param {string} PageQueryShopStaffParamDTO.operator - 
             *  @return {Promise}
             */
  async pageQueryStaffInfoByKdtId(PageQueryShopStaffParamDTO) {
    return this.invoke('pageQueryStaffInfoByKdtId', [PageQueryShopStaffParamDTO]);
  }
}

module.exports = StaffQueryOpenService;
