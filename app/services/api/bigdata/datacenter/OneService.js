const BaseService = require('../../../base/BaseService');

/** com.youzan.bigdata.datacenter.oneservice.service.OneService -  */
class OneService extends BaseService {
  get SERVICE_NAME() {
    return 'com.youzan.bigdata.datacenter.oneservice.service.OneService';
  }

  /**
   *
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/450122
   *
   *  @param {Object} request -
   *  @param {string} request.caller - 调用方名称,一般填写调用者应用名
   *  @param {string} request.apiName - 统一数据服务的api名称,全局唯一
   *  @param {string} request.requestId -
   *  @param {Object} request.params - 请求参数
   *  @param {boolean} request.debugMode - 是否开启debug模式,如果开启,将返回更多的信息
   *  @return {Promise}
   */
  async query(request) {
    return this.invoke('query', [request]);
  }
}

module.exports = OneService;
