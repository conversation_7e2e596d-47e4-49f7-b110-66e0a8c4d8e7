const BaseService = require('../../../base/BaseService');
/** com.youzan.bigdata.datacenter.wsc.api.service.customer.NewCustomerService -  */
class NewCustomerService extends BaseService {
  get SERVICE_NAME() {
    return 'com.youzan.bigdata.datacenter.wsc.api.service.customer.NewCustomerService';
  }

  /**
             *  
  *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/228780 
  *
             *  @param {Object} param - 
             *  @param {number} param.canalType - {@link com.youzan.bigdata.datacenter.base.api.constance.CanalTypeEnum}
  店铺类型 10：全店；0：网店；1：门店
             *  @param {Object} param.timeParam - 时间参数
             *  @param {number} param.dateType - {@link com.youzan.bigdata.datacenter.base.api.constance.DateTypeEnum}
  日期类型
  REAL_TIME(0, "今日实时"),
  NATURAL_DAY(1, "自然天"),
  NATURAL_WEEK(2, "自然周"),
  NATURAL_MONTH(3, "自然月"),
  LAST_SEVEN_DAY(4, "最近7天"),
  LAST_THIRTY_DAY(5, "最近30天"),
  SELF_DEFINE(6, "自定义"),
  NATURAL_QUARTER(7, "自然季度"),
  NATURAL_DAY_WITH_HOUR(8, "自然天 按照小时统计")
  *  @param {number} param.kdtId - 店铺id
  *  @param {string} param.fromApp - 请求来源
  *  @param {string} param.requestId - UUID
  *  @param {number} param.pageSize - 分页信息
  *  @param {number} param.page - 分页信息
  *  @param {string} param.operator - 操作人信息
  *   json 格式 ['user_id' => $userId, 'nick_name' => $nickName,
  *  'client_ip' => $clientIp, 'client_id': $clientId, 'app_id'=>$appId, 'client_name'=>$clientName]
  *  @return {Promise}
  */
  async getCustomerStatistic(param) {
    return this.invoke('getCustomerStatistic', [param]);
  }
}

module.exports = NewCustomerService;
