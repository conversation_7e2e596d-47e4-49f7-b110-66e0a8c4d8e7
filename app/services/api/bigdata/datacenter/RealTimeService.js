const BaseService = require('../../../base/BaseService');

/** com.youzan.bigdata.datacenter.base.api.service.realtime.RealTimeService -  */
class RealTimeService extends BaseService {
  get SERVICE_NAME() {
    return 'com.youzan.bigdata.datacenter.base.api.service.realtime.RealTimeService';
  }

  /**
  *  支持微商城，零售 连锁-实时概况数据
  *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/421790 
  *
  *  @param {Object} basicParam - 
  *  @param {number} basicParam.canalType - 线上线下： 0 线上；1 线下；10 全部
  *  @param {string} basicParam.tagId - 分组id
  *  @param {string} basicParam.fromApp - 请求来源
  *  @param {number} basicParam.hqKdtId - 总店
  *  @param {number} basicParam.pageSize - 分页参数
  *  @param {string} basicParam.operator - 操作人信息, json 格式 
  * ['user_id' => $userId, 'nick_name' => $nickName, 'client_ip' => $clientIp, 
  * 'client_id': $clientId, 'app_id'=>$appId, 'client_name'=>$clientName]
  *  @param {string} basicParam.kdtIds - 店铺列表
  *  @param {number} basicParam.teamLevel - 店铺等级：0 - 总店或者管理单元; 1 - 子店;
  *  @param {number} basicParam.currentDay - 不填则为今日实时
  format: yyyyMMdd
  *  @param {string} basicParam.sortType - 'asc' 'desc'
  *  @param {string} basicParam.requestId - UUID
  *  @param {string} basicParam.sortBy - 排序字段
  *  @param {number} basicParam.page - 分页参数
  *  @param {number} basicParam.categoryId - 分类id
  *  @return {Promise}
  */
  async getRealTimeOverView(basicParam) {
    return this.invoke('getRealTimeOverView', [basicParam]);
  }
}

module.exports = RealTimeService;
