const BaseService = require('../../../base/BaseService');
/** com.youzan.bigdata.datacenter.base.api.service.ebiz.SaleExpectedTargetService -  */
class SaleExpectedTargetService extends BaseService {
  get SERVICE_NAME() {
    return 'com.youzan.bigdata.datacenter.base.api.service.ebiz.SaleExpectedTargetService';
  }

  /**
   *
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/380298
   *
   *  @param {Object} param -
   *  @param {number} param.bizType - 业务类型
   *  @param {number} param.kdtId - 店铺id
   *  @param {string} param.fromApp - 请求来源
   *  @param {string} param.requestId - UUID
   *  @param {number} param.saleYear - 年份
   *  @param {string} param.operator - 操作人信息,
   *  json 格式 ['user_id' => $userId,
   *  'nick_name' => $nickName,
   *  'client_ip' => $clientIp,
   *  'client_id': $clientId, 'app_id'=>$appId, 'client_name'=>$clientName]
   *  @return {Promise}
   */
  async listSaleExpectedTargets(param) {
    return this.invoke('listSaleExpectedTargets', [param]);
  }
}

module.exports = SaleExpectedTargetService;
