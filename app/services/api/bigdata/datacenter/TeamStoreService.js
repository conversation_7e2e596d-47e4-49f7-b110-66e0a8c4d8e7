const BaseService = require('../../../base/BaseService');
/** com.youzan.bigdata.datacenter.base.api.service.store.TeamStoreService -  */
class TeamStoreService extends BaseService {
  get SERVICE_NAME() {
    return 'com.youzan.bigdata.datacenter.base.api.service.store.TeamStoreService';
  }

  /**
   *  获取店铺网点数据（离线 + 实时，流量 + 交易）
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/377945
   *
   *  @param {Object} param -
   *  @param {number} param.dateType - 日期类型
   *  @param {Object} param.timeParam - 时间参数：currentDay
   *  @param {number} param.kdtId - 店铺id
   *  @param {string} param.sortType - 'asc' 'desc'
   *  @param {string} param.fromApp - 请求来源
   *  @param {string} param.requestId - UUID
   *  @param {number} param.pageSize - 分页信息
   *  @param {string} param.sortBy - 排序字段
   *  @param {number} param.page - 分页信息
   *  @param {Array.<Array>} param.storeIds[] - 网点id列表
   *  @param {string} param.operator -
   *  操作人信息, json 格式 ['user_id' => $userId,
   *  'nick_name' => $nickName, 'client_ip' => $clientIp,
   *  'client_id': $clientId, 'app_id'=>$appId, 'client_name'=>$clientName]
   *  @return {Promise}
   */
  async listTeamStore(param) {
    return this.invoke('listTeamStore', [param]);
  }
}

module.exports = TeamStoreService;
