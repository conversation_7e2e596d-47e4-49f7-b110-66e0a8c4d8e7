const BaseService = require('../../../base/BaseService');
/** com.youzan.bigdata.datacenter.base.api.service.dashboard.NewDashboardOverviewService -  */
class NewDashboardOverviewService extends BaseService {
  get SERVICE_NAME() {
    return 'com.youzan.bigdata.datacenter.base.api.service.dashboard.NewDashboardOverviewService';
  }

  /**
   *
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/364765
   *
   *  @param {Object} param -
   *  @param {number} param.dateType - DateTypeEnum
   *  @param {number} param.kdtId - kdtId
   *  @param {string} param.fromApp - 请求来源
   *  @param {number} param.startDay - 开始时间
   *  @param {string} param.requestId - UUID
   *  @param {number} param.endDay - 结束时间
   *  @param {string} param.operator - 操作人信息
   *  json 格式 ['user_id' => $userId,
   *  'nick_name' => $nickName,
   *  'client_ip' => $clientIp,
   *  'client_id': $clientId, 'app_id'=>$appId, 'client_name'=>$clientName]
   *  @return {Promise}
   */
  async getSaleTarget(param) {
    return this.invoke('getSaleTarget', [param]);
  }
}

module.exports = NewDashboardOverviewService;
