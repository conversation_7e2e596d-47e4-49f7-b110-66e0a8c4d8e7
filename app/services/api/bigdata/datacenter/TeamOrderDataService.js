const BaseService = require('../../../base/BaseService');
/** com.youzan.bigdata.datacenter.base.api.service.team.TeamOrderDataService -  */
class TeamOrderDataService extends BaseService {
  get SERVICE_NAME() {
    return 'com.youzan.bigdata.datacenter.base.api.service.team.TeamOrderDataService';
  }

  /**
   *
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/421806
   *
   *  @param {Object} param -
   *  @param {number} param.dateType - 日期类型 选传
   *  @param {number} param.kdtId - 店铺id
   *  @param {string} param.fromApp - 请求来源
   *  @param {number} param.startDay - 统计日期 开始时间
   *  @param {string} param.requestId - UUID
   *  @param {number} param.endDay - 统计日期 结束时间
   *  @param {string} param.operator - 操作人信息, json
   * 格式 ['user_id' => $userId, 'nick_name' => $nickName,
   * 'client_ip' => $clientIp, 'client_id': $clientId, 'app_id'=>$appId, 'client_name'=>$clientName]
   *  @return {Promise}
   */
  async listTeamOrderTrend(param) {
    return this.invoke('listTeamOrderTrend', [param]);
  }

  /**
   *  从交易获取数据
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/422901
   *
   *  @param {Object} param -
   *  @param {number} param.kdtId - 店铺id 必传
   *  @param {string} param.fromApp - 请求来源
   *  @param {string} param.requestId - UUID
   *  @param {number} param.storeId - 网店id 选传
   *  @param {string} param.operator - 操作人信息,
   * json 格式 ['user_id' => $userId, 'nick_name' => $nickName,
   * 'client_ip' => $clientIp, 'client_id': $clientId, 'app_id'=>$appId,
   *  'client_name'=>$clientName]
   *  @return {Promise}
   */
  async getTeamOrderStatFromTrade(param) {
    return this.invoke('getTeamOrderStatFromTrade', [param]);
  }
}

module.exports = TeamOrderDataService;
