const BaseService = require('../../../base/BaseService');
/** com.youzan.bigdata.datacenter.wsc.api.service.WscGoodsAnalysisService -  */
class WscGoodsAnalysisService extends BaseService {
  get SERVICE_NAME() {
    return 'com.youzan.bigdata.datacenter.wsc.api.service.WscGoodsAnalysisService';
  }

  /**
   *  商品分析-概况数据
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/113732
   *
   *  @param {Object} basicParam -
   *  @param {number} basicParam.dateType - 时间类型，0：实时；6：自定义
   *  @param {number} basicParam.currentDay - 当前时间
   *  @param {number} basicParam.kdtId - 店铺id
   *  @param {string} basicParam.fromApp - 请求来源
   *  @param {number} basicParam.startDay - 开始时间
   *  @param {string} basicParam.requestId - UUID
   *  @param {number} basicParam.endDay - 结束时间
   *  @param {string} basicParam.operator - 操作人信息,
   *  json 格式 ['user_id' => $userId,
   * 'nick_name' => $nickName,
   * 'client_ip' => $clientIp, 'client_id': $clientId, 'app_id'=>$appId, 'client_name'=>$clientName]
   *  @return {Promise}
   */
  async getGoodOverView(basicParam) {
    return this.invoke('getGoodOverView', [basicParam]);
  }
}

module.exports = WscGoodsAnalysisService;
