const BaseService = require('../../../base/BaseService');

/**  com.youzan.bigdata.datacenter.proxy.api.service.overview.HomePageQueryService -  */
class HomePageQueryService extends BaseService {
  get SERVICE_NAME() {
    return 'com.youzan.bigdata.statcenter.proxy.api.service.overview.HomePageQueryService';
  }

  /**
   *  查询指标数据
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/1188527
   *
   *  @param {Object} param -
   *  @param {number} param.dateType - {@link }
   *  日期类型
   *  REAL_TIME(0, "今日实时"),
   *  NATURAL_DAY(1, "自然天"),
   *  NATURAL_WEEK(2, "自然周"),
   *  NATURAL_MONTH(3, "自然月"),
   *  LAST_SEVEN_DAY(4, "最近7天"),
   *  LAST_THIRTY_DAY(5, "最近30天"),
   *  SELF_DEFINE(6, "自定义"),
   *  NATURAL_QUARTER(7, "自然季度"),
   *  NATURAL_DAY_WITH_HOUR(8, "自然天 按照小时统计"),
   *  LAST_SIX_MONTH(9, "最近半年"),
   *  LAST_ONE_YEAR(10, "最近一年")
   *  @param {Array.<Array>} param.indicatorIdList[] - 指标id list
   *  @param {number} param.kdtId - kdtId
   *  @param {string} param.fromApp -
   *  @param {string} param.startDay - 开始时间，格式为yyyy-MM-dd，和endDay合用，闭区间
   *  @param {number} param.hqKdtId - 总部店铺id
   *  @param {string} param.requestId -
   *  @param {string} param.endDay - 结束时间，格式为yyyy-MM-dd，和startDay合用，闭区间 ,如果表示某一天的话， 必须和startDay相等
   *  @param {number} param.shopType - 店铺类型 0：微商城   7：零售
   *  @param {number} param.userId - 登陆用户id
   *  @param {string} param.operator -
   *  @return {Promise}
   */
  async doQueryDataByIndicators(param) {
    return this.invoke('doQueryDataByIndicators', [param]);
  }
}

module.exports = HomePageQueryService;
