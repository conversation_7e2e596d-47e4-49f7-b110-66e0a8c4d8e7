const BaseService = require('../../../base/BaseService');
/** com.youzan.bigdata.datacenter.base.api.service.ecard.ECardService -  */
class ECardService extends BaseService {
  get SERVICE_NAME() {
    return 'com.youzan.bigdata.datacenter.base.api.service.ecard.ECardService';
  }

  /**
   *
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/231215
   *
   *  @param {Object} staffParam -
   *  @param {number} staffParam.kdtId - 分组id
   *  @param {string} staffParam.fromApp - 请求来源
   *  @param {string} staffParam.requestId - UUID
   *  @param {string} staffParam.operator - 操作人信息,
   *  json 格式 ['user_id' => $userId, 'nick_name' => $nickName,
   * 'client_ip' => $clientIp, 'client_id': $clientId, 'app_id'=>$appId, 'client_name'=>$clientName]
   *  @return {Promise}
   */
  async getVerifyList(staffParam) {
    return this.invoke('getVerifyList', [staffParam]);
  }
}

module.exports = ECardService;
