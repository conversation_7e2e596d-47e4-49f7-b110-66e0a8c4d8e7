const BaseService = require('../../../base/BaseService');
/** com.youzan.bigdata.dspplatform.api.StatisticService -  */
class StatisticService extends BaseService {
  get SERVICE_NAME() {
    return 'com.youzan.bigdata.dspplatform.api.StatisticService';
  }

  /**
   *
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/365863
   *
   *  @param {number} kdtId -
   *  @return {Promise}
   */
  async wscGetAdStatusMap(kdtId) {
    return this.invoke('wscGetAdStatusMap', [kdtId]);
  }

  /**
             *  微商城改版一期数据概览
  *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/360484 
  *
             *  @param {Object} queryDTO - 
             *  @param {number} queryDTO.kdtId - 店铺ID
             *  @param {number} queryDTO.startDay - 查询开始时间 格式20190312
  当前天 开始时间和结束时间都传当前时间
             *  @param {number} queryDTO.endDay - 查询结束时间 格式20190312
             *  @return {Promise}
             */
  async wscGlobalAnalyse(queryDTO) {
    return this.invoke('wscGlobalAnalyse', [queryDTO]);
  }
}

module.exports = StatisticService;
