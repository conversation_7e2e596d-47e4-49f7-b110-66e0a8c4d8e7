const BaseService = require('../../../base/BaseService');
/** com.youzan.bigdata.ad.ext.api.service.wepay.DiscountService -  */
class DiscountService extends BaseService {
  get SERVICE_NAME() {
    return 'com.youzan.bigdata.ad.ext.api.service.wepay.DiscountService';
  }
  /**
   *
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/393498
   *
   *  @param {Object} configDTO -
   *  @param {number} configDTO.kdtId -
   *  @param {number} configDTO.percentage - 折扣代表的百分数，有效区间1-100
   *  @param {boolean} configDTO.isOn - 微信支付开关
   *  @return {Promise}
   */
  async setDiscount(configDTO) {
    return this.invoke('setDiscount', [configDTO]);
  }
  /**
   *
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/393497
   *
   *  @param {Object} queryDTO -
   *  @param {number} queryDTO.kdtId -
   *  @return {Promise}
   */
  async getDiscount(queryDTO) {
    return this.invoke('get', [queryDTO]);
  }
}

module.exports = DiscountService;
