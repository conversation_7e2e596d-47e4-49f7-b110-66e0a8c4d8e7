const BaseService = require('../../../base/BaseService');
/**  com.youzan.bigdata.jarvis.data.manager.api.service.card.CardIndicatorTargetSetService -  */
class CardIndicatorTargetSetService extends BaseService {
  get SERVICE_NAME() {
    return 'com.youzan.bigdata.jarvis.data.manager.api.service.card.CardIndicatorTargetSetService';
  }

  /**
   *  保存指标目标信息
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/1222437
   *
   *  @param {Object} param -
   *  @param {number} param.indicatorId - 指标id
   *  @param {number} param.dateType - 日期类型 1-自然日，2-自然周，3-自然月
   *  @param {number} param.kdtId - 店铺id
   *  @param {string} param.fromApp -
   *  @param {number} param.hqKdtId - 总部店铺id
   *  @param {string} param.requestId -
   *  @param {number} param.adminId - 登陆用户Id
   *  @param {number} param.targetValue - 目标值
   *  @param {number} param.targetDay - 设置目标的日期
   *  @param {string} param.operator -
   *  @return {Promise}
   */
  async saveIndicatorTarget(param) {
    return this.invoke('saveIndicatorTarget', [param]);
  }

  /**
   *  查询指标目标信息
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/1222438
   *
   *  @param {Object} param -
   *  @param {number} param.indicatorId - 指标id
   *  @param {number} param.dateType - 日期类型 1-自然日，2-自然周，3-自然月
   *  @param {number} param.kdtId - 店铺id
   *  @param {string} param.fromApp -
   *  @param {number} param.hqKdtId - 总部店铺id
   *  @param {string} param.requestId -
   *  @param {number} param.adminId - 登陆用户Id
   *  @param {number} param.targetValue - 目标值
   *  @param {number} param.targetDay - 设置目标的日期
   *  @param {string} param.operator -
   *  @return {Promise}
   */
  async queryIndicatorTarget(param) {
    return this.invoke('queryIndicatorTarget', [param]);
  }
}

module.exports = CardIndicatorTargetSetService;
