const BaseService = require('../../base/BaseService');

/**
 * 小红书晒图打卡相关接口
 */
class VersionInfoService extends BaseService {
  /**
   * SERVICE_NAME
   * @return {string}
   * @constructor
   */
  get SERVICE_NAME() {
    return 'com.youzan.channels.apps.service.VersionInfoService';
  }

  /** 获取小程序版本信息 */
  async getLatestVersionInfo(dto) {
    return this.invoke('getLatestVersionInfo', [dto]);
  }
}

module.exports = VersionInfoService;
