const BaseService = require('../../base/BaseService');

/**
 * 小红书晒图打卡相关接口
 */
class ContentThirdNoteService extends BaseService {
  /**
   * SERVICE_NAME
   * @return {string}
   * @constructor
   */
  get SERVICE_NAME() {
    return 'com.youzan.ebiz.video.channels.flow.api.service.content.ContentThirdNoteService';
  }

  /** 数据白名单 */
  async isDataSwitch(param) {
    return this.invoke('isDataSwitch', [param]);
  }
}

module.exports = ContentThirdNoteService;
