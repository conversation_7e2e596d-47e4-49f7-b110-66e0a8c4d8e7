const BaseService = require('../../base/BaseService');

/**
 * 小红书晒图打卡相关接口
 */
class SharePhotoActivityService extends BaseService {
  /**
   * SERVICE_NAME
   * @return {string}
   * @constructor
   */
  get SERVICE_NAME() {
    return 'com.youzan.ebiz.video.channels.flow.api.service.content.SharePhotoActivityService';
  }

  /** 查询笔记范例 */
  async getExampleNote(param) {
    return this.invoke('getExampleNote', [param]);
  }

  /** 初始化活动 */
  async initActivity(param) {
    return this.invoke('init', [param]);
  }

  /** 初始化活动 */
  async createActivityTest(param) {
    return this.invoke('init', [param]);
  }

  /** 开启活动 */
  async start(param) {
    return this.invoke('start', [param]);
  }

  /** 更新活动 */
  async update(param) {
    return this.invoke('update', [param]);
  }

  /** 活动列表 */
  async listOfPage(param) {
    return this.invoke('listOfPage', [param]);
  }

  /** 活动详情 */
  async getById(param) {
    return this.invoke('getById', [param]);
  }
}

module.exports = SharePhotoActivityService;
