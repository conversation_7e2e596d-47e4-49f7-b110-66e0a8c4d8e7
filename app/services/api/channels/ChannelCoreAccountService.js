const BaseService = require('../../base/BaseService');

/**
 * 渠道相关接口
 */
class ChannelCoreAccountService extends BaseService {
  /**
   * SERVICE_NAME
   * @return {string}
   * @constructor
   */
  get SERVICE_NAME() {
    return 'com.youzan.channels.channel.core.api.service.ChannelCoreAccountService';
  }

  /**
   * 文档：http://zanapi.qima-inc.com/site/service/view/783163
   * @param {object} params
   * @param {number} params.kdtId -
   * @param {number} params.accountType - 渠道类型:标识三方渠道类型   1, 微信公众号   2, 微信小程序   3, DSP移动应用   4, 微信移动应用   5, 饿了么   6, 百度小程序   7, 企业微信   8, LINE   9, 头条小程序   10,支付宝小程序   11, qq小程序
   * @param {number} params.businessType - 业务类型:标识三方渠道在有赞侧的业务用途  1,商城业务  2,品牌宣传业务  3,LINE login  4,LINE Messaging  5,LIN<PERSON>
   * @param {number} params.mpId? - 三方渠道在有赞的唯一标识mpId
   * @param {number} params.userId? - 操作人id 写接口类必传,用于记录写操作的操作人
   */
  async queryWorkWeixinCopInfo(params) {
    const result = await this.invoke('queryWorkWeixinCopInfo', [params]);
    return result || {};
  }

  /**
   *  根据外部实体id查询绑定的渠道信息
   *  accountType 必填
   *  businessType 必填
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/502224
   *
   *  @param {Object} param -
   *  @param {number} param.accountType - 账户类型:ChannelAccountTypeEnum
   *  1, 微信公众号
   *  2, 微信小程序
   *  3, DSP移动应用
   *  4, 微信移动应用
   *  5, 饿了么
   *  6, 百度小程序
   *  7, 企业微信
   *  8, LINE
   *  9, 头条小程序
   *  10,支付宝小程序
   *  11, qq小程序
   *  @param {number} param.externalId - 外部实体编号,如kdtId 必填
   *  @param {number} param.businessType - 业务类型:BusinessTypeEnum
   *  1,商城
   *  2,品宣
   *  3,LINE login
   *  4,LINE Messaging
   *  5,LINE Clova
   *  @return {Promise}
   */
  async queryMpBindInfoByKdtId(param) {
    return this.invoke('queryMpBindInfoByKdtId', [param]);
  }
}
module.exports = ChannelCoreAccountService;
