const BaseService = require('../../base/BaseService');


/**
 * 获取小程序二维码接口
 * @class WeappQrCodeService
 * @extends {ChannelsBaseService}
 */
class WeappQrCodeService extends BaseService {
  get SERVICE_NAME() {
    return 'com.youzan.channels.apps.service.WeappQRCodeService';
  }

  /**
   * 用V2方法
   * 获取专享版小程序推广二维码
   * @param {String} kdtId: 店铺id
   * @param {Number} path: 小程序启动路径
   *
   * 文档: http://zanapi.qima-inc.com/site/service/view/155922
   */
  async wxaGetCode(kdtId, path) {
    return await this.invoke('wxaGetCode', [kdtId, path]);
  }

  /**
   *  有限制地获取小程序码，path最多128字节，有透明底色选项。
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/307437
   *
   *  @param {Object} params -
   *  @param {string} params.path - 扫码进入的小程序页面路径，最大长度 128 字节，不能为空
   *  @param {number} params.kdtId - 店铺ID
   *  @param {number} params.width - 二维码宽度默认400px,业务方可自行设置,
   *  @param {boolean} params.hyaLine - 透明底色
   *  @return {Promise}
   */
  async wxaGetCodeV2(params) {
    return this.invoke('wxaGetCode', [params]);
  }

  /**
   * 无限获取小程序推广二维码
   * @param {Object} params - 入参对象
   * @param {number} params.kdtId - 店铺ID
   * @param {boolean} params.hyaLine - 透明底色
   * @param {string} params.page - 必须是已经发布的小程序存在的页面（否则报错）
   * @param {Object} params.params - {'kdtId':'63077','guestKdtId':'63077','page':'packages/goods/detail/index'}
   * @return {Promise}
   *
   * 文档: http://zanapi.qima-inc.com/site/service/view/266047
   */
  async wxaGetCodeUltra(params) {
    return await this.invoke('wxaGetCodeUltra', [params]);
  }

  /**
  *  微信素材查询
  传入 mpId + materialIds
  *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/704935
  *
  *  @param {Object} getMaterialDto -
  *  @param {number} getMaterialDto.kdtId - 店铺id
  *  @param {string} getMaterialDto.appId - 授权主体唯一标识
  *  @param {number} getMaterialDto.accountType - 渠道账号类型
  1代表微信公众号；2代表微信小程序；3代表DSP；4代表微信移动应用；5代表饿了么；6代表百度智能小程序
  *  @param {Array.<Array>} getMaterialDto.materialIds[] - 图片素材id 列表
  *  @param {Array} getMaterialDto.materialIds[] -
  *  @param {number} getMaterialDto.businessType - 业务类型，1代表商城；2代表直播业务
  *  @param {number} getMaterialDto.mpId - 授权主体的唯一标识
  *  @return {Promise}
  */
  async getMaterialByParam(getMaterialDto) {
    return this.invoke('getMaterialByParam', [getMaterialDto]);
  }

  /**
   *  无限制获取小程序码，path限制128字节，有透明底色选项。
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/307439
   *
   *  @param {Object} params - 入参对象
   *  @param {number} params.kdtId - 店铺ID
   *  @param {number} params.width - 二维码宽度默认400px,业务方可自行设置,
   *  @param {boolean} params.hyaLine - 透明底色
   *  @param {string} params.page - 必须是已经发布的小程序存在的页面（否则报错）
   *  @param {string} params.scene - 最大32个可见字符，只支持数字，大小写英文以及部分特殊字符
   *  @param {string} params.businessType - 1代表商城（不传默认），2代表品宣
   *  @return {Promise}
   */
  async wxaGetCodeUnlimit(params) {
    return this.invoke('wxaGetCodeUnlimit', [params]);
  }

  /**
   *  获取指定图片的base64
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/895070
   *
   *  @param {Object} param - 图片的信息
   *  @param {string} param.url -
   *  @return {Promise}
   */
  async getImageBase64ByImageInfo(param) {
    return this.invoke('getImageBase64ByImageInfo', [param]);
  }

  /**
   * 获取小程序体验码
   * zanAPI 地址：http://zanapi.qima-inc.com/site/service/view/602597
   *
   * @param {Object} param - 请求参数
   * @return {Promise}
   */
  async getExperienceCode(param) {
    return this.invoke('getExperienceCode', [param]);
  }
}

module.exports = WeappQrCodeService;
