const BaseService = require('../../base/BaseService');

/**
 * 小红书晒图打卡相关接口
 */
class AgentBuildService extends BaseService {
  /**
   * SERVICE_NAME
   * @return {string}
   * @constructor
   */
  get SERVICE_NAME() {
    return 'com.youzan.agent.api.service.AgentBuildService';
  }

  /** 是否初始化完成智能助手 */
  async isFinished(param) {
    return this.invoke('isFinished', [param]);
  }
}

module.exports = AgentBuildService;
