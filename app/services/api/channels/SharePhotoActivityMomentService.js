const BaseService = require('../../base/BaseService');

/**
 * 小红书晒图打卡相关接口
 */
class SharePhotoActivityService extends BaseService {
  /**
   * SERVICE_NAME
   * @return {string}
   * @constructor
   */
  get SERVICE_NAME() {
    return 'com.youzan.ebiz.video.channels.flow.api.service.content.ContentActivityMomentService';
  }

  /** 活动动态列表 */
  async listOfPage(param) {
    return this.invoke('listOfPage', [param]);
  }
}

module.exports = SharePhotoActivityService;
