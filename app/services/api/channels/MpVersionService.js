const BaseService = require('../../base/BaseService');

/**
 * @class MpVersionService
 * @extend BaseService
 */
class MpVersionService extends BaseService {
  get SERVICE_NAME() {
    return 'com.youzan.channels.apps.service.MpVersionService';
  }

  /**
   *  更新渠道版本
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/684545
   *
   *  @param {Object} dto -
   *  @param {number} dto.autoUpgrade - 是否自动更新：1是 0：否
   *  @param {number} dto.kdtId - 店铺id
   *  @param {string} dto.appId - 授权主体唯一标识
   *  @param {number} dto.accountType - 渠道账号类型
      1代表微信公众号；2代表微信小程序；3代表DSP；4代表微信移动应用；5代表饿了么；6代表百度智能小程序
   *  @param {number} dto.businessType - 业务类型，1代表商城；2代表品宣业务
   *  @param {number} dto.mpId - 授权主体的唯一标识
   *  @return {Promise}
  */
  async updateMpVersion(dto) {
    return this.invoke('updateMpVersion', [dto]);
  }

  /**
   * @description 查询渠道版本
   * @see http://zanapi.qima-inc.com/site/service/view/475884
   * @param {Object} getMpVersionDto -
   * @param {number} getMpVersionDto.kdtId - 店铺ID
   * @param {number} getMpVersionDto.accountType - 渠道账号类型，0、1:公众号；2：微信小程序；3：DSP； 4：微信移动应用；5：饿了么;6:百度小程序
   * @param {number} getMpVersionDto.businessType - 业务类型，1：商城；2: 品宣
   * @param {number} getMpVersionDto.mpId - 渠道ID
   * @return {Promise}
   */
  async getMpVersion(getMpVersionDto) {
    return this.invoke('getMpVersion', [getMpVersionDto]);
  }
}

module.exports = MpVersionService;
