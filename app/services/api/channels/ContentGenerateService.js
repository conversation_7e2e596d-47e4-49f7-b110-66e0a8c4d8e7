const BaseService = require('../../base/BaseService');

/**
 * 小红书晒图打卡相关接口
 */
class ContentGenerateService extends BaseService {
  /**
   * SERVICE_NAME
   * @return {string}
   * @constructor
   */
  get SERVICE_NAME() {
    return 'com.youzan.ebiz.video.channels.flow.api.service.content.ContentGenerateService';
  }

  /** 发布回调 */
  async publishCallback(param) {
    return this.invoke('publishCallback', [param]);
  }

  /** 异步生成 */
  async asyncGenerate(param) {
    return this.invoke('asyncGenerate', [param]);
  }

  /** 获取生成结果 */
  async getGenerateResult(param) {
    return this.invoke('getGenerateResult', [param]);
  }
}

module.exports = ContentGenerateService;
