const BaseService = require('../../base/BaseService');

/**
 * 公众号相关接口
 */
class MpAccountService extends BaseService {
  /**
   * SERVICE_NAME
   * @return {string}
   * @constructor
   */
  get SERVICE_NAME() {
    return 'com.youzan.channels.service.MpAccountService';
  }

  /**
   * @description 根据kdtId获取公众号详情
   * @see http://zanapi.qima-inc.com/site/service/view/142971
   * @param {number} kdtId
   * @return {Promise<Object>}
   */
  async getMpAccountByKdtId(kdtId) {
    const result = await this.invoke('getMpAccountByKdtId', [kdtId]);
    return result || {};
  }
}

module.exports = MpAccountService;
