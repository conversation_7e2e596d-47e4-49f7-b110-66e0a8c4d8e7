const BaseService = require('../../base/BaseService');

/**
 * 公众号相关接口
 */
class WeappAccountService extends BaseService {
  /**
   * SERVICE_NAME
   * @return {string}
   * @constructor
   */
  get SERVICE_NAME() {
    return 'com.youzan.channels.service.WeappAccountService';
  }

  /**
   * 返回公众号详情
   * 文档：https://doc.qima-inc.com/pages/viewpage.action?pageId=********
   * @param {number} kdtId
   * @return {Promise<Object>}
   */
  async getWeappAccountByKdtId(kdtId) {
    const result = await this.invoke('getWeappAccountByKdtId', [kdtId]);
    return result || {};
  }

  /**
   *
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/119056
   *
   *  @param {number} kdtId -
   *  @return {Promise}
   */
  async getWeappCodeLcByKdtId(kdtId) {
    return this.invoke('getWeappCodeLcByKdtId', [kdtId]);
  }
}

module.exports = WeappAccountService;
