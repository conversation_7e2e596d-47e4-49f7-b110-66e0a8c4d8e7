const BaseService = require('../../base/BaseService');

/**
 * 小红书晒图打卡相关接口
 */
class ContentActivityDataService extends BaseService {
  /**
   * SERVICE_NAME
   * @return {string}
   * @constructor
   */
  get SERVICE_NAME() {
    return 'com.youzan.ebiz.video.channels.flow.api.service.content.ContentActivityDataService';
  }

  /** 所有活动数据概览 */
  async overviewOfAllActivity(param) {
    return this.invoke('overviewOfAllActivity', [param]);
  }

  /** 活动数据概览 */
  async overviewOfActivity(param) {
    return this.invoke('overviewOfActivity', [param]);
  }
}

module.exports = ContentActivityDataService;
