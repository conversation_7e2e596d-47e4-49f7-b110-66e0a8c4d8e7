const BaseService = require('../../../base/BaseService');

/** com.youzan.risk.bifrost.api.client.service.RiskCommonService -  */
class RiskCommonService extends BaseService {
  get SERVICE_NAME() {
    return 'com.youzan.risk.bifrost.api.client.service.RiskCommonService';
  }

  /**
   *  风控服务通用查询接口
   *  https://doc.qima-inc.com/pages/viewpage.action?pageId=268035447
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/717042
   *
   *  @param {Object} riskCommonDTO - 查询bean
   *  @param {string} riskCommonDTO.serviceCode - 服务code,必填
   *  @param {Object} riskCommonDTO.params - Map<String,Object> 类型入参,必填
   */
  async commonInvoke(riskCommonDTO) {
    return this.riskApiInvoke('commonInvoke', [riskCommonDTO]);
  }
}

module.exports = RiskCommonService;
