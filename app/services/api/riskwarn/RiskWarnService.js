const BaseService = require('../../base/BaseService');

/**
 * 风险店铺警示Service
 * @class RiskWarnService
 * @extends {BaseService}
 */
class RiskWarnService extends BaseService {
  /**
   * 风险店铺警示Service
   * @readonly
   * @memberof RiskWarnService
   */
  get SERVICE_NAME() {
    return 'com.youzan.ctu.list.api.advisory.service.RiskWarnService';
  }

  /**
   * 风险店铺警示接入
   * https://doc.qima-inc.com/pages/viewpage.action?pageId=207227230
   * @param {Object} param
   * @return {Object}
   * @memberof RiskWarnService
   */
  async getRiskWarnMatch(param) {
    return this.invoke('match', [param]);
  }
}

module.exports = RiskWarnService;
