const BaseService = require('../../base/BaseService');

/**
 * 开放应用授权
 *
 * @class OauthRemoteService
 * @extends {BaseService}
 */
class OauthRemoteService extends BaseService {
  get SERVICE_NAME() {
    return 'com.youzan.bifrost.developer.api.service.OauthRemoteService';
  }
  /**
   *  根据grantId获取授权列表（未授权和已授权）
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/300837
   *
   *  @param {Object} authotizationDTO -
   *  @param {string} authotizationDTO.grantId - 授权Id
   *  @param {string} authotizationDTO.grantType - 授权类型
   *  @return {Promise}
   */
  async getAuthorizationList(authotizationDTO) {
    return this.invoke('getAuthorizationList', [authotizationDTO]);
  }

  /**
   *  授权操作
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/300838
   *
   *  @param {Object} authotizationDTO -
   *  @param {string} authotizationDTO.grantId - 授权Id
   *  @param {number} authotizationDTO.appId - 应用Id
   *  @param {string} authotizationDTO.grantType - 授权类型
   *  @return {Promise}
   */
  async authorize(authotizationDTO) {
    return this.invoke('authorize', [authotizationDTO]);
  }

  /**
   *  解除授权操作
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/300839
   *
   *  @param {Object} authotizationDTO -
   *  @param {string} authotizationDTO.grantId - 授权Id
   *  @param {number} authotizationDTO.appId - 应用Id
   *  @param {string} authotizationDTO.grantType - 授权类型
   *  @return {Promise}
   */
  async unAuthorize(authotizationDTO) {
    return this.invoke('unAuthorize', [authotizationDTO]);
  }
}

module.exports = OauthRemoteService;
