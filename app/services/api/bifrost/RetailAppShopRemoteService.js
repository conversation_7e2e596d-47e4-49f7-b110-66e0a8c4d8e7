const BaseService = require('../../base/BaseService');

/**
 *  com.youzan.bifrost.service.api.service.RetailAppShopRemoteService -
 **/
class RetailAppShopRemoteService extends BaseService {
  get SERVICE_NAME() {
    return 'com.youzan.bifrost.service.api.service.RetailAppShopRemoteService';
  }

  /**
   *  零售3.0App开店获取绑定的App开店信息（是否是总店、有效期、绑定应用名称、绑定App名称）
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/735151
   *
   *  @param {string} grantId - 店铺Id
   *  @return {Promise}
   */
  async getRetailAppShopInfo(grantId) {
    return this.invoke('getRetailAppShopInfo', [grantId]);
  }

  /**
   *  零售3.0App开店待授权绑定店铺（网店）列表
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/735097
   *
   *  @param {Object} request -
   *  @param {number} request.total -
   *  @param {string} request.grantId - 总店授权Id，请求子网店列表
   *  @param {string} request.clientId - 绑定的应用
   *  @param {number} request.pageSize -
   *  @param {number} request.page - 获取页码,页编码从1开始
   *  @param {Array.<Object>} request.content[] -
   *  @return {Promise}
   */
  async getAuthSubShopList(request) {
    return this.invoke('getAuthSubShopList', [request]);
  }

  /**
   *  零售3.0App开店解除绑定店铺（网店）列表
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/735098
   *
   *  @param {Object} request -
   *  @param {number} request.total -
   *  @param {string} request.grantId - 总店授权Id，请求子网店列表
   *  @param {string} request.clientId - 绑定的应用
   *  @param {number} request.pageSize -
   *  @param {number} request.page - 获取页码,页编码从1开始
   *  @param {Array.<Object>} request.content[] -
   *  @return {Promise}
   */
  async getUnbindSubShopList(request) {
    return this.invoke('getUnbindSubShopList', [request]);
  }

  /**
   *  零售3.0App开店绑定店铺
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/735099
   *
   *  @param {Object} request - 店铺Id
   *  @param {number} request.total -
   *  @param {string} request.clientId - 绑定的应用
   *  @param {string} request.grantId - 绑定的总店id
   *  @param {number} request.pageSize -
   *  @param {number} request.page - 获取页码,页编码从1开始
   *  @param {Array.<Object>} request.content[] -
   *  @param {Array.<Array>} request.subGrantIds[] - 绑定的子网店id
   *  @param {Array} request.subGrantIds[] -
   *  @return {Promise}
   */
  async bind(request) {
    return this.invoke('bind', [request]);
  }

  /**
   *  总店或者分店是否有零售3.0App开店能力
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/811528
   *
   *  @param {string} grantId - 店铺Id
   *  @return {Promise}
   */
  async hasRetailAppShopAbility(grantId) {
    return this.invoke('hasRetailAppShopAbility', [grantId]);
  }
}

module.exports = RetailAppShopRemoteService;
