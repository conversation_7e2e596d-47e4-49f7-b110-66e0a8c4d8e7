const BaseService = require('../../base/BaseService');

/**
 * com.youzan.bifrost.service.api.service.AppShopRemoteService -
 **/
class AppShopRemoteService extends BaseService {
  get SERVICE_NAME() {
    return 'com.youzan.bifrost.service.api.service.AppShopRemoteService';
  }

  /**
   *  App开店解除绑定店铺
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/735121
   *
   *  @param {Object} request -
   *  @param {string} request.clientId - 绑定的应用
   *  @param {string} request.grantId - 绑定的id
   *  @return {Promise}
   */
  async unbind(request) {
    return this.invoke('unbind', [request]);
  }
}

module.exports = AppShopRemoteService;
