const BaseService = require('../../../base/BaseService');

/** com.youzan.multistore.center.api.service.store.PhysicalStoreService -  */
class PhysicalStoreService extends BaseService {
  get SERVICE_NAME() {
    return 'com.youzan.multistore.center.api.service.store.PhysicalStoreService';
  }

  /**
   *  创建线下门店
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/337865
   *
   *  @param {Object} createDTO - {@link PhysicalStoreCreateDTO,
   *  com.youzan.multistore.center.api.dto.req.physicalstore.PhysicalStoreOperateDTO}
   *  @param {Array.<Array>} createDTO.images[] - 门店照片
   *  @param {Object} createDTO.address - 地理位置信息
   *  @param {number} createDTO.kdtId - 店铺id
   *  @param {Object} createDTO.phone - 联系方式
   *  @param {Object} createDTO.businessTimeSetting - 运营时间设置
   *  @param {string} createDTO.name - 名称
   *  @param {Object} createDTO.bizTimeSetting - 新版支持跨天的营业时间
   *  @param {string} createDTO.description - 描述
   *  @param {Object} createDTO.operator - 操作人信息
   *  @return {Promise}
   */
  async create(createDTO) {
    return this.invoke('create', [createDTO]);
  }

  /**
   *  根据店铺id和线下门店id更新线下门店
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/337866
   *
   *  @param {Object} updateDTO - {@link PhysicalStoreUpdateDTO,
   * com.youzan.multistore.center.api.dto.req.physicalstore.PhysicalStoreOperateDTO}
   *  @param {Array.<Array>} updateDTO.images[] - 门店照片
   *  @param {Object} updateDTO.address - 地理位置信息
   *  @param {number} updateDTO.kdtId - 店铺id
   *  @param {Object} updateDTO.phone - 联系方式
   *  @param {Object} updateDTO.businessTimeSetting - 运营时间设置
   *  @param {string} updateDTO.name - 名称
   *  @param {Object} updateDTO.bizTimeSetting - 新版支持跨天的营业时间
   *  @param {string} updateDTO.description - 描述
   *  @param {number} updateDTO.id -
   *  @param {Object} updateDTO.operator - 操作人信息
   *  @return {Promise}
   */
  async updateByKdtIdAndId(updateDTO) {
    return this.invoke('updateByKdtIdAndId', [updateDTO]);
  }
}

module.exports = PhysicalStoreService;
