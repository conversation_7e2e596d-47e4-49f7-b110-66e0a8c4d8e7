const BaseService = require('../../../base/BaseService');

/** com.youzan.multistore.center.api.service.store.StoreOperateService -  */
class StoreOperateService extends BaseService {
  get SERVICE_NAME() {
    return 'com.youzan.multistore.center.api.service.store.StoreOperateService';
  }

  /**
   *
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/306996
   *
   *  @param {Object} storeDelDTO -
   *  @param {number} storeDelDTO.kdtId - 店铺ID
   *  @param {number} storeDelDTO.storeId - 网点ID
   *  @param {number} storeDelDTO.operatorId - 操作人ID
   *  @return {Promise}
   */
  async delete(storeDelDTO) {
    return this.invoke('delete', [storeDelDTO]);
  }
}

module.exports = StoreOperateService;
