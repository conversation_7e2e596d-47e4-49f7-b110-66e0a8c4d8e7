const BaseService = require('../../../base/BaseService');

/** com.youzan.showcase.center.api.service.dashboard.CommonModuleService -  */
class FxCommonModuleService extends BaseService {
  get SERVICE_NAME() {
    return 'com.youzan.showcase.center.api.service.dashboard.FxCommonModuleService';
  }

  /**
   *  保存管理员自定义常用模块设置
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/357740
   *
   *  @param {Object} request -
   *  @param {string} request.bizType -
   *  @param {number} request.kdtId -
   *  @param {string} request.settingData -
   *  @param {string} request.fromApp - 来源应用，填写应用名
   *  @param {number} request.adminId -
   *  @param {number} request.id - 主键ID
   *  @param {number} request.operatorType - 操作账号类型，1:'有赞账号id'，2:'内部员工统一账号id'
   *  @param {number} request.operatorId - 操作人账号id
   *  @param {number} request.platform -
   *  @return {Promise}
   */
  async saveCustomCommonModuleSetting(request) {
    return this.invoke('saveCustomCommonModuleSetting', [request]);
  }

  /**
   *  查询可用的常用模块组件
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/362073
   *
   *  @param {Object} request -
   *  @param {string} request.samBiz -
   *  @param {number} request.samRoleId -
   *  @param {number} request.kdtId -
   *  @param {number} request.samRoleType -
   *  @param {number} request.adminId -
   *  @return {Promise}
   */
  async listCommonModuleCmpsGroupByTag(request) {
    return this.invoke('listCommonModuleCmpsGroupByTag', [request]);
  }

  /**
   *  根据adminId和展示平台，查询常用功能列表
      优先展示用户自定义设置，若没有则展示管理员角色对应默认配置
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/357739
   *
   *  @param {Object} request -
   *  @param {string} request.samBiz -
   *  @param {string} request.bizType -
   *  @param {number} request.samRoleId -
   *  @param {number} request.kdtId -
   *  @param {number} request.samRoleType -
   *  @param {number} request.adminId -
   *  @param {number} request.platform -
   *  @return {Promise}
  */
  async queryCommonModuleSetting(request) {
    return this.invoke('queryCommonModuleSetting', [request]);
  }

  /**
   *  查询角色默认配置
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/376783
   *
   *  @param {Object} request - CommonModuleSettingQueryRequest
   *  @param {string} request.samBiz - 'wsc'-微商城,
   *  @param {number} request.samRoleId - 角色id,  例如1高级管理员； 2客服
   *  @param {number} request.kdtId - 店铺id
   *  @param {number} request.samRoleType - 角色类型 1,系统默认角色； 2，自定义角色
   *  @param {number} request.adminId - 管理员id
   *  @param {number} request.platform - 平台 4，pc; 5 wsc_app
   *  @return {Promise}
   */
  async queryCommonModuleDefaultSetting(request) {
    return this.invoke('queryCommonModuleDefaultSetting', [request]);
  }

  /**
   *  查询精简版微商城默认数据概览配置
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/425445
   *
   *  @param {Object} request - CommonModuleSettingQueryRequest
   *  @param {string} request.samBiz - 'wsc'-微商城,
   *  @param {number} request.samRoleId - 角色id,  例如1高级管理员； 2客服
   *  @param {number} request.kdtId - 店铺id
   *  @param {number} request.samRoleType - 角色类型 1,系统默认角色； 2，自定义角色
   *  @param {number} request.adminId - 管理员id
   *  @param {number} request.platform - 平台 4，pc; 5 wsc_app
   *  @return {Promise}
   */
  async queryCommonModuleDefaultForBriefWsc(request) {
    return this.invoke('queryCommonModuleDefaultForBriefWsc', [request]);
  }

  /**
             *  查询国外版微商城常用功能概览配置
  *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/860773
  *
             *  @param {Object} request -
             *  @param {string} request.samBiz - com.youzan.showcase.center.common.enums.account.SamBizEnum
  业务线：微商城/零售/美业等
  现在只有微商城，'wsc'
             *  @param {string} request.wscType - 微商城衍生版类型，比如北美版 wscNA
             *  @return {Promise}
             */
  async queryCommonModuleForRegionWsc(request) {
    return this.invoke('queryCommonModuleForRegionWsc', [request]);
  }

  /**
   *  根据店铺类型查询所有模块列表
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/902497
   *
   *  @param {Object} request -
   *  @param {string} request.samBiz - 'wsc'-微商城,
   *  @param {string} request.appVersion - app版本
   *  @param {number} request.samRoleId - 角色id,  例如1高级管理员； 2客服
   *  @param {Array.<Object>} request.multiSamRoles[] - 单个管理员，多角色配置的情况；
   *  @param {string} request.bizType - 模块
   *  @param {number} request.kdtId - 店铺id
   *  @param {number} request.samRoleType - 角色类型 1,系统默认角色； 2，自定义角色
   *  @param {number} request.adminId - 管理员id
   *  @param {number} request.shopBizType - 店铺业务类型
   *  @param {number} request.platform - 平台 4，pc; 5 wsc_app
   *  @return {Promise}
   */
  async queryModuleSettingList(request) {
    return this.invoke('queryModuleSettingList', [request]);
  }
}

module.exports = FxCommonModuleService;
