const BaseService = require('../../../base/BaseService');
/** com.youzan.showcase.center.api.service.micropage.MicropageReadService -  */
class MicropageReadService extends BaseService {
  get SERVICE_NAME() {
    return 'com.youzan.showcase.center.api.service.micropage.MicropageReadService';
  }

  /**
   *  查询微页面数量
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/298794
   *
   *  @param {Object} countRequest - MicropageCountRequest
   *  @param {number} countRequest.kdtId - kdtId  店铺id
   *  @param {string} countRequest.title - 微页面标题； 支持模糊匹配
   *  @param {boolean} countRequest.isDisplay - 是否上架， true 上架； false下架
   *  @return {Promise}
   */
  async queryMicropageCount(countRequest) {
    return this.invoke('queryMicropageCount', [countRequest]);
  }
}

module.exports = MicropageReadService;
