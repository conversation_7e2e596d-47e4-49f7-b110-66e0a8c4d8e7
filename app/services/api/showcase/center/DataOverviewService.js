const BaseService = require('../../../base/BaseService');

/** com.youzan.showcase.center.api.service.dashboard.DataOverviewService -  */
class DataOverviewService extends BaseService {
  get SERVICE_NAME() {
    if (this.ctx.hostname.indexOf('fx.youzan.com') === 0) {
      return 'com.youzan.showcase.center.api.service.dashboard.FxDataOverviewService';
    }

    return 'com.youzan.showcase.center.api.service.dashboard.DataOverviewService';
  }

  /**
             *  保存管理员自定义数据概况模块设置
  *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/362054
  *
             *  @param {Object} request -
             *  @param {string} request.samBiz - 'wsc'-微商城,
             *  @param {Array.<Object>} request.cmpList[] - 组件列表
             *  @param {number} request.samRoleId - 角色id,  例如1高级管理员； 2客服
             *  @param {number} request.kdtId - 店铺id
             *  @param {number} request.samRoleType - 角色类型 1,系统默认角色； 2，自定义角色
             *  @param {string} request.fromApp - 来源应用，填写应用名
             *  @param {number} request.adminId - 管理员id
             *  @param {number} request.showType - 展示样式；
             *  @param {number} request.operatorType - 操作账号类型，1:'有赞账号id'，2:'内部员工统一账号id'
             *  @param {number} request.operatorId - 操作人账号id
             *  @param {number} request.shopBizType - 店铺业务类型
  微商城单店/教育单店 1
  微商城连锁-总店 5
  微商城连锁-网店 6
             *  @param {number} request.platform - 平台 4，pc; 5 wsc_app
             *  @return {Promise}
             */
  async saveCustomDataOverviewSetting(request) {
    return this.invoke('saveCustomDataOverviewSetting', [request]);
  }

  /**
   *  翻页查询可用的数据概况模块组件
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/362055
   *
   *  @param {Object} request -
   *  @param {string} request.samBiz - 'wsc'-微商城,
   *  @param {number} request.samRoleId - 角色id,  例如1高级管理员； 2客服
   *  @param {number} request.kdtId - 店铺id
   *  @param {number} request.samRoleType - 角色类型 1,系统默认角色； 2，自定义角色
   *  @param {number} request.adminId - 管理员id
   *  @param {number} request.pageSize - 页数
   *  @param {number} request.page - 页码
   *  @return {Promise}
   */
  async listAvailableDataOverviewCmps(request) {
    return this.invoke('listAvailableDataOverviewCmps', [request]);
  }

  /**
             *  根据adminId和展示平台，查询数据概览模块设置
  优先展示用户自定义设置，若没有则展示管理员角色对应默认配置
  *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/362053
  *
             *  @param {Object} request -
             *  @param {string} request.samBiz - 'wsc'-微商城, 必传
             *  @param {number} request.samRoleId - 角色id,  例如1高级管理员； 2客服
             *  @param {Array.<Object>} request.multiSamRoles[] - 单个管理员，多角色配置的情况； 传了samRoleId， 单角色优先
             *  @param {number} request.kdtId - 店铺id
             *  @param {number} request.samRoleType - 角色类型 1,系统默认角色； 2，自定义角色
             *  @param {number} request.adminId - 管理员id
             *  @param {number} request.shopBizType - 店铺业务类型
  微商城单店/教育单店 1
  微商城连锁-总店 5
  微商城连锁-网店 6
             *  @param {number} request.platform - 平台 4，pc; 5 wsc_app
             *  @return {Promise}
             */
  async queryDataOverviewSetting(request) {
    return this.invoke('queryDataOverviewSetting', [request]);
  }

  /**
             *  查询可用的数据概况模块组件
  *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/376502
  *
             *  @param {Object} request - AvailableCmpsQueryRequest
             *  @param {string} request.samBiz - 'wsc'-微商城, 必传
             *  @param {number} request.samRoleId - 角色id,   和 多角色参数 两者选填一个
             *  @param {Array.<Object>} request.multiSamRoles[] - 单个管理员，多角色配置的情况；
             *  @param {number} request.kdtId - 店铺id， 必传
             *  @param {number} request.samRoleType - 角色类型 1,系统默认角色； 2，自定义角色
             *  @param {number} request.pageSize - 页数 默认20
             *  @param {number} request.page - 页码 默认1
             *  @param {string} request.title - title 组件标题
             *  @param {number} request.shopBizType - 店铺业务类型
  微商城单店/教育单店 1
  微商城连锁-总店 5
  微商城连锁-网店 6
             *  @return {Promise}
             */
  async listDataOverviewCmpsGroupByTag(request) {
    return this.invoke('listDataOverviewCmpsGroupByTag', [request]);
  }

  /**
             *  查询角色默认配置
  *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/376847
  *
             *  @param {Object} request - CommonModuleSettingQueryRequest
             *  @param {string} request.samBiz - 'wsc'-微商城, 必传
             *  @param {number} request.samRoleId - 角色id,  例如1高级管理员； 2客服
             *  @param {Array.<Object>} request.multiSamRoles[] - 单个管理员，多角色配置的情况； 传了samRoleId， 单角色优先
             *  @param {number} request.kdtId - 店铺id
             *  @param {number} request.samRoleType - 角色类型 1,系统默认角色； 2，自定义角色
             *  @param {number} request.adminId - 管理员id
             *  @param {number} request.shopBizType - 店铺业务类型
  微商城单店/教育单店 1
  微商城连锁-总店 5
  微商城连锁-网店 6
             *  @param {number} request.platform - 平台 4，pc; 5 wsc_app
             *  @return {Promise}
             */
  async queryDataOverviewDefaultSetting(request) {
    return this.invoke('queryDataOverviewDefaultSetting', [request]);
  }

  /**
   *  查询精简版微商城默认数据概览配置
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/425450
   *
   *  @param {Object} request -
   *  @param {string} request.samBiz - 'wsc'-微商城,
   *  @param {number} request.samRoleId - 角色id,  例如1高级管理员； 2客服
   *  @param {number} request.kdtId - 店铺id
   *  @param {number} request.samRoleType - 角色类型 1,系统默认角色； 2，自定义角色
   *  @param {number} request.adminId - 管理员id
   *  @param {number} request.platform - 平台 4，pc; 5 wsc_app
   *  @return {Promise}
   */
  async queryDataOverviewDefaultForBriefWsc(request) {
    return this.invoke('queryDataOverviewDefaultForBriefWsc', [request]);
  }

  /**
             *  查询国外区域版微商城的数据概况配置，目前是北美版 wscNA
  *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/860766
  *
             *  @param {Object} request -
             *  @param {string} request.samBiz - com.youzan.showcase.center.common.enums.account.SamBizEnum
  业务线：微商城/零售/美业等
  现在只有微商城，'wsc'
             *  @param {string} request.wscType - 微商城衍生版类型，比如北美版： wscNA
             *  @return {Promise}
             */
  async queryDataOverviewDefaultForBriefWscByRegion(request) {
    return this.invoke('queryDataOverviewDefaultForBriefWscByRegion', [request]);
  }
}

module.exports = DataOverviewService;
