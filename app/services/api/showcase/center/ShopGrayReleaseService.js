const BaseService = require('../../../base/BaseService');

/** com.youzan.ebiz.showcase.api.gray.ShopGrayReleaseService -  */
class ShopGrayReleaseService extends BaseService {
  SERVICE_NAME = 'com.youzan.ebiz.showcase.api.gray.ShopGrayReleaseService';

  /**
   *  查询店铺是否命中灰度规则
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/1327811
   *
   *  @param {Object} request - 请求参数
   *  @param {string} request.accountId - 用户id
   *  @param {number} request.kdtId - 店铺id
   *  @param {string} request.nickName - 用户昵称
   *  @param {string} request.grayReleaseKey - 灰度key
   *  @param {string} request.from - 来源标记
   *  @param {string} request.accountAdress - 请求来源地址
   *  @return {Promise}
   */
  async isHit(request) {
    return this.invoke('isHit', [request]);
  }
}

module.exports = ShopGrayReleaseService;
