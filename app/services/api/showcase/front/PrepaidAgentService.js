const BaseService = require('../../../base/BaseService');

/* com.youzan.showcase.front.api.service.prepaidagent.PrepaidAgentService -  */ 
class PrepaidAgentService extends BaseService {
  get SERVICE_NAME() {
    return 'com.youzan.showcase.front.api.service.prepaidagent.PrepaidAgentService';
  }

  /**
   *  生成储值海报的预览地址
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/1586420
   *
   *  @param {Object} request -
   *  @param {number} request.imgId - 储值海报的素材id，必传
   *  @param {number} request.kdtId - 店铺 id，必传
   *  @return {Promise}
   */
  async genPreviewUrl(request) {
    return this.invoke('genPreviewUrl', [request]);
  }
}

module.exports = PrepaidAgentService;
