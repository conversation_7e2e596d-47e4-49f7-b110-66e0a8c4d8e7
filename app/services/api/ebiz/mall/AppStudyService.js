const BaseService = require('../../../base/BaseService');

/** com.youzan.ebiz.mall.gemini.api.service.app.AppStudyService -  */
class AppStudyService extends BaseService {
  get SERVICE_NAME() {
    return 'com.youzan.ebiz.mall.gemini.api.service.app.AppStudyService';
  }

  /**
   *  分页查询专题或课程下的内容列表
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/478693
   *
   *  @param {Object} param -
   *  @param {Array.<Array>} param.types[] - 素材类型 用于批量查询
   *  @param {number} param.tagId - 分类id;
   *  @param {number} param.columnId - 专栏id
   *  @param {number} param.pageSize - 页数 默认20
   *  @param {string} param.title - 标题
   *  @param {number} param.type - 素材类型
   *  @param {number} param.auditorId - 作者id
   *  @param {boolean} param.withCategory -
   *  @param {boolean} param.withComponents -
   *  @param {number} param.page - 页码 默认1
   *  @param {string} param.createdAtEnd -
   *  @param {number} param.status - 素材状态
   *  @param {string} param.createdAtBegin -
   *  @return {Promise}
   */
  async listByColumnInPage(param) {
    return this.invoke('listByColumnInPage', [param]);
  }
}

module.exports = AppStudyService;
