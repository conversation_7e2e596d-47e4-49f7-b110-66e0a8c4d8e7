const BaseService = require('../../../base/BaseService');

/**
 * 店铺端海景房Service
 * @class SidebarPictureAdService
 * @extends {BaseService}
 */
class SidebarPictureAdService extends BaseService {
  /**
   * 店铺端海景房Service
   * @readonly
   * @memberof SidebarPictureAdService
   */
  get SERVICE_NAME() {
    return 'com.youzan.ebiz.mall.gemini.api.service.cp.SidebarPictureAdService';
  }

  /**
   *  获取店铺端海景房
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/693759
   *
   *  @param {Object} request -
   *  @param {number} request.kdtId -
   *  @param {number} request.shopType - 店铺类型  com.youzan.ebiz.mall.gemini.common.enums.cp.CPShopTypeEnum
   *  @param {number} request.httpSource - 来源 app pc com.youzan.ebiz.mall.gemini.common.enums.cp.CPHttpSourceEnum
   *  @return {Promise}
   */
  async getPictureAdForShop(request) {
    return this.invoke('getPictureAdForShop', [request]);
  }
}

module.exports = SidebarPictureAdService;
