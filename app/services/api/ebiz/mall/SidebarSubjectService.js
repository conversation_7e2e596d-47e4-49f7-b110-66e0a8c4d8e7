const BaseService = require('../../../base/BaseService');

/**
 * 店铺端文章列表Service
 * @class SidebarSubjectService
 * @extends {BaseService}
 */
class SidebarSubjectService extends BaseService {
  /**
   * 店铺端文章列表Service
   * @readonly
   * @memberof SidebarSubjectService
   */
  get SERVICE_NAME() {
    return 'com.youzan.ebiz.mall.gemini.api.service.cp.SidebarSubjectService';
  }

  /**
   *  店铺文章列表
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/693753
   *
   *  @param {Object} request -
   *  @param {number} request.kdtId -
   *  @param {number} request.shopType - 店铺类型  com.youzan.ebiz.mall.gemini.common.enums.cp.CPShopTypeEnum
   *  @param {number} request.httpSource - 来源 app pc com.youzan.ebiz.mall.gemini.common.enums.cp.CPHttpSourceEnum
   *  @return {Promise}
   */
  async listSubjectForShop(request) {
    return this.invoke('listSubjectForShop', [request]);
  }
}

module.exports = SidebarSubjectService;
