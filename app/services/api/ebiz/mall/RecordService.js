const BaseService = require('../../../base/BaseService');

/**
 * 商品操作记录页面相关service
 */
class RecordService extends BaseService {
  get SERVICE_NAME() {
    return 'com.youzan.ebiz.mall.tracker.api.RecordService';
  }

  /**
   * 根据参数获取商品操作记录
   * @param {Object} data
   * @param {number} data.adminId 操作人
   * @param {number} data.module 操作模块
   * @param {number} data.startTime 操作时间，开始时间
   * @param {number} data.endTime 操作时间，结束时间
   * @param {number} data.kdtId 店铺id
   * @param {number} data.page 当前页数
   * @param {number} data.size 店铺id
   * @return {Object} 商品操作记录
   * 文档: http://zanapi.qima-inc.com/site/service/view/155762
   */
  async listRecordsPage(data) {
    return this.invoke('listRecordsPage', [data]);
  }
}

module.exports = RecordService;
