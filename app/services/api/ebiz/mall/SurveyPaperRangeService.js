const BaseService = require('../../../base/BaseService');

/**
 * 概况页问卷调查Service
 * @class SurveyPaperRangeService
 * @extends {BaseService}
 */
class SurveyPaperRangeService extends BaseService {
  /**
   * 概况页问卷调查Service
   * @readonly
   * @memberof SurveyPaperRangeService
   */
  get SERVICE_NAME() {
    return 'com.youzan.ebiz.mall.gemini.api.service.cp.SurveyPaperRangeService';
  }

  /**
   *  根据kdtId和店铺code查询优先级最高的未参与的问卷投放店铺记录
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/721011
   *  @param {Object} request
   *  @param {number} request.kdtId - 店铺id
   *  @param {number} request.shopType - 店铺类型
   *  @param {number} request.httpSource - 1:app 2:pc
   *  @return {Promise}
   */
  async getSurvey(request) {
    return this.invoke('getSurvey', [request]);
  }
}

module.exports = SurveyPaperRangeService;
