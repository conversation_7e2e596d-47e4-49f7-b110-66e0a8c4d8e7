import BaseService from '../../base/BaseService';

/**
 * AI 内容生成服务
 */
class ContentAsyncService extends BaseService {
  /**
   * SERVICE_NAME
   * @return {string}
   * @constructor
   */
  get SERVICE_NAME() {
    return 'com.youzan.aigc.app.api.service.content.ContentAsyncService';
  }

  /**
   * 查询AI生成内容的ID
   * @param {Object} request - 请求参数
   * @returns {Promise} 返回生成内容的ID
   */
  async queryAiGenerateContentId(request) {
    return this.invoke('generateContent', [request]);
  }
}

module.exports = ContentAsyncService;
