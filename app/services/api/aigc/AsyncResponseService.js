import BaseService from '../../base/BaseService';

/**
 * AI 内容生成异步响应服务
 */
class ContentAsyncService extends BaseService {
  /**
   * SERVICE_NAME
   * @return {string}
   * @constructor
   */
  get SERVICE_NAME() {
    return 'com.youzan.aigc.app.api.service.async.AsyncResponseService';
  }

  /**
   * 根据ID获取AI生成的内容
   * @param {Object} request - 请求参数
   * @returns {Promise} 返回AI生成的内容
   */
  async getAiGenerateContentById(request) {
    return this.invoke('getResponse', [request]);
  }

  /**
   *  异步轮询接口，分批返回
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/1429912
   *
   *  @param {Object} param -
   *  @param {string} param.responseId - 生成文案接口返回的responseId
   *  @return {Promise}
   */
  async getBatchResponse(param) {
    return this.invoke('getBatchResponse', [param]);
  }
}
module.exports = ContentAsyncService;
