const BaseService = require('../../../base/BaseService');

/**
 * com.youzan.finance.fee.api.service.FeeStatQueryService
 */
class FeeStatQueryService extends BaseService {
  get SERVICE_NAME() {
    return 'com.youzan.finance.fee.api.service.FeeStatQueryService';
  }

  /**
   *  查询店铺欠费信息
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/428923
   *
   *  @param {number} kdtId -
   *  @return {Promise}
   */
  async queryArrearsInfo(kdtId) {
    return this.invoke('queryArrearsInfo', [kdtId]);
  }
}

module.exports = FeeStatQueryService;
