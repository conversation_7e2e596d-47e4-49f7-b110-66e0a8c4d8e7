const BaseService = require('../../base/BaseService');

/**
 * DeliverySettingService
 */
class DeliverySettingService extends BaseService {
  /**
   * @return {string}
   */
  get SERVICE_NAME() {
    return 'com.youzan.ic.delivery.service.DeliverySettingService';
  }

  /**
   * 获取物流设置开关,包括是否支持自提、同城送、快递、同城送定时达, 以及计费模式类型。
   * @param {Object} param
   * @param {number} param.kdtId 店铺id
   * @param {string} param.formApp 请求来源
   * 文档: http://zanapi.qima-inc.com/site/service/view/115246
   */
  async getSettingV3(param) {
    return this.invoke('getSettingV3', [param]);
  }

  /**
   * 更新同城配送的开关
   * @param {Object} param 需要更新的物流开关，同城配送、快递、自提至少开启一个。
   * 文档: http://zanapi.qima-inc.com/site/service/view/19240
   */
  async updateSetting(param) {
    return this.invoke('updateSetting', [param]);
  }
}

module.exports = DeliverySettingService;
