const BaseService = require('../../../base/BaseService.js');

/** com.youzan.owl.pc.api.dashboard.DashboardFacade -  */
class DashboardFacade extends BaseService {
  get SERVICE_NAME() {
    return 'com.youzan.owl.pc.api.dashboard.DashboardFacade';
  }

  /**
  *  经营概况
  *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/438160 
  *
  *  @param {number} kdtId - 店铺id
  *  @param {Array} itemNames - 需要展示的数据项名称
                    TO_BE_ASSIGNED_CLUE(1, '待分配线索数'),
                    TO_BE_FOLLOWED_UP_CLUE(2, '待跟进线索数'),
                    INCREASED_CLUE(3, '新增线索数'),
                    WAIT_VISIT_CLUE(4, '待回访线索数'),
                    CONSUMED_ASSET(5, '课时消耗数'),
                    UN_CONFIRM_APPOINTMENT(6, '待确认预约数'),
  *  @return {Promise}
  */
  async workbenchOverview(kdtId, itemNames) {
    return this.invoke('workbenchOverview', [kdtId, itemNames]);
  }

  /**
             *  实时概览
  *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/512827 
  *
             *  @param {Object} overviewQuery - 
             *  @param {number} overviewQuery.dateType - 必填
  REAL_TIME(0, '今日实时'),
  NATURAL_DAY(1, '自然天'),
  NATURAL_WEEK(2, '自然周'),
  NATURAL_MONTH(3, '自然月'),
  LAST_SEVEN_DAY(4, '最近7天'),
  LAST_THIRTY_DAY(5, '最近30天'),
  SELF_DEFINE(6, '自定义'),
  NATURAL_QUARTER(7, '自然季度'),
  YESTERDAY(8, '昨日')
             *  @param {Array.<Object>} overviewQuery.multiSamRoles[] - sam角色id，对角色的数据获取逻辑由业务方负责
             *  @param {number} overviewQuery.kdtId - 店铺ID
  必填
             *  @param {number} overviewQuery.hqKdtId - 非必填
             *  @param {number} overviewQuery.roleId - sam角色id，对角色的数据获取逻辑由业务方负责
             *  @param {Array.<Array>} overviewQuery.keys[] - 需要获取的数据KEYs
             *  @param {number} overviewQuery.startTime - 开始时间，13位时间戳
             *  @param {number} overviewQuery.endTime - 结束时间，13位时间戳
             *  @param {number} overviewQuery.storeId - 网点ID，当角色是网点管理员时会传入该值
             *  @param {number} overviewQuery.userId - 用户id
             *  @return {Promise}
             */
  async workbenchOverviewV2(overviewQuery) {
    return this.invoke('workbenchOverviewV2', [overviewQuery]);
  }

  /**
   * 首页查询任务数据
   * @link http://zanapi.qima-inc.com/site/service/view/1264605
   * @param {number} kdtId -
   * @param {Object} query -
   * @param {number} query.bizType - 业务类别
   * @param {number} query.adminId - 用户ID
   * @param {string} query.viewCode - 视图编码
   * @return {Promise}
   */
  async findMerchantTaskDashBoardView(kdtId, query) {
    return this.invoke('findMerchantTaskDashBoardView', [kdtId, query]);
  }
}

module.exports = DashboardFacade;
