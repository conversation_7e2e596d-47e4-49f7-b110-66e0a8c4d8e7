const BaseService = require('../../../base/BaseService');

/** com.youzan.owl.pc.api.merchant.StaffActionFacade */
class StaffActionFacade extends BaseService {
  get SERVICE_NAME() {
    return 'com.youzan.owl.pc.api.merchant.StaffActionFacade';
  }

  /**
   * 执行B端员工行为
   * @link http://zanapi.qima-inc.com/site/service/view/1254674
   * @param {number} kdtId -
   * @param {Object} command -
   * @param {number} command.adminId - 员工ID
   * @param {string} command.actionCode - 执行的行为
   * @param {string} command.value - 行为值
   * @return {Promise}
   */
  async execute(kdtId, command) {
    return this.invoke('execute', [kdtId, command]);
  }
}

module.exports = StaffActionFacade;
