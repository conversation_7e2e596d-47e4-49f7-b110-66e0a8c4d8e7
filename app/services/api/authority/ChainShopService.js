const BaseService = require('../../base/BaseService');

/** com.youzan.authority.center.api.auth.service.ChainShopService  **/
class ChainShopService extends BaseService {
  get SERVICE_NAME() {
    return 'com.youzan.authority.center.api.auth.service.ChainShopService';
  }

  /**
   *  获取默认授权状态
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/472214
   *
   *  @param {Object} param
   *  @return {Promise}
   */
  async getSlientAuthStatus(param) {
    return this.invoke('getSlientAuthStatus', param);
  }

  /**
   *  关闭默认授权
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/472213
   *
   *  @param {Object} param
   *  @return {Promise}
   */
  async disableSlientAuth(param) {
    return this.invoke('disableSlientAuth', param);
  }

  /**
   *  默认授权
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/472212
   *
   *  @param {Object} param
   *  @return {Promise}
   */
  async enableSlientAuth(param) {
    return this.invoke('enableSlientAuth', param);
  }
}

module.exports = ChainShopService;
