/* eslint-disable require-jsdoc-except/require-jsdoc */
/* com.youzan.authority.center.api.auth.service.iot.config.ShopDeviceConfigService -  */
const BaseService = require('../../base/BaseService');

class ShopDeviceConfigService extends BaseService {
  get SERVICE_NAME() {
    return 'com.youzan.authority.center.api.auth.service.iot.config.ShopDeviceConfigService';
  }

  /**
             *  删除设备 http://zanapi.qima-inc.com/site/service/view/490767
*  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/490767 
*
             *  @param {Object} request - 
             *  @param {number} request.identitySource - 操作人来源
 0:uic体系
             *  @param {number} request.kdtId - 店铺kdtId
             *  @param {string} request.identityId - 操作人Id
             *  @param {number} request.appId - 应用appId
             *  @param {string} request.deviceKey - 设备Id
             *  @param {number} request.env - env
             *  @return {Promise}
             */
  async removeDevice(request) {
    return this.invoke('removeDevice', [request]);
  }

  /**
             *  管理应用
*  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/509511 
*
             *  @param {Object} request - 
             *  @param {number} request.identitySource - 操作人来源
 0:uic体系
             *  @param {number} request.kdtId - 店铺kdtId
             *  @param {string} request.identityId - 操作人Id
             *  @param {number} request.appId - 应用appId
             *  @param {string} request.deviceKey - 设备Id
             *  @param {number} request.env - env
             *  @return {Promise}
             */
  async manageDeviceAndApp(request) {
    return this.invoke('manageDeviceAndApp', [request]);
  }
}

module.exports = ShopDeviceConfigService;
