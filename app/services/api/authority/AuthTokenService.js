const BaseService = require('../../base/BaseService');

/**
 * com.youzan.uic.auth.api.service.AuthTokenService
 * @class AuthService
 * @extends {BaseService}
 */
class AuthTokenService extends BaseService {
  get SERVICE_NAME() {
    return 'com.youzan.uic.auth.api.service.AuthTokenService';
  }

  /**
   *  token使用前校验
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/891403
   *
   *  @param {Object} authInfoDto -
   *  @param {Object} authInfoDto.authTokenInfo - 授权token信息
   *  @param {Object} authInfoDto.tokenAuthUserInfoDto - 授权发起用户信息
   *  @param {Array.<Object>} authInfoDto.authUserInfoList[] - 有权限授权的用户信息
   *  @param {Object} authInfoDto.authChannelInfoDto - 授权发起用户的渠道信息
   *  @param {Object} authInfoDto.scene - 授权详细场景信息
   *  @return {Promise}
   */
  async tokenCheck(authInfoDto) {
    return this.invoke('tokenCheck', [authInfoDto]);
  }

  /**
   *  查询授权token信息
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/887792
   *
   *  @param {Object} authInfoDto -
   *  @param {Object} authInfoDto.authTokenInfo - 授权token信息
   *  @param {Object} authInfoDto.tokenAuthUserInfoDto - 授权发起用户信息
   *  @param {Array.<Object>} authInfoDto.authUserInfoList[] - 有权限授权的用户信息
   *  @param {Object} authInfoDto.authChannelInfoDto - 授权发起用户的渠道信息
   *  @param {Object} authInfoDto.scene - 授权详细场景信息
   *  @return {Promise}
   */
  async getAuthTokenInfo(authInfoDto) {
    return this.invoke('getAuthTokenInfo', [authInfoDto]);
  }

  /**
   *  获取发起验证时的相关信息
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/1509765
   *
   *  @param {Object} authVerificationDto - token信息
   *  @param {string} authVerificationDto.token -
   *  @return {Promise}
   */
  async getAuthVerificationInfo(authVerificationDto) {
    return this.invoke('getAuthVerificationInfo', [authVerificationDto]);
  }
}

module.exports = AuthTokenService;
