const BaseService = require('../../base/BaseService');

/** com.youzan.authority.center.api.auth.service.query.ShopAuthQueryService -  */
class ShopAuthQueryService extends BaseService {
  get SERVICE_NAME() {
    return 'com.youzan.authority.center.api.auth.service.query.ShopAuthQueryService';
  }

  /**
    *  由店铺 获取授权信息(不分页) 定制服务
  展示正在申请&已经授权的
  *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/444078
  *
             *  @param {Object} param - 入参
             *  @param {string} param.grantId - 店铺kdtId or displayNo or merchantId
             *  @param {number} param.grantIdType - 店铺Id类型
  0: kdtId
  1: displayNo
  2: merchantId
             *  @return {Promise}
             */
  async queryAuthorizationList(param) {
    return this.invoke('queryAuthorizationList', [param]);
  }
}

module.exports = ShopAuthQueryService;
