const BaseService = require('../../base/BaseService');

/**
 *
 * @class AuthService
 * @extends {BaseService}
 */
class AuthService extends BaseService {
  get SERVICE_NAME() {
    return 'com.youzan.authority.center.api.auth.service.AuthService';
  }

  /**
   *  授权
    *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/424360
    *
    *  @param {Object} param - 入参
    *  @param {number} param.identitySource - 操作人来源
        0:uic体系
        1:cas体系
    *  @param {string} param.grantId - 店铺kdtId or displayNo or merchantId
    *  @param {string} param.expireTime - 授权过期时间
        如果是自用型应用/工具型应用测试店铺，默认授权1年
        如果是工具型应用，前端传入
    *  @param {number} param.grantIdType - 店铺Id类型
        0: kdtId
        1: displayNo
        2: merchantId
    *  @param {string} param.identityId - 操作人Id
    *  @param {number} param.appId - 应用appId
    *  @param {number} param.env - 环境参数
        0  开发环境
        1  生产环境
        -1 没有环境
  *  @return {Promise}
  */
  async authorize(param) {
    return this.invoke('authorize', [param]);
  }

  /**
             *  解除授权
*  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/424361
*
             *  @param {Object} param - 入参
             *  @param {number} param.identitySource - 操作人来源
 0:uic体系
 1:cas体系
             *  @param {string} param.grantId - 店铺kdtId or displayNo or merchantId
             *  @param {string} param.expireTime - 授权过期时间
 如果是自用型应用/工具型应用测试店铺，默认授权1年
 如果是工具型应用，前端传入
             *  @param {number} param.grantIdType - 店铺Id类型
 0: kdtId
 1: displayNo
 2: merchantId
             *  @param {string} param.identityId - 操作人Id
             *  @param {number} param.appId - 应用appId
             *  @param {number} param.env - 环境参数
 0  开发环境
 1  生产环境
 -1 没有环境
             *  @return {Promise}
             */
  async unAuthorize(param) {
    return this.invoke('unAuthorize', [param]);
  }
}

module.exports = AuthService;
