const BaseService = require('../../base/BaseService');

/**
 * com.youzan.authority.center.api.auth.service.proxy.auth.ShopAuthQueryProxyService -
 **/
class ShopAuthQueryProxyService extends BaseService {
  get SERVICE_NAME() {
    return 'com.youzan.authority.center.api.auth.service.proxy.auth.ShopAuthQueryProxyService';
  }

  /**
             *  校验店铺与人的关系
 由店铺 获取授权信息(不分页) 定制服务
 展示正在申请&已经授权的,自用型应用
*  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/979900 
*
             *  @param {Object} param - 入参
             *  @param {string} param.grantId - 店铺kdtId or displayNo or merchantId
             *  @param {number} param.grantIdType - 店铺Id类型
 0: kdtId
 1: displayNo
 2: merchantId
             *  @param {Array.<Array>} param.statusList[] - 授权状态，非必填，默认查（待授权，已授权）
 com.youzan.authority.center.api.common.enums.AuthOpenStatusEnum
             *  @param {Array} param.statusList[] - 
             *  @param {Array.<Array>} param.appTypeList[] - 应用类型，非必填，默认查（自用型）
 com.youzan.authority.center.api.common.enums.AuthorizedPartyEnum
             *  @param {Array} param.appTypeList[] - 
             *  @param {number} param.userId - 用户id
             *  @return {Promise}
             */
  async queryAuthorizationList(param) {
    return this.invoke('queryAuthorizationList', [param]);
  }
}

module.exports = ShopAuthQueryProxyService;
