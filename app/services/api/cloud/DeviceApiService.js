/* eslint-disable valid-jsdoc */
const BaseService = require('../../base/BaseService');

/** com.youzan.cloud.magneto.console.api.service.DeviceApiService -  */
class DeviceApiService extends BaseService {
  get SERVICE_NAME() {
    return 'com.youzan.cloud.magneto.console.api.service.DeviceApiService';
  }

  /**
   *  统计设备数
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/497842
   *
   *  @param {Object} request -
   *  @param {number} request.productId - 产品ID
   *  @param {number} request.operator - 操作人ID
   *  @returns {string}
   */
  async count(request) {
    return this.invoke('count', [request]);
  }

  /**
             *  添加设备
  *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/497844 
  *
             *  @param {Object} request - 
             *  @param {Object} request.ext - 三方平台扩展信息
  key: appNo/appKey/deviceNo/deviceKey/...
             *  @param {number} request.productId - 产品 Id
             *  @param {string} request.name - 设备名称
             *  @param {string} request.description - 设备备注
             *  @param {number} request.operator - 操作人ID
             *  @returns {string}
             */
  async create(request) {
    return this.invoke('create', [request]);
  }

  /**
   *  分页查询
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/497845
   *
   *  @param {string} pageQueryDTO -
   *  @returns {string}
   */
  async pageQuery(pageQueryDTO) {
    return this.invoke('pageQuery', [pageQueryDTO]);
  }

  /**
   *  删除
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/497846
   *
   *  @param {Object} request -
   *  @param {number} request.id - 主键
   *  @param {number} request.operator - 操作人ID
   *  @returns {string}
   */
  async delete(request) {
    return this.invoke('delete', [request]);
  }

  /**
   *  设备升级
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/498830
   *
   *  @param {Object} request -
   *  @param {string} request.deviceIds - 需要升级的设备 ID 集合
   *  @param {number} request.firmwareId - 固件 ID
   *  @param {number} request.operator - 操作人ID
   *  @returns {string}
   */
  async upgrade(request) {
    return this.invoke('upgrade', [request]);
  }

  /**
   *  查询设备是否存在
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/508255
   *
   *  @param {Object} request -
   *  @param {string} request.deviceKey -
   *  @returns {string}
   */
  async exist(request) {
    return this.invoke('exist', [request]);
  }

  /**
   *  分页查询
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/508258
   *
   *  @param {string} pageQueryDTO -
   *  @returns {string}
   */
  async shopPageQuery(pageQueryDTO) {
    return this.invoke('shopPageQuery', [pageQueryDTO]);
  }

  // eslint-disable-next-line require-jsdoc-except/require-jsdoc
  async appPageQuery(pageQueryDTO) {
    return this.invoke('appPageQuery', [pageQueryDTO]);
  }

  // eslint-disable-next-line require-jsdoc-except/require-jsdoc
  async firmwarePageQuery(pageQueryDTO) {
    return this.invoke('firmwarePageQuery', [pageQueryDTO]);
  }

  // eslint-disable-next-line require-jsdoc-except/require-jsdoc
  async info(request) {
    return this.invoke('info', [request]);
  }

  /**
   *  获取内部 deviceKey
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/508263
   *
   *  @param {Object} request -
   *  @param {string} request.thirdDeviceKey -
   *  @param {string} request.platform -
   *  @returns {string}
   */
  async getKey(request) {
    return this.invoke('getKey', [request]);
  }

  /**
   *  上线
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/508266
   *
   *  @param {Object} request -
   *  @param {string} request.ip - ip
   *  @param {string} request.deviceKey -
   *  @param {string} request.sdkVersion - sdk版本
   *  @param {string} request.firmwareVersion - 固件版本
   *  @returns {string}
   */
  async online(request) {
    return this.invoke('online', [request]);
  }

  /**
   *  下线
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/508267
   *
   *  @param {Object} request -
   *  @param {string} request.deviceKey -
   *  @returns {string}
   */
  async offline(request) {
    return this.invoke('offline', [request]);
  }

  /**
   *  设备元数据查询
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/509535
   *
   *  @param {Object} request -
   *  @param {number} request.platform - 平台
   *  @returns {string}
   */
  async deviceMetadataQuery(request) {
    return this.invoke('deviceMetadataQuery', [request]);
  }

  /**
   *  功能是否存在
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/509543
   *
   *  @param {Object} request -
   *  @param {string} request.identifier - 标识符
   *  @param {string} request.deviceKey - 设备 key
   *  @param {number} request.type - 脚本类型(0:属性、1:事件、2:服务)
   *  @returns {string}
   */
  async hasFunction(request) {
    return this.invoke('hasFunction', [request]);
  }

  /**
   *  查询设备所有功能
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/509547
   *
   *  @param {Object} request -
   *  @param {number} request.functionType -
   *  @param {number} request.deviceId -
   *  @returns {string}
   */
  async allFunctions(request) {
    return this.invoke('allFunctions', [request]);
  }

  /**
   *  动态注册
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/510510
   *
   *  @param {Object} request -
   *  @param {string} request.productSecret -
   *  @param {string} request.productKey -
   *  @returns {string}
   */
  async dynamicRegister(request) {
    return this.invoke('dynamicRegister', [request]);
  }

  /**
   *  基本信息，diy控制台使用
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/510517
   *
   *  @param {Object} request -
   *  @param {number} request.id -
   *  @returns {string}
   */
  async baseInfo(request) {
    return this.invoke('baseInfo', [request]);
  }

  /**
   *  网桥获取基本信息
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/510627
   *
   *  @param {string} deviceKey -
   *  @returns {string}
   */
  async getThirdByKey(deviceKey) {
    return this.invoke('getThirdByKey', [deviceKey]);
  }

  /**
   *  更新
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/510629
   *
   *  @param {Object} request -
   *  @param {string} request.name - 设备名称
   *  @param {string} request.description - 设备备注
   *  @param {number} request.id -
   *  @param {number} request.operator - 操作人ID
   *  @returns {string}
   */
  async update(request) {
    return this.invoke('update', [request]);
  }

  /**
   *  查询productId
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/512487
   *
   *  @param {string} deviceKey -
   *  @returns {string}
   */
  async getProductByKey(deviceKey) {
    return this.invoke('getProductByKey', [deviceKey]);
  }

  /**
   *
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/559317
   *
   *  @param {Object} request -
   *  @param {Object} request.ext - 三方平台扩展信息
   *  @param {string} request.productModel - 产品型号
   *  @param {string} request.thirdDeviceKey - 三方平台设备key
   *  @param {number} request.kdtId - 店铺ID
   *  @param {number} request.appId - 授权应用ID
   *  @param {string} request.name - 设备名称
   *  @param {number} request.env - 环境
   *  @param {number} request.bizLine - 所属业务线
   *  @param {number} request.platform - 平台类型
   *  @param {number} request.operator - 操作人ID
   *  @return {Promise}
   */
  async shopRegister(request) {
    return this.invoke('shopRegister', [request]);
  }

  /**
   *
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/566112
   *
   *  @param {Object} request -
   *  @param {number} request.id -
   *  @return {Promise}
   */
  async shopDelete(request) {
    return this.invoke('shopDelete', [request]);
  }
}

module.exports = DeviceApiService;
