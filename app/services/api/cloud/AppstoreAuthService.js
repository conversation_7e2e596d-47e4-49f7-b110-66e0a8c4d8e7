/* eslint-disable require-jsdoc-except/require-jsdoc */
const BaseService = require('../../base/BaseService');

/* com.youzan.cloud.appstore.api.service.auth.AppstoreAuthService -  */
class AppstoreAuthService extends BaseService {
  get SERVICE_NAME() {
    return 'com.youzan.cloud.appstore.api.service.auth.AppstoreAuthService';
  }

  /**
   *  应用授权开关
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/962502
   *
   *  @param {Array} params -
   *  @return {Promise}
   */
  async batchAuthSwitch(params) {
    return this.invoke('batchAuthSwitch', [params]);
  }
}

module.exports = AppstoreAuthService;
