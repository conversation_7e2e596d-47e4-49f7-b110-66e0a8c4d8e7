/* eslint-disable require-jsdoc-except/require-jsdoc */
const BaseService = require('../../base/BaseService');

/* com.youzan.cloud.magneto.console.api.service.CommonApiService -  */
class CommonApiService extends BaseService {
  get SERVICE_NAME() {
    return 'com.youzan.cloud.magneto.console.api.service.CommonApiService';
  }

  /**
   *  获取枚举值列表
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/512466
   *
   *  @param {string} identifiers - 枚举值标识: propertyWritable\toolkitType\platformType\productCategory
   *  @return {string}
   */
  async enums(identifiers) {
    return this.invoke('enums', [identifiers]);
  }

  /**
   *
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/565911
   *
   *  @param {Object} request -
   *  @param {number} request.bizLine -
   *  @param {number} request.platform -
   *  @return {Promise}
   */
  async platformDeviceRequiredParams(request) {
    return this.invoke('platformDeviceRequiredParams', [request]);
  }
}

module.exports = CommonApiService;
