const BaseService = require('../../base/BaseService');

/** com.youzan.cloud.darwin.api.service.rule.RuleCheckService -  */
class RuleCheckService extends BaseService {
  get SERVICE_NAME() {
    return 'com.youzan.cloud.darwin.api.service.rule.RuleCheckService';
  }

  /**
   *  规则冲突检测
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/360161
   *
   *  @param {Object} request -
   *  @param {number} request.kdtId - 绑定的店铺id 必填
   *  @param {number} request.appId - 待激活 appId
   *  @param {string} request.env - 环境变量 必填
   *  @return {Promise}
   */
  async checkRuleConflict(request) {
    return this.invoke('checkRuleConflict', [request]);
  }
}

module.exports = RuleCheckService;
