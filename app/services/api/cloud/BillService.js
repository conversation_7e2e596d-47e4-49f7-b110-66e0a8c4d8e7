/* eslint-disable require-jsdoc-except/require-jsdoc */
const BaseService = require('../../base/BaseService');

/* com.youzan.cloud.abacus.console.api.service.BillService -  */

class BillService extends BaseService {
  get SERVICE_NAME() {
    return 'com.youzan.cloud.abacus.console.api.service.BillService';
  }

  /**
   * 根据店铺Id查询已授权应用欠费信息
   * zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/1079395
   *
   * @param {Object} request -
   * @param {number} request.grantId -
   * @return {Promise}
   */
  async arrearsAppList(request) {
    return this.invoke('arrearsAppList', [request]);
  }
}

module.exports = BillService;
