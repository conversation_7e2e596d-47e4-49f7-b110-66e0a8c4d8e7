const BaseService = require('../../../base/BaseService');

/** com.youzan.shopcenter.shop.service.ShopBaseReadService -  */
class ShopBaseReadService extends BaseService {
  get SERVICE_NAME() {
    return 'com.youzan.shopcenter.shop.service.ShopBaseReadService';
  }

  /**
   *
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/8988
   *
   *  @param {number} kdtId -
   *  @return {Promise}
   */
  async getShopBaseInfoByKdtId(kdtId) {
    return this.shopcenterApiInvoke('getShopBaseInfoByKdtId', [kdtId], { raw: true });
  }

  /**
   *
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/8985
   *  shopcenter-api是thrift生成的，不支持继承plainresult @石斌
   *  @param {number} kdtId -
   *  @return {Promise}
   */
  async getPaymentByKdtId(kdtId) {
    return this.shopcenterApiInvoke('getPaymentByKdtId', [kdtId], { allowBigNumberInJSON: true });
  }
}

module.exports = ShopBaseReadService;
