const BaseService = require('../../../base/BaseService');

/**
 * 连锁店铺搜索
 */
class ShopChainSearchService extends BaseService {
  get SERVICE_NAME() {
    return 'com.youzan.shopcenter.shopfront.api.service.chain.ShopChainSearchService';
  }

  /**
   *  搜索后代店铺
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/416248
   *
   *  @param {Object} request - {@link DescendentShopSearchRequest}
   *  @param {boolean} request.realTimeMode - 是否实时数据模式（强制走db）
   *  @param {string} request.sortName - 排序字段
   *  @param {string} request.lng - 经度
   *  @param {string} request.city - 城市
   *  @param {number} request.hqKdtId - 总部kdtId
   *  @param {Array.<Array>} request.subKdtIdList[] - 指定店铺kdtId集合
   *  @param {string} request.shopName - 检索:店铺名称（支持模糊）
   *  @param {number} request.pageSize - 分页大小
   *  @param {boolean} request.offlineOpen - 是否开启门店
   *  @param {boolean} request.onlineOpen - 是否开启网店
   *  @param {string} request.managerKeyword - 检索:负责人账号or名称（支持模糊）
   *  @param {number} request.pageNum - 页码
   *  @param {Array.<Array>} request.shopLifecycleStatuses - 检索:店铺生命周期状态
   *  @param {string} request.province - 省份
   *  @param {number} request.sortType - 1 升序 2 降序
   *  @param {number} request.adminId - 店铺管理员id
   *  @param {Array.<Array>} request.joinTypes - 检索:加盟类型
   *  @param {Array.<Array>} request.shopRoleList - 店铺角色列表
   *  @param {string} request.lat - 纬度
   *  @return {Promise}
   */
  async searchDescendentShop(request) {
    return this.invoke('searchDescendentShop', [request]);
  }
}

module.exports = ShopChainSearchService;
