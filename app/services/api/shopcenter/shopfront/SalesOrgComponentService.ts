import BaseService from '../../../base/BaseService';

class SalesOrgComponentService extends BaseService {
  SERVICE_NAME = 'com.youzan.shopcenter.shopfront.api.service.org.SalesOrgComponentService';

  /**
   *  搜索组织，需指定起始组织id
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/1285383
   *
   *  @param {Object} request -
   *  @param {Array.<Array>} request.abilities[] - 能力列表
   *  @param {Object} request.orgExtendSearchDTO - 拓展信息搜索
   *  @param {Array.<Array>} request.orgTypes[] - 组织单元类型
   *  @param {boolean} request.isIncludeMyself - 是否包含自身
   *  @param {string} request.orgName - 组织名称
   *  @param {number} request.startOrgId - 起始组织单元id
   *  @param {number} request.limit - 数量限制
   *  @param {number} request.orgId - 组织单元id，往下搜索
   *  @param {boolean} request.recursive - 是否递归往下搜索，默认不递归
   *  @return {Promise}
   */
  async searchOrgWithStart(request) {
    return this.invoke('searchOrgWithStart', [request]);
  }
}

export = SalesOrgComponentService;
