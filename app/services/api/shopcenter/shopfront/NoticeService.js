const BaseService = require('../../../base/BaseService');

/** com.youzan.shopcenter.shopfront.api.service.notice.NoticeService -  */
class NoticeService extends BaseService {
  get SERVICE_NAME() {
    return 'com.youzan.shopcenter.shopfront.api.service.notice.NoticeService';
  }

  /**
   *  查询通知列表
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/717310
   *
   *  @param {object} params
   *  @param {number} params.kdtId - 店铺Id
   *  @param {number} params.adminId - 员工账号Id
   *  @param {number} params.pageId - 页面类型
   *  @param {number} params.terminalType - 来源终端类型
   *  @return {Promise}
   */
  async queryActiveNotice(params) {
    return this.invoke('queryActiveNotice', [params]);
  }

  /**
   *  标记已读
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/717312
   *
   *  @param {object} params
   *  @param {number} params.kdtId - 店铺Id
   *  @param {number} params.adminId - 员工账号Id
   *  @param {number} params.noticeId - 通知Id
   *  @return {Promise}
   */
  async markRead(params) {
    return this.invoke('markRead', [params]);
  }

  /**
   *  标记未读
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/717313
   *
   *  @param {number} params
   *  @param {number} params.kdtId - 店铺Id
   *  @param {number} params.adminId - 员工账号Id
   *  @param {number} params.noticeId - 通知Id
   *  @return {Promise}
   */
  async markIgnore(params) {
    return this.invoke('markIgnore', [params]);
  }
}

module.exports = NoticeService;
