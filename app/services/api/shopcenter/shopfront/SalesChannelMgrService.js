const BaseService = require('../../../base/BaseService');

/**
 * 店铺渠道
 */
class SalesChannelMgrService extends BaseService {
  get SERVICE_NAME() {
    return 'com.youzan.shopcenter.shopfront.api.service.channel.SalesChannelMgrService';
  }

  /**
   *  是否命中销售渠道白名单
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/1295943
   *
   *  @param {number} kdtId -
   *  @return {Promise}
   */
  async isHitWhiteList(kdtId) {
    return this.invoke('isHitWhiteList', [kdtId]);
  }

  /**
   *  查询外卖店铺托管配置
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/1586398
   *
   *  @param {number} kdtId -
   *  @return {Promise}
   */
  async queryWmShopHostConfig(kdtId) {
    return this.invoke('queryWmShopHostConfig', [kdtId]);
  }

  /**
   *  设置外卖店铺托管配置
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/1586399
   *
   *  @param {Object} request -
   *  @param {Object} request.channelAiConfigDetailDTO - 托管内容
   *  @param {number} request.kdtId -
   *  @param {number} request.shopRole - 店铺角色 0单店 1总部
   *  @param {boolean} request.enable - 店铺托管运行状态
   *  @param {string} request.name - 店铺托管业务名称
   *  @param {string} request.description - 店铺托管业务备注
   *  @return {Promise}
   */
  async updateWmShopHostConfig(request) {
    return this.invoke('updateWmShopHostConfig', [request]);
  }

  /**
   *  查询外卖店铺的所有渠道的托管状态
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/1586400
   *
   *  @param {Object} request -
   *  @param {number} request.agentId -
   *  @param {number} request.kdtId -
   *  @return {Promise}
   */
  async queryWmChannelHostRunStatus(request) {
    return this.invoke('queryWmChannelHostRunStatus', [request]);
  }
}

module.exports = SalesChannelMgrService;
