const BaseService = require('../../../base/BaseService');
/**
 * com.youzan.shopcenter.shopfront.api.service.chain.ShopVisitService
 */
class ShopVisitService extends BaseService {
  get SERVICE_NAME() {
    return 'com.youzan.shopcenter.shopfront.api.service.chain.ShopVisitService';
  }

  /**
   *  判断是否在白名单内
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/1037137
   *
   *  @param {number} KdtId -
   *  @return {Promise}
   */
  async checkShopVisitWhiteList(KdtId) {
    return this.invoke('checkShopVisitWhiteList', [KdtId]);
  }
}

module.exports = ShopVisitService;
