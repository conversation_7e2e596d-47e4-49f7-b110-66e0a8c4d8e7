const BaseService = require('../../../base/BaseService');

/** com.youzan.shopcenter.shopprod.api.service.lifecycle.LifecycleReadService -  */
class LifecycleReadService extends BaseService {
  get SERVICE_NAME() {
    return 'com.youzan.shopcenter.shopprod.api.service.lifecycle.LifecycleReadService';
  }

  /**
   *  查询店铺生命周期
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/1375556
   *
   *  @param {number} kdtId - 店铺kdtId
   *  @return {Promise}
   */
  async queryShopLifecycle(kdtId) {
    return this.invoke('queryShopLifecycle', [kdtId]);
  }
}

module.exports = LifecycleReadService;
