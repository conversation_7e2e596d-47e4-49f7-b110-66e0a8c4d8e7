const BaseService = require('../../../base/BaseService');

/**
 * @class SalesOrgCategoryService
 * @extends {BaseService}
 */
class SalesOrgCategoryService extends BaseService {
  get SERVICE_NAME() {
    return 'com.youzan.shopcenter.outer.service.org.SalesOrgCategoryService';
  }

  /**
   *  查询单个组织分类
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/1304086
   *
   *  @param {number} orgId - 组织id
   *  @return {Promise}
   */
  async queryOne(orgId) {
    return this.invoke('queryOne', [orgId]);
  }

  /**
   *  查询总部下面的组织分类（去重）
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/1443309
   *
   *  @param {number} hqOrgId -
   *  @return {Promise}
   */
  async querySubShopOrgCategoryByMu(hqOrgId) {
    return this.invoke("querySubShopOrgCategoryByMu", [hqOrgId]);
  }
}

module.exports = SalesOrgCategoryService;
