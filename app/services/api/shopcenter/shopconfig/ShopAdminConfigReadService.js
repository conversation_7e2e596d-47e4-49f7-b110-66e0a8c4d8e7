const BaseService = require('../../../base/BaseService');

/** com.youzan.shopcenter.shopconfig.api.service.ShopAdminConfigReadService -  */
class ShopAdminConfigReadService extends BaseService {
  get SERVICE_NAME() {
    return 'com.youzan.shopcenter.shopconfig.api.service.ShopAdminConfigReadService';
  }

  /**
   *  查询单个店铺下管理员单个偏好配置
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/155974
   *
   *  @param {number} kdtId - 店铺kdtId
   *  @param {number} adminId - 管理员id
   *  @param {string} key - 配置项key
   *  @return {Promise}
   */
  async queryShopAdminConfig(kdtId, adminId, key) {
    return this.invoke('queryShopAdminConfig', [kdtId, adminId, key]);
  }
}

module.exports = ShopAdminConfigReadService;
