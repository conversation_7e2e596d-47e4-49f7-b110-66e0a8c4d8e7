const BaseService = require('../../../base/BaseService');

/** com.youzan.shopcenter.shopconfig.api.service.ShopAdminConfigWriteService -  */
class ShopAdminConfigWriteService extends BaseService {
  get SERVICE_NAME() {
    return 'com.youzan.shopcenter.shopconfig.api.service.ShopAdminConfigWriteService';
  }

  /**
   *  修改单个店铺管理员偏好配置
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/155979
   *
   *  @param {Object} shopAdminConfigSetRequest - {@link ShopAdminConfigSetRequest}
   *  @param {number} shopAdminConfigSetRequest.kdtId - 店铺kdtId
   *  @param {number} shopAdminConfigSetRequest.adminId - 管理员id
   *  @param {string} shopAdminConfigSetRequest.value - 配置value
   *  @param {string} shopAdminConfigSetRequest.key - 配置key
   *  @param {Object} shopAdminConfigSetRequest.operator - 操作人信息
   *  @return {Promise}
   */
  async setShopAdminConfig(shopAdminConfigSetRequest) {
    return this.invoke('setShopAdminConfig', [shopAdminConfigSetRequest]);
  }
}

module.exports = ShopAdminConfigWriteService;
