const BaseService = require('../../../base/BaseService');

/** com.youzan.shopcenter.shopconfig.api.service.ShopConfigReadService -  */
class ShopConfigReadService extends BaseService {
  get SERVICE_NAME() {
    return 'com.youzan.shopcenter.shopconfig.api.service.ShopConfigReadService';
  }

  /**
   *  查询单个店铺多个配置
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/76609
   *
   *  @param {number} kdtId - 店铺kdtId
   *  @param {Array} keys - 配置项key列表
   *  @return {Promise}
   */
  async queryShopConfigs(kdtId, keys) {
    return this.invoke('queryShopConfigs', [kdtId, keys]);
  }

  /**
   *  查询单个店铺单个配置
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/76608
   *
   *  @param {number} kdtId - 店铺kdtId
   *  @param {string} key - 配置项key
   *  @return {Promise}
   */
  async queryShopConfig(kdtId, key) {
    return this.invoke('queryShopConfig', [kdtId, key]);
  }
}

module.exports = ShopConfigReadService;
