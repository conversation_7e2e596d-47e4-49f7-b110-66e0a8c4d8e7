const BaseService = require('../../base/BaseService');

/**
 * 概况页图片广告Service
 * @class DashBoardIndexQrAdService
 * @extends {BaseService}
 */
class DashBoardIndexQrAdService extends BaseService {
  /**
   * 概况页图片广告Service
   * @readonly
   * @memberof DashBoardIndexQrAdService
   */
  get SERVICE_NAME() {
    return 'com.youzan.ebiz.mall.sop.api.service.DashBoardIndexQrAdService';
  }

  /**
   *  http://zanapi.qima-inc.com/site/service/view/163708
   *  获取概况页海景房数据
   *  @param {integer} kdtId
   *  @return {object}
   */
  async listQrAdVertData(kdtId) {
    return this.invoke('listQrAdVertData', [kdtId]);
  }
}

module.exports = DashBoardIndexQrAdService;
