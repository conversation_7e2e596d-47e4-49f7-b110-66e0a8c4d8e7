const BaseService = require('../../base/BaseService');

/**
 * 店铺新手任务
 */
class ShopTaskApiService extends BaseService {
  /**
   * 获取 Dubbo 服务名
   * @return {string}
   * @constructor
   */
  get SERVICE_NAME() {
    return 'com.youzan.ebiz.mall.sop.api.service.ShopTaskApiService';
  }

  /**
   * 获取店铺新手任务
   * @param {{ kdtId: number }} param - 店铺Id
   * @return {Object} FullTask - 店铺任务
   *
   * ZanAPI Doc: http://zanapi.qima-inc.com/site/service/view/141548
   */
  getShopTask(param) {
    return this.invoke('getShopTask', [param]);
  }
}

module.exports = ShopTaskApiService;
