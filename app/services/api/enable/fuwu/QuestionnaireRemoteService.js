const BaseService = require('../../../base/BaseService');

/**
 * @class QuestionnaireRemoteService
 * @extends {BaseService}
 */
class QuestionnaireRemoteService extends BaseService {
  get SERVICE_NAME() {
    return 'com.youzan.enable.fuwu.api.service.businessprofile.QuestionnaireRemoteService';
  }

  /**
   *  控制商业化是否展示问卷入口
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/224386
   *
   *  @param {number} kdtId - 店铺Id
   *  @return {object}
   */
  async getYopQuestionnaireStatus(kdtId) {
    return this.invoke('getYopQuestionnaireStatus', [kdtId]);
  }
}

module.exports = QuestionnaireRemoteService;
