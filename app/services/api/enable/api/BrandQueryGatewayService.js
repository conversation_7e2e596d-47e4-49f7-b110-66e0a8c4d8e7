/* eslint-disable valid-jsdoc */
const BaseService = require('../../../base/BaseService');

/** 品牌搜集查询 */
class BrandQueryGatewayService extends BaseService {
  get SERVICE_NAME() {
    return 'com.youzan.enable.api.gateway.api.customerbrand.BrandQueryGatewayService';
  }

  /**
   * @description 获取所有类目
   * @link http://zanapi.qima-inc.com/site/service/view/954395
   */
  async getCategories() {
    return this.invoke('getCategories', []);
  }

  /**
   * @description 根据品牌名模糊查询品牌库内已有的品牌词
   * @link http://zanapi.qima-inc.com/site/service/view/957229
   */
  async searchBrandName(req) {
    return this.invoke('searchBrandName', [req]);
  }

  /**
   * @description 根据品牌名精确查询品牌库内的品牌信息
   * @link http://zanapi.qima-inc.com/site/service/view/957230
   */
  async getBrandByName(req) {
    return this.invoke('getBrandByName', [req]);
  }
}

module.exports = BrandQueryGatewayService;
