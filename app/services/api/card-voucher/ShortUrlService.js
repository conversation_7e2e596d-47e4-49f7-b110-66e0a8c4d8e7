/**
 * ShortUrlService 类用于生成短链接。
 * @extends BaseService
 */
class ShortUrlService extends require('../../base/BaseService') {
  /**
   * 获取域名标识。
   * @returns {string} 域名标识符。
   */
  get DOMAIN_NAME() {
    return 'API_SHORT';
  }

  /**
   * 获取短链接。
   * @param {string} url - 需要缩短的长链接。
   * @returns {Promise<Object>} 包含短链接的结果对象。
   */
  async getShortUrl(url) {
    return this.ajax(
      {
        url: '/shorten',
        data: {
          longUrl: url,
        },
      },
      {
        processCb(resp) {
          if (resp.status_code === 200) {
            return {
              code: 200,
              msg: 'ok',
              data: resp.data,
            };
          }
          return resp;
        },
      }
    );
  }
}

module.exports = ShortUrlService;
