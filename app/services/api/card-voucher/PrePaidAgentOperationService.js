const BaseService = require('../../base/BaseService');

/**
 * 储值智能体
 */
class PrePaidAgentOperationService extends BaseService {
  /**
   * SERVICE_NAME
   * @return {string}
   * @constructor
   */
  get SERVICE_NAME() {
    return 'com.youzan.pay.cardvoucher.biz.api.agent.AgentOperationService';
  }

    /**
   *  保持智能体的详情
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/1598973
   *
   *  @param {Object} request -
   *  @param {string} request.activityNo - 活动编号
   *  @param {string} request.content - 保存的json数据
   *  @return {Promise}
   */
    async saveCardValueAgentDetail(request) {
      return this.invoke("saveCardValueAgentDetail", [request]);
    }

  /**
   * 储值智能体的操作（启用、暂停、失效）
   * https://zanapi.qima-inc.com/site/service/view/1584038
   * @param {*} params
   * @returns
   */
  async operateAgent(params) {
    const result = await this.invoke('operateAgent', [params]);
    return result;
  }

  /**
   * 创建储值智能体
   * https://zanapi.qima-inc.com/site/service/view/1584037
   * @param {*} params
   * @returns
   */
  async createAgent(params) {
    const result = await this.invoke('createAgent', [params]);
    return result;
  }

  /**
   * 检查智能体活动的时间范围是否合法
   * @param {*} params
   * @param {*} params.kdtId
   * @returns
   */
  async checkCreateAgentTimeRange(params) {
    const result = await this.invoke('checkCreateAgentTimeRange', [params]);
    return result;
  }

  /**
   * 智能体的命令执行回调任务
   * https://zanapi.qima-inc.com/site/service/view/1584041
   * @param {*} params
   * @param {*} params.kdtId
   * @returns
   */
  async executeCommandCallbackTask(params) {
    const result = await this.invoke('executeCommandCallbackTask', [params]);
    return result;
  }
}
module.exports = PrePaidAgentOperationService;
