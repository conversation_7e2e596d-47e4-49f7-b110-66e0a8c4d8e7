const BaseService = require('../../base/BaseService');

/**
 * 储值智能体
 */
class PrePaidAgentQueryService extends BaseService {
  /**
   * SERVICE_NAME
   * @return {string}
   * @constructor
   */
  get SERVICE_NAME() {
    return 'com.youzan.pay.cardvoucher.biz.api.agent.AgentQueryService';
  }

  /**
   *  获取智能体的详情
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/1598965
   *
   *  @param {Object} request -
   *  @param {string} request.activityNo - 活动编号
   *  @return {Promise}
   */
  async getCardValueAgentDetail(request) {
    return this.invoke('getCardValueAgentDetail', [request]);
  }

  /**
   *  查询储值智能体单个活动数据
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/1598190
   *
   *  @param {Object} request -
   *  @param {number} request.kdtId -
   *  @param {string} request.activityNo -
   *  @return {Promise}
   */
  async queryAgentActivityData(request) {
    return this.invoke('queryAgentActivityData', [request]);
  }

  /**
   *  查询储值智能体数据
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/1598189
   *
   *  @param {Object} request -
   *  @param {number} request.kdtId -
   *  @return {Promise}
   */
  async queryAgentActivityAllData(request) {
    return this.invoke('queryAgentActivityAllData', [request]);
  }

  /**
   *  查询储值智能体单个趋势数据
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/1598191
   *
   *  @param {Object} request -
   *  @param {number} request.kdtId -
   *  @param {string} request.activityNo -
   *  @return {Promise}
   */
  async queryAgentActivityTrendData(request) {
    return this.invoke('queryAgentActivityTrendData', [request]);
  }

  /**
   * 创建智能体前的检查
   * https://zanapi.qima-inc.com/site/service/view/1585003
   * @param {*} params
   * @param {*} params.kdtId
   * @returns
   */
  async checkBeforeCreateAgent(params) {
    const result = await this.invoke('checkBeforeCreateAgent', [params]);
    return result;
  }

  /**
   * https://zanapi.qima-inc.com/site/service/view/1585152
   * 检查短信是否足够
   * @param {*} params
   * @returns
   */
  async checkSmsBeforeCreateAgent(params) {
    const result = await this.invoke('checkSmsBeforeCreateAgent', [params]);
    return result;
  }

  /**
   * 打开智能体前的检查
   * https://zanapi.qima-inc.com/site/service/view/1584040
   * @param {*} params
   * @param {*} params.kdtId
   * @returns
   */
  async checkBeforeOpenAgent(params) {
    const result = await this.invoke('checkBeforeOpenAgent', [params]);
    return result;
  }

  /**
   * 查询创建储智能体的结果
   * @param {*} params
   * @returns
   */
  async queryCardValueAgentResult(params) {
    const result = await this.invoke('queryCardValueAgentResult', [params]);
    return result;
  }

  /**
   * 查询储值智能体列表
   * @param {*} params
   * @returns
   */
  async queryCardValueAgentList(params) {
    const result = await this.invoke('queryCardValueAgentList', [params]);
    return result;
  }

  /**
   * 储值智能体的操作（启用、暂停、失效）
   * https://zanapi.qima-inc.com/site/service/view/1584038
   * @param {*} params
   * @returns
   */
  async operateAgent(params) {
    const result = await this.invoke('operateAgent', [params]);
    return result;
  }

  /**
   * 物料的压缩包下载
   * @param {*} params
   * @param {*} params.kdtId
   * @param {*} params.activityNo 活动编号
   * @param {*} params.command 操作活动指令
   * @returns
   */
  async queryImageUrl(params) {
    const result = await this.invoke('queryImageUrl', [params], {
      timeout: 15000,
    });
    return result;
  }

  /**
   * https://zanapi.qima-inc.com/site/service/view/1585982
   * @param {*} params
   * @param {*} params.kdtId
   * @param {*} params.step 1-分析情况 2-创建计划
   * @returns
   */
  async getCardValueAgentCot(params) {
    const result = await this.invoke('getCardValueAgentCot', [params]);
    return result;
  }

  /**
   * https://zanapi.qima-inc.com/site/service/view/1585983
   * @param {*} params
   * @param {*} params.kdtId
   * @returns
   */
  async queryCardValueAgentActivity(params) {
    const result = await this.invoke('queryCardValueAgentActivity', [params], {
      timeout: 30000,
    });
    return result;
  }
}
module.exports = PrePaidAgentQueryService;
