const BaseService = require('../../base/BaseService');
/**
  com.youzan.secured.quick.service.QuickSettleService
*/
class QuickSettleService extends BaseService {
  get SERVICE_NAME() {
    return 'com.youzan.secured.quick.service.QuickSettleService';
  }

  /**
   *  查询快速回款服务状态
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/128048
   *
   *  @param {Object} request -
   *  @param {number} request.mchId - 商户号
   *  @param {number} request.kdtId - 店铺ID
   *  @param {string} request.requestId -
   *  @param {Object} request.extra -
   *  @param {number} request.operatorId - 操作人员id
   *  @return {Promise}
   */
  async query(request) {
    return this.invoke('query', [request]);
  }

  /**
   *  交易统计信息
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/128052
   *
   *  @param {Object} request -
   *  @param {number} request.mchId - 商户号
   *  @param {number} request.kdtId - 店铺ID
   *  @param {string} request.requestId -
   *  @param {Object} request.extra -
   *  @param {number} request.operatorId - 操作人员id
   *  @return {Promise}
   */
  async tradeStat(request) {
    return this.invoke('tradeStat', [request]);
  }
}

module.exports = QuickSettleService;
