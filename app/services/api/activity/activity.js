const BaseService = require('../../base/BaseService');

/** com.youzan.fx.ump.api.TimeLimitedService.getActivityStatus  **/
class TimeLimitedService extends BaseService {
  get SERVICE_NAME() {
    return 'com.youzan.fx.ump.api.TimeLimitedService';
  }

  /**
   *  获取默认授权状态
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/1187066
   *
   *  @return {object}
   */
  async getActivityStatus() {
    return this.invoke('getActivityStatus', []);
  }
}

module.exports = TimeLimitedService;
