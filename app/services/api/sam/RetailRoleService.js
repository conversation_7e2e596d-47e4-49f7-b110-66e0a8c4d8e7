const BaseService = require('../../base/BaseService');

/**
 * 角色相关
 */
class RetailRoleService extends BaseService {
  get SERVICE_NAME() {
    return 'com.youzan.sam.gateway.api.service.perm.retail.RoleService';
  }

  /**
   *
   * @param {*} params
   * @param {*} params.adminId
   * @param {*} params.kdtId
   * @param {*} params.onlineShopOpen
   * @param {*} params.roleId
   * @param {*} params.roleType
   * @param {*} params.shopRole
   */
  async deleteCustomRole(params) {
    return this.invoke('deleteCustomRole', [params]);
  }

  /**
   *
   * @param {*} params
   * @param {*} params.adminId
   * @param {*} params.kdtId
   * @param {*} params.onlineShopOpen
   * @param {*} params.roleId
   * @param {*} params.roleType
   * @param {*} params.shopRole
   */
  async addCustomizeRole(params) {
    return this.invoke('addCustomizeRole', [params]);
  }

  /**
   *
   * @param {*} params
   * @param {number} params.kdtId
   * @param {boolean} params.onlineShopOpen
   * @param {number} params.page
   * @param {number} params.pageSize
   * @param {number} params.shopRole
   * @param {boolean} params.withStaffCount
   */
  async getRetailRoleList(params) {
    return this.invoke('getRetailRoleList', [params]);
  }
}

module.exports = RetailRoleService;
