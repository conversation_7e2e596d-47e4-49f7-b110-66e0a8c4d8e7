const BaseService = require('../../base/BaseService');
/* com.youzan.shopcenter.shopfront.api.shop.operate.log.service.ShopOperateLogService -  */

class ShopOperateLogService extends BaseService {
  get SERVICE_NAME() {
    return 'com.youzan.shopcenter.shopfront.api.shop.operate.log.service.ShopOperateLogService';
  }

  /**
   *
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/432890
   *
   *  @param {Object} shopOperateLog -
   *  @param {string} shopOperateLog.bizCode -
   *  @param {string} shopOperateLog.operateTime -
   *  @param {string} shopOperateLog.operatorAccount -
   *  @param {string} shopOperateLog.openApiUrl -
   *  @param {string} shopOperateLog.operatorName -
   *  @param {string} shopOperateLog.openAppName -
   *  @param {string} shopOperateLog.platform -
   *  @param {Object} shopOperateLog.extInfo -
   *  @param {string} shopOperateLog.casUserName -
   *  @param {string} shopOperateLog.operateTargetIds -
   *  @param {string} shopOperateLog.logId -
   *  @param {string} shopOperateLog.operateTargetId -
   *  @param {string} shopOperateLog.traceId -
   *  @param {string} shopOperateLog.invokeType -
   *  @param {string} shopOperateLog.bizDesc -
   *  @param {boolean} shopOperateLog.visible -
   *  @param {number} shopOperateLog.kdtId -
   *  @param {string} shopOperateLog.logContent -
   *  @param {string} shopOperateLog.appName -
   *  @param {string} shopOperateLog.bizModule -
   *  @param {string} shopOperateLog.userAgent -
   *  @param {string} shopOperateLog.tags -
   *  @param {number} shopOperateLog.operatorUserId -
   *  @param {string} shopOperateLog.openAppId -
   *  @param {number} shopOperateLog.casId -
   *  @param {string} shopOperateLog.operatorUserRole -
   *  @param {string} shopOperateLog.clientIp -
   *  @param {string} shopOperateLog.operatorType -
   *  @return {Promise}
   */
  async log(shopOperateLog) {
    return this.invoke('log', [shopOperateLog]);
  }
}

module.exports = ShopOperateLogService;
