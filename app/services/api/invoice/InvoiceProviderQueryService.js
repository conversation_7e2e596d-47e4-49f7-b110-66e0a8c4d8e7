const BaseService = require('../../base/BaseService');

/**
 * InvoiceProviderQueryService
 */
class InvoiceProviderQueryService extends BaseService {
  /**
   * @return {string}
   */
  get SERVICE_NAME() {
    return 'com.youzan.ebiz.mall.trade.seller.api.service.invoice.InvoiceProviderQueryService';
  }

  /**
   * 查询店铺开通过的电子发票服务商
   * @param {object} param -
   * @param {number} param.kdtId - 店铺id
   * 文档: http://zanapi.qima-inc.com/site/service/view/1453301
   */
  async queryInvoiceProviderList(param) {
    return this.invoke('queryInvoiceProviderList', [
      param
    ]);
  }
}

module.exports = InvoiceProviderQueryService;
