const BaseService = require('../../../base/BaseService');

/**
 * QiniuAudioReadService
 * @class QiniuAudioReadService
 * @extends {BaseService}
 */
class QiniuAudioReadService extends BaseService {
  get SERVICE_NAME() {
    return 'com.youzan.material.materialcenter.api.service.general.audio.QiniuAudioReadService';
  }

  /**
   * 音频能否搜索校验请求
   * @param {Object} params
   * @memberof QiniuAudioReadService
   *
   * 文档: http://zanapi.qima-inc.com/site/service/view/266052
   */
  async couldSearchAudio(params) {
    return await this.invoke('couldSearchAudio', [params]);
  }

  /**
   * 分页搜索音频列表
   * 跟查询走的不同的业务线，这个是走的ES那边
   * @param {Object} params
   * @memberof QiniuAudioReadService
   *
   * 文档: http://zanapi.qima-inc.com/site/service/view/264767
   */
  async searchAudioList(params) {
    return await this.invoke('searchAudioList', [params]);
  }

  /**
   *  分页查询音频列表
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/116738
   *
   *  @param {Object} request - 音频分页查询请求
   *  @param {number} request.partnerBizType - 合作方业务类型
   *  @param {number} request.pageSize - 每页数量
   *  @param {number} request.pageNum - 页码
   *  @param {number} request.categoryId - 分组id
   *  @param {number} request.partnerBizId - 合作方业务实体id，如kdtId，userId
   *  @return {Promise}
   */
  async queryAudioList(request) {
    return this.invoke('queryAudioList', [request]);
  }

  /**
   *  按音频素材id查询音频列表
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/202159
   *
   *  @param {Object} request - 音频查询请求
   *  @param {number} request.partnerBizType - 合作方业务类型
   *  @param {Array.<Array>} request.mediaIds[] - 音频id(id数量限制1~50个之间)
   *  @param {Array} request.mediaIds[] -
   *  @param {number} request.partnerBizId - 合作方业务实体id，如kdtId，userId
   *  @return {Promise}
   */
  async queryAudioListByIds(request) {
    return this.invoke('queryAudioListByIds', [request]);
  }
}

module.exports = QiniuAudioReadService;
