const BaseService = require('../../../base/BaseService');

/**
 * QiniuImageReadService
 * @class QiniuImageReadService
 * @extends {BaseService}
 */
class QiniuImageReadService extends BaseService {
  get SERVICE_NAME() {
    return 'com.youzan.material.materialcenter.api.service.general.image.QiniuImageReadService';
  }

  /**
   *  能否搜索图片
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/266056
   *
   *  @param {Object} request - 图片能否搜索校验请求
   *  @param {number} request.partnerBizType - 合作方业务类型
   *  @param {number} request.partnerBizId - 合作方业务实体id，如kdtId，userId
   *  @return {Promise}
   */
  async couldSearchImage(request) {
    return this.invoke('couldSearchImage', [request]);
  }

  /**
   *  分页搜索图片列表
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/264707
   *
   *  @param {Object} request - 图片分页搜索请求
   *  @param {number} request.partnerBizType - 合作方业务类型
   *  @param {number} request.pageSize - 每页数量
   *  @param {string} request.keyword - 搜索关键词
   *  @param {number} request.pageNum - 页码
   *  @param {number} request.partnerBizId - 合作方业务实体id，如kdtId，userId
   *  @return {Promise}
   */
  async searchImageList(request) {
    return this.invoke('searchImageList', [request]);
  }

  /**
   *  分页查询图片列表
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/116737
   *
   *  @param {Object} request - 图片分页查询请求
   *  @param {number} request.partnerBizType - 合作方业务类型
   *  @param {number} request.pageSize - 每页数量
   *  @param {number} request.pageNum - 页码
   *  @param {number} request.categoryId - 分组id
   *  @param {number} request.partnerBizId - 合作方业务实体id，如kdtId，userId
   *  @return {Promise}
   */
  async queryImageList(request) {
    return this.invoke('queryImageList', [request]);
  }

  /**
   *  按图片素材id查询图片列表
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/202157
   *
   *  @param {Object} request - 图片查询请求
   *  @param {number} request.partnerBizType - 合作方业务类型
   *  @param {Array.<Array>} request.mediaIds[] - 图片id(id数量限制1~50个之间)
   *  @param {Array} request.mediaIds[] -
   *  @param {number} request.partnerBizId - 合作方业务实体id，如kdtId，userId
   *  @return {Promise}
   */
  async queryImageListByIds(request) {
    return this.invoke('queryImageListByIds', [request]);
  }
}

module.exports = QiniuImageReadService;
