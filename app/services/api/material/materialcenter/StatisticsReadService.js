const BaseService = require('../../../base/BaseService');
/** com.youzan.material.materialcenter.api.service.general.statistics.StatisticsReadService -  */
class StatisticsReadService extends BaseService {
  get SERVICE_NAME() {
    return 'com.youzan.material.materialcenter.api.service.general.statistics.StatisticsReadService';
  }

  /**
   *  查询腾讯视频使用量统计信息
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/232779
   *
   *  @param {Object} request - 腾讯视频使用量统计信息查询请求
   *  @param {number} request.partnerBizType - 合作方业务类型(详见枚举类型
   *  com.youzan.material.materialcenter.api.enums.partner.PartnerBizTypeEnum)
   *  @param {number} request.partnerBizId - 合作方业务实体id，如kdtId，userId
   *  @return {Promise}
   */
  async queryTencentVideoUsageStatistics(request) {
    return this.invoke('queryTencentVideoUsageStatistics', [request]);
  }

  /**
   *  查询数量统计信息
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/371257
   *
   *  @param {Object} request - 数量统计信息查询请求
   *  @param {number} request.partnerBizType - 合作方业务类型
   *  @param {number} request.partnerBizId - 合作方业务实体id，如kdtId，userId
   *  @return {Promise}
   */
  async queryNumStatistics(request) {
    return this.invoke('queryNumStatistics', [request]);
  }
}

module.exports = StatisticsReadService;
