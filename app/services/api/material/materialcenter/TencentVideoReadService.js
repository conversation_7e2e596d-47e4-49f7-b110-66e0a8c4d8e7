const BaseService = require('../../../base/BaseService');

/** com.youzan.material.materialcenter.api.service.general.video.TencentVideoReadService -  */
class TencentVideoReadService extends BaseService {
  get SERVICE_NAME() {
    return 'com.youzan.material.materialcenter.api.service.general.video.TencentVideoReadService';
  }

  /**
   *  按视频素材id查询视频列表
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/236079
   *
   *  @param {Object} request - 视频批量查询请求
   *  @param {number} request.partnerBizType - 合作方业务类型
   *  @param {Array.<Array>} request.mediaIds[] - 视频素材id(id数量限制1~50个之间)
   *  @param {Array} request.mediaIds[] -
   *  @param {number} request.partnerBizId - 合作方业务实体id，如kdtId，userId
   *  @return {Promise}
   */
  async queryVideoListByIds(request) {
    return this.invoke('queryVideoListByIds', [request]);
  }

  /**
   *  生成视频播放信息
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/236080
   *
   *  @param {Object} request - 视频播放信息生成请求
   *  @param {number} request.partnerBizType - 合作方业务类型
   *  @param {number} request.mediaId - 视频素材id
   *  @param {number} request.partnerBizId - 合作方业务实体id，如kdtId，userId
   *  @return {Promise}
   */
  async generateVideoPlayInfo(request) {
    return this.invoke('generateVideoPlayInfo', [request]);
  }

  /**
   *  分页查询视频列表
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/117275
   *
   *  @param {Object} request - 视频分页查询请求
   *  @param {boolean} request.hiddenUnavailable - 隐藏不可用的视频，true：隐藏(默认)，false：不隐藏
   *  @param {number} request.partnerBizType - 合作方业务类型
   *  @param {number} request.pageSize - 每页数量
   *  @param {number} request.sort - 排序方式 {@link TencentVideoSortEnum}
   *  @param {number} request.pageNum - 页码
   *  @param {number} request.categoryId - 分组id(为null查询所有分组)
   *  @param {boolean} request.descending - 倒序排列，true：倒序(默认)，false：顺序
   *  @param {number} request.partnerBizId - 合作方业务实体id，如kdtId，userId
   *  @return {Promise}
   */
  async queryVideoList(request) {
    return this.invoke('queryVideoList', [request]);
  }

  /**
   *  分页搜索视频列表
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/156016
   *
   *  @param {Object} request - 视频分页搜索请求
   *  @param {boolean} request.hiddenUnavailable - 隐藏不可用的视频，true：隐藏(默认)，false：不隐藏
   *  @param {number} request.partnerBizType - 合作方业务类型
   *  @param {number} request.pageSize - 每页数量
   *  @param {number} request.sort - 排序方式 {@link TencentVideoSortEnum}
   *  @param {string} request.keyword - 搜索关键词
   *  @param {number} request.pageNum - 页码
   *  @param {number} request.categoryId - 分组id(为null查询所有分组)
   *  @param {boolean} request.descending - 倒序排列，true：倒序(默认)，false：顺序
   *  @param {number} request.partnerBizId - 合作方业务实体id，如kdtId，userId
   *  @return {Promise}
   */
  async searchVideoList(request) {
    return this.invoke('searchVideoList', [request]);
  }
}

module.exports = TencentVideoReadService;
