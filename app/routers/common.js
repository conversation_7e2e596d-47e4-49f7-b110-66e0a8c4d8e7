module.exports = [
  // 批量获取店铺配置项
  ['GET', '/v4/setting/api/shopConfigs', 'common.ShopConfigController', 'getShopConfigs'],
  // 批量更新店铺配置项
  ['POST', '/v4/setting/api/shopConfigs', 'common.ShopConfigController', 'updateShopConfigs'],
  // 更新单个店铺配置项
  ['POST', '/v4/api/setting/setShopConfig.json', 'common.ShopConfigController', 'setShopConfig'],
  // 获取店铺管理员配置
  ['GET', '/v4/setting/api/shopAdminConfig', 'common.ShopAdminConfigController', 'getShopAdminConfig'],
  // 更新店铺管理员配置
  ['POST', '/v4/setting/api/shopAdminConfig', 'common.ShopAdminConfigController', 'setShopAdminConfig'],
  // 根据key获取白名单
  ['GET', '/v4/api/whitelist/grayRelease', 'common.WhiteListController', 'getGrayReleaseJson'],
  // 获取同城能力开关能力
  [
    'GET',
    '/v4/setting/api/isLocalDeliveryKdtWhite',
    'common.ShopConfigController',
    'isLocalDeliveryKdtWhite',
  ],
];
