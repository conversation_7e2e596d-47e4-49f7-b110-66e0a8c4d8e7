const { registerApp } = require('@youzan/micro-app-plugin');

module.exports = [
  ['GET', registerApp('@wsc-pc-v4/diy', '/v4/setting/diy'), 'setting.DiyController', 'getIndexHtml'],
  // @愈柯 @阿璐 授权相关接口
  ['GET', '/v4/setting/diy/api/app/list', 'setting.DiyController', 'getAuthorizationList'],
  [
    'POST',
    '/v4/setting/diy/api/device/list',
    'setting.DiyController',
    'getDeviceAuthorizationList',
  ],
  // 无容器应用授权解除授权
  ['POST', '/v4/setting/diy/api/open/authorize', 'setting.DiyController', 'authorize'],
  ['POST', '/v4/setting/diy/api/open/unAuthorize', 'setting.DiyController', 'unAuthorize'],
  // 有容器应用授权解除授权
  ['POST', '/v4/setting/diy/api/cloud/authorize', 'setting.DiyController', 'authorizeCloud'],
  ['POST', '/v4/setting/diy/api/cloud/unAuthorize', 'setting.DiyController', 'unAuthorizeCloud'],
  // 冲突检测
  ['GET', '/v4/setting/diy/api/check-conflict', 'setting.DiyController', 'checkConflict'],
  ['POST', '/v4/setting/diy/api/batch-turn', 'setting.DiyController', 'batchTurn'],
  // 默认授权
  [
    'GET',
    '/v4/setting/diy/api/cloud/silent-auth-status',
    'setting.DiyController',
    'getSlientAuthStatus',
  ],
  ['POST', '/v4/setting/diy/api/cloud/silent-auth', 'setting.DiyController', 'enableSlientAuth'],
  ['POST', '/v4/setting/diy/api/cloud/silent-unauth', 'setting.DiyController', 'disableSlientAuth'],
  [
    'POST',
    '/v4/setting/diy/api/cloud/remove-device-auth',
    'setting.DiyController',
    'removeDeviceAuth',
  ],
  ['POST', '/v4/setting/diy/api/cloud/add-device-auth', 'setting.DiyController', 'addDeviceAuth'],
  [
    'POST',
    '/v4/setting/diy/api/cloud/manage-device-auth',
    'setting.DiyController',
    'manageDeviceAuth',
  ],
  [
    'GET',
    '/v4/setting/diy/api/cloud/get-device-detail-by-id',
    'setting.DiyController',
    'getDeviceDetailById',
  ],
  [
    'GET',
    '/v4/setting/diy/api/cloud/get-device-creator-dynamic-form',
    'setting.DiyController',
    'getDeviceCreatorDynamicForm',
  ],
  ['GET', '/v4/setting/diy/api/arrears-app-list', 'setting.DiyController', 'arrearsAppList'],
];
