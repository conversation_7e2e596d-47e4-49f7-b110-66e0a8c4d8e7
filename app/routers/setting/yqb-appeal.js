module.exports = [
  [
    'GET',
    '/v4/setting/yqb-appeal/api/getAllAppealStatus',
    'setting.YqbAppealController',
    'getAllAppealStatus',
  ],
  [
    'GET',
    '/v4/setting/yqb-appeal/api/getAllRiskType',
    'setting.YqbAppealController',
    'getAllRiskType',
  ],
  [
    'GET',
    '/v4/setting/yqb-appeal/api/getAllDisposal',
    'setting.YqbAppealController',
    'getAllDisposal',
  ],
  [
    'GET',
    '/v4/setting/yqb-appeal/api/getYqbViolationList',
    'setting.YqbAppealController',
    'getYqbViolationList',
  ],
  [
    'GET',
    '/v4/setting/yqb-appeal/api/getYqbViolationDetail',
    'setting.YqbAppealController',
    'getYqbViolationDetail',
  ],
  [
    'POST',
    '/v4/setting/yqb-appeal/api/yqbAppealCommit',
    'setting.YqbAppealController',
    'yqbAppealCommit',
  ],
  [
    'GET',
    '/v4/setting/yqb-appeal/api/getAppealLogs',
    'setting.YqbAppealController',
    'getAppealLogs',
  ],
  [
    'GET',
    '/v4/setting/yqb-appeal/api/getAppealLogDetail',
    'setting.YqbAppealController',
    'getAppealLogDetail',
  ],
  [
    'GET',
    '/v4/setting/yqb-appeal/api/getYqbViolationReportDetail',
    'setting.YqbAppealController',
    'getYqbViolationReportDetail',
  ],
  [
    'POST',
    '/v4/setting/yqb-appeal/api/yqbReportCommit',
    'setting.YqbAppealController',
    'yqbReportCommit',
  ],
  [
    'GET',
    '/v4/setting/yqb-appeal/api/queryAllInspectionTypes',
    'setting.YqbAppealController',
    'queryAllInspectionTypes',
  ],
  [
    'GET',
    '/v4/setting/yqb-appeal/api/getYqbViolationReportLogDetail',
    'setting.YqbAppealController',
    'getYqbViolationReportLogDetail',
  ],
  [
    'GET',
    '/v4/setting/yqb-appeal/api/getAppealNoticeLogs',
    'setting.YqbAppealController',
    'queryYqbViolationMsgHistory',
  ],
];
