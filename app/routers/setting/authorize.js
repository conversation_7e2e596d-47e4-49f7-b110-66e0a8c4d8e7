const { registerApp } = require('@youzan/micro-app-plugin');

module.exports = [
  /** START: 验证记录 */
  [
    'GET',
    '/v4/setting/auth/cost/get-cost-list.json',
    'setting.auth.CostController',
    'getAuthCostList',
  ],
  [
    'POST',
    '/v4/setting/auth/cost/export-cost-list.json',
    'setting.auth.CostController',
    'exportAuthCostList',
  ],
  [
    'GET',
    '/v4/setting/auth/cost/export-list',
    'setting.auth.CostController',
    'getCostExportListHtml',
  ],
  [
    'GET',
    '/v4/setting/auth/cost/export-list.json',
    'setting.auth.CostController',
    'getCostExportList',
  ],
  // 获取单个导出记录的下载地址
  [
    'POST',
    '/v4/setting/auth/cost/get-export-private-url.json',
    'setting.auth.CostController',
    'queryPrivateUrl',
  ],
  // 获取单个导出记录的下载记录
  [
    'GET',
    '/v4/setting/auth/cost/get-download-list.json',
    'setting.auth.CostController',
    'getListRecords',
  ],
  [
    'GET',
    '/v4/setting/auth/cost/get-reveal-config.json',
    'setting.auth.CostController',
    'getRevealConfig',
  ],
  /** END: 验证记录 */

  // 用户授权页
  [
    'GET',
    registerApp('@wsc-pc-v4/setting-authorize', ['/v4/setting/authorize', '/v4/setting/customer-pro/authorize']),
    'setting.AuthorizeController',
    'getIndexHtml',
  ],
  [
    'GET',
    '/v4/api/setting/authorize/sceneList',
    'setting.AuthorizeController',
    'getSceneModelInfoListByKdtId',
  ],
  [
    'GET',
    '/v4/api/setting/authorize/weChatAuth',
    'setting.AuthorizeController',
    'isWeChatAuthJudge',
  ],
  [
    'GET',
    '/v4/api/setting/authorize/authType',
    'setting.AuthorizeController',
    'kdtIdAuthTypeJudge',
  ],
  [
    'POST',
    '/v4/api/setting/authorize/saveScene',
    'setting.AuthorizeController',
    'saveSceneModelInfoList',
  ],

  // 获取隐私协议配置
  [
    'GET',
    '/v4/api/setting/authorize/protocol/config/info',
    'setting.AuthorizeController',
    'getProtocolAuthConfig',
  ],

  // 保存隐私协议配置
  [
    'POST',
    '/v4/api/setting/authorize/protocol/config/save',
    'setting.AuthorizeController',
    'saveProtocolAuthConfig',
  ],
];
