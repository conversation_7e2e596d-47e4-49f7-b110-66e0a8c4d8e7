const { registerApp } = require('@youzan/micro-app-plugin');

module.exports = [
  [
    'GET',
    '/v4/api/setting/ump/getActivityList.json',
    'setting.UmpController',
    'getActivityListJson',
  ],
  ['GET', '/v4/api/setting/ump/getStaffList.json', 'setting.UmpController', 'getStaffListJson'],
  ['GET', '/v4/api/setting/ump/getStaffRoles.json', 'setting.UmpController', 'getStaffRolesJson'],

  ['PUT', '/v4/api/setting/ump/editRemind.json', 'setting.UmpController', 'editRemindJson'],
  ['POST', '/v4/api/setting/ump/createRemind.json', 'setting.UmpController', 'createRemindJson'],
  ['GET', '/v4/api/setting/ump/getRemind.json', 'setting.UmpController', 'getRemindJson'],

  ['GET', registerApp('@wsc-pc-v4/setting-ump', ['/v4/setting/ump', '/v4/setting/ump/*']), 'setting.UmpController', 'getIndexHtml'],
];
