const { registerApp } = require('@youzan/micro-app-plugin');

module.exports = [
  ['GET', registerApp('@wsc-pc-v4/store-manage', '/v4/setting/store/manage'), 'setting.StoreManageController', 'getIndexHtml'],
  ['GET', '/v4/setting/store/manage/api/list', 'setting.StoreManageController', 'listByPage'],
  [
    'GET',
    '/v4/setting/store/manage/api/detail',
    'setting.StoreManageController',
    'getStoreInfoWithOption',
  ],
  ['POST', '/v4/setting/store/manage/api/create', 'setting.StoreManageController', 'create'],
  [
    'POST',
    '/v4/setting/store/manage/api/update',
    'setting.StoreManageController',
    'updateByKdtIdAndId',
  ],
  ['POST', '/v4/setting/store/manage/api/delete', 'setting.StoreManageController', 'delete'],
];
