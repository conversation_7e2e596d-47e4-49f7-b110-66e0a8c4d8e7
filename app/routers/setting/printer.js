module.exports = [
  ['GET', '/v4/setting/printer', 'setting.PrinterController', 'getIndexHtml'],
  ['GET', '/v4/setting/printers.json', 'setting.PrinterController', 'getPrinterListJson'],
  ['POST', '/v4/setting/printer.json', 'setting.PrinterController', 'postPrinterJson'],
  ['PUT', '/v4/setting/printer.json', 'setting.PrinterController', 'putPrinterJson'],
  ['DELETE', '/v4/setting/printer.json', 'setting.PrinterController', 'deletePrinterJson'],
  [
    'POST',
    '/v4/setting/printer/connect.json',
    'setting.PrinterController',
    'postConnectPrinterJson',
  ],
  [
    'POST',
    '/v4/setting/printer/disconnect.json',
    'setting.PrinterController',
    'postDisconnectPrinterJson',
  ],
  ['POST', '/v4/setting/printer/test.json', 'setting.PrinterController', 'postPrinterAssertJson'],

  // 小票样式相关接口
  ['GET', '/v4/setting/printer/receipt.json', 'setting.PrinterController', 'getReceiptJson'],
  ['GET', '/v4/setting/printer/receipts.json', 'setting.PrinterController', 'getReceiptListJson'],
  ['POST', '/v4/setting/printer/receipt.json', 'setting.PrinterController', 'postReceiptJson'],
  ['DELETE', '/v4/setting/printer/receipt.json', 'setting.PrinterController', 'deleteReceiptJson'],
  ['PUT', '/v4/setting/printer/receipt.json', 'setting.PrinterController', 'putReceiptJson'],

  // 商品分组
  ['GET', '/v4/setting/printer/goods-group.json', 'setting.PrinterController', 'getGoodsGroupJson'],

  // 小票升级
  ['POST', '/v4/setting/printer/upgrade.json', 'setting.PrinterController', 'upgradePrinter'],
  // 获取小票升级状态
  [
    'GET',
    '/v4/setting/printer/upgrade-status.json',
    'setting.PrinterController',
    'getUpgradeStatus',
  ],
];
