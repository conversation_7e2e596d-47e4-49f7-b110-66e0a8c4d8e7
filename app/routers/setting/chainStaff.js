module.exports = [
  // 连锁版员工管理
  ['GET', '/v4/setting/chainstaff', 'setting.ChainStaffController', 'getIndexHtml'],
  [
    'GET',
    '/v4/api/setting/chainstaff/detail.json',
    'setting.ChainStaffController',
    'getStaffDetailJSON',
  ],
  [
    'GET',
    '/v4/api/setting/chainstaff/queryOrgList.json',
    'setting.ChainStaffController',
    'getStaffOrgList',
  ],
  [
    'GET',
    '/v4/api/setting/chainstaff/isInRegionalGrayList.json',
    'setting.ChainStaffController',
    'isInRegionalGrayList',
  ],
  [
    'GET',
    '/v4/api/setting/chainstaff/list.json',
    'setting.ChainStaffController',
    'getStaffListJSON',
  ],
  [
    'POST',
    '/v4/api/setting/chainstaff/changeChainShopKeeper.json',
    'setting.ChainStaffController',
    'changeChainShopKeeper',
  ],
  ['POST', '/v4/api/setting/chainstaff/delete', 'setting.ChainStaffController', 'delete'],
  ['POST', '/v4/api/setting/chainstaff/create', 'setting.ChainStaffController', 'create'],
  ['POST', '/v4/api/setting/chainstaff/update', 'setting.ChainStaffController', 'update'],
  [
    'POST',
    '/v4/setting/chainstaff/api/batchDelete',
    'setting.StaffBatchOperateController',
    'chainBatchDelete',
  ],
  [
    'POST',
    '/v4/setting/chainstaff/api/batchEnable',
    'setting.StaffBatchOperateController',
    'batchEnable',
  ],
  [
    'POST',
    '/v4/setting/chainstaff/api/batchDisable',
    'setting.StaffBatchOperateController',
    'batchDisable',
  ],
  [
    'GET',
    '/v4/setting/chainstaff/api/listStaffDetailInfo',
    'setting.StaffBatchOperateController',
    'listStaffDetailInfo',
  ],
  [
    'GET',
    '/v4/setting/chainstaff/api/listStaffDeleteHooks',
    'setting.StaffBatchOperateController',
    'listStaffDeleteHooks',
  ],
  [
    'GET',
    '/v4/setting/chainstaff/api/queryListStaffDetailInfoResult',
    'setting.StaffBatchOperateController',
    'queryListStaffDetailInfoResult',
  ],
  [
    'GET',
    '/v4/setting/chainstaff/api/queryBatchDeleteResult',
    'setting.StaffBatchOperateController',
    'queryBatchDeleteResult',
  ],

  ['POST', '/v4/api/setting/chainstaff/role/add.json', 'setting.ChainStaffController', 'addRole'],
  [
    'POST',
    '/v4/api/setting/chainstaff/role/update.json',
    'setting.ChainStaffController',
    'updateRole',
  ],
  [
    'POST',
    '/v4/api/setting/chainstaff/role/delete.json',
    'setting.ChainStaffController',
    'deleteRole',
  ],

  [
    'POST',
    '/v4/api/setting/chainstaff/role/retail-save.json',
    'setting.ChainStaffController',
    'retailSaveRole',
  ],
  [
    'POST',
    '/v4/api/setting/chainstaff/role/retail-delete.json',
    'setting.ChainStaffController',
    'retailDeleteRole',
  ],
  [
    'GET',
    '/v4/api/setting/chainstaff/queryDescendentShopUsage',
    'setting.ChainStaffController',
    'queryDescendentShopUsage',
  ],
  ['GET', '/v4/api/setting/chainstaff/getShopList', 'setting.ChainStaffController', 'getShopList'],
  [
    'GET',
    '/v4/api/setting/chainstaff/searchShopList',
    'setting.ChainStaffController',
    'searchShopList',
  ],
  [
    'GET',
    '/v4/api/setting/chainstaff/getShopRoleList',
    'setting.ChainStaffController',
    'getShopRoleList',
  ],
  [
    'GET',
    '/v4/api/setting/chainstaff/queryShopAbilityInfo',
    'setting.ChainStaffController',
    'queryShopAbilityInfo',
  ],
  [
    'GET',
    '/v4/api/setting/chainstaff/queryStaffInfoByKdtId',
    'setting.ChainStaffController',
    'queryStaffInfoByKdtId',
  ],
];
