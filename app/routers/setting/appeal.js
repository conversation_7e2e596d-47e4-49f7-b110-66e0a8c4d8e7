const { registerApp } = require('@youzan/micro-app-plugin');

module.exports = [
  // 页面路由
  ['GET', '/v4/setting/appeal/wx-appeal/appeal*', 'setting.WxAppealController', 'getDetailHtml'],
  ['GET', '/v4/setting/appeal/wx-appeal/supplement', 'setting.WxAppealController', 'getDetailHtml'],
  ['GET', '/v4/setting/appeal/wx-appeal/detail', 'setting.WxAppealController', 'getDetailHtml'],

  ['GET', registerApp('@wsc-pc-v4/setting-appeal', ['/v4/setting/appeal*']), 'setting.AppealController', 'getIndexHtml'],
  ['GET', '/v4/api/setting/appeal/deduct/list', 'setting.AppealController', 'getDeductList'],
  [
    'POST',
    '/v4/api/setting/appeal/deduct/cancel',
    'setting.AppealController',
    'cancelDeductAppeal',
  ],
  ['GET', '/v4/api/setting/appeal/deduct/detail', 'setting.AppealController', 'getDeductDetail'],
  ['GET', '/v4/api/setting/appeal/deduct/score', 'setting.AppealController', 'getDeductScore'],
  ['GET', '/v4/api/setting/appeal/deduct/history', 'setting.AppealController', 'getDeductHistory'],
  ['GET', '/v4/api/setting/appeal/punish/list', 'setting.AppealController', 'getPunishList'],
  ['GET', '/v4/api/setting/appeal/getMerchantWhite', 'setting.AppealController', 'getMerchantWhite'],
  ['POST', '/v4/api/setting/appeal/addMerchantWhite', 'setting.AppealController', 'addMerchantWhite'],
  ['POST', '/v4/api/setting/appeal/updateMerchantWhite', 'setting.AppealController', 'updateMerchantWhite'],
  [
    'POST',
    '/v4/api/setting/appeal/punish/cancel',
    'setting.AppealController',
    'cancelPunishAppeal',
  ],
  ['GET', '/v4/api/setting/appeal/punish/detail', 'setting.AppealController', 'getPunishDetail'],
  ['GET', '/v4/api/setting/appeal/punish/history', 'setting.AppealController', 'getPunishHistory'],
  ['GET', '/v4/api/setting/appeal/punish/limit-list', 'setting.AppealController', 'getLimitList'],

  ['GET', '/v4/api/setting/appeal/early-warn/list', 'setting.AppealController', 'getWarnList'],
  [
    'GET',
    '/v4/api/setting/appeal/early-warn/queryWarnItems',
    'setting.AppealController',
    'queryWarnItems',
  ],
  ['GET', '/v4/api/setting/appeal/punish/queryItems', 'setting.AppealController', 'queryItems'],
  ['GET', '/v4/api/setting/appeal/queryGoodsExist', 'setting.AppealController', 'queryGoodsExist'],
  [
    'GET',
    '/v4/api/setting/appeal/violation/overview',
    'setting.AppealController',
    'getViolationOverview',
  ],
  [
    'GET',
    '/v4/api/setting/appeal/violation/score',
    'setting.AppealController',
    'queryTotalByKdtId',
  ],
  [
    'GET',
    '/v4/api/setting/appeal/queryRefRelationPageList',
    'setting.AppealController',
    'queryRefRelationPageList',
  ],
];
