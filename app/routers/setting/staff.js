module.exports = [
  ['GET', '/v4/setting/staff', 'setting.StaffController', 'getIndexHtml'],
  ['GET', '/v4/setting/role', 'setting.StaffController', 'getRoleHtml'],
  ['GET', '/v4/setting/staff/api/getRoleList.json', 'setting.StaffController', 'getRoleList'],
  ['GET', '/v4/setting/staff/api/findStaff.json', 'setting.StaffController', 'findStaff'],
  ['GET', '/v4/setting/staff/api/getFreeRoles.json', 'setting.StaffController', 'getFreeRoles'],
  [
    'GET',
    '/v4/setting/staff/api/isShopDisabledAllStaffsForExpire.json',
    'setting.StaffController',
    'isShopDisabledAllStaffsForExpire',
  ],
  [
    'GET',
    '/v4/setting/staff/api/clearShopDisabledAllStaffsForExpireRecord.json',
    'setting.StaffController',
    'clearShopDisabledAllStaffsForExpireRecord',
  ],
  ['POST', '/v4/setting/staff/api/enable.json', 'setting.StaffController', 'enable'],
  ['POST', '/v4/setting/staff/api/disable.json', 'setting.StaffController', 'disable'],
  [
    'GET',
    '/v4/setting/staff/api/findStaffDeleteHooks.json',
    'setting.StaffController',
    'findStaffDeleteHooks',
  ],
  ['POST', '/v4/setting/staff/api/delete.json', 'setting.StaffController', 'delete'],
  [
    'POST',
    '/v4/setting/staff/api/changeShopKeeper.json',
    'setting.StaffController',
    'changeShopKeeper',
  ],
  [
    'POST',
    '/v4/setting/staff/api/queryTaskStatusById.json',
    'setting.StaffController',
    'queryTaskStatusById',
  ],
  [
    'GET',
    '/v4/setting/staff/api/getStaffAbility.json',
    'setting.StaffController',
    'getStaffAbility',
  ],
  ['GET', '/v4/setting/staff/api/getRolePerm.json', 'setting.StaffController', 'getRolePerm'],
  ['GET', '/v4/setting/staff/api/get.json', 'setting.StaffController', 'get'],
  ['POST', '/v4/setting/staff/api/create.json', 'setting.StaffController', 'create'],
  ['POST', '/v4/setting/staff/api/update.json', 'setting.StaffController', 'update'],
  [
    'POST',
    '/v4/setting/staff/api/addCustomizeRole.json',
    'setting.StaffController',
    'addCustomizeRole',
  ],
  [
    'POST',
    '/v4/setting/staff/api/deleteCustomRole.json',
    'setting.StaffController',
    'deleteCustomRole',
  ],
  [
    'GET',
    '/v4/setting/staff/api/listStaffDeleteHooks',
    'setting.StaffBatchOperateController',
    'listStaffDeleteHooks',
  ],
  [
    'POST',
    '/v4/setting/staff/api/batchDelete',
    'setting.StaffBatchOperateController',
    'singleBatchDelete',
  ],
  [
    'GET',
    '/v4/setting/staff/api/pageBatchCreateTaskResult',
    'setting.StaffBatchOperateController',
    'pageBatchCreateTaskResult',
  ],
  [
    'POST',
    '/v4/setting/staff/api/submitBatchCreateTask',
    'setting.StaffBatchOperateController',
    'submitBatchCreateTask',
  ],
  [
    'GET',
    '/v4/setting/staff/api/getFileUploadToken',
    'setting.StaffBatchOperateController',
    'getFileUploadToken',
  ],
  [
    'GET',
    '/v4/setting/staff/api/pageStaffAbility',
    'setting.StaffBatchOperateController',
    'pageStaffAbility',
  ],
  [
    'GET',
    '/v4/setting/staff/api/getDownloadUrl',
    'setting.StaffBatchOperateController',
    'getDownloadUrl',
  ],
  [
    'GET',
    '/v4/setting/staff/api/querySingleBatchDeleteResult',
    'setting.StaffBatchOperateController',
    'querySingleBatchDeleteResult',
  ],
  [
    'POST',
    '/v4/setting/staff/api/updateScopeOfDataPermission',
    'setting.DataPermissionController',
    'updateScopeOfDataPermission',
  ],
  [
    'GET',
    '/v4/setting/staff/api/getScopeOfDataPermissionV2',
    'setting.DataPermissionController',
    'getScopeOfDataPermissionV2',
  ],
];
