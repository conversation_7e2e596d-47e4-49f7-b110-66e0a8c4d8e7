module.exports = [
  ['GET', '/v4/wx-appeal/api/getListBykdtId', 'setting.WxAppealController', 'getListBykdtId'],
  [
    'GET',
    '/v4/wx-appeal/api/getDetailByTransId',
    'setting.WxAppealController',
    'getDetailByTransId',
  ],
  [
    'GET',
    '/v4/wx-appeal/api/getDefaultBaseInfo',
    'setting.WxAppealController',
    'getDefaultBaseInfo',
  ],
  ['GET', '/v4/wx-appeal/api/getByStep', 'setting.WxAppealController', 'getByStep'],
  [
    'GET',
    '/v4/wx-appeal/api/listHistoryByTransId',
    'setting.WxAppealController',
    'listHistoryByTransId',
  ],
  ['POST', '/v4/wx-appeal/api/saveByStep', 'setting.WxAppealController', 'saveByStep'],
  ['POST', '/v4/wx-appeal/api/supplement', 'setting.WxAppealController', 'supplement'],
  // 页面路由
  ['GET', '/v4/setting/appeal/wx-appeal/appeal*', 'setting.WxAppealController', 'getDetailHtml'],
  ['GET', '/v4/setting/appeal/wx-appeal/supplement', 'setting.WxAppealController', 'getDetailHtml'],
  ['GET', '/v4/setting/appeal/wx-appeal/detail', 'setting.WxAppealController', 'getDetailHtml'],
  ['GET', '/v4/setting/appeal/wx-appeal*', 'setting.WxAppealController', 'getIndexHtml'],
  // 2022.08.02
  // v4商家后台查询微信处罚记录
  ['GET', '/v4/wx-appeal/api/listForMerchant', 'setting.WxAppealController', 'listForMerchant'],
  // 2022.08.18
  // v4商家后台查询店铺微信支付申诉记录
  [
    'GET',
    '/v4/wx-appeal/api/listForComplaint',
    'setting.WxComplaintController',
    'listForComplaint',
  ],
];
