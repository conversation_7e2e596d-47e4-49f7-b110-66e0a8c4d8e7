const { registerApp } = require('@youzan/micro-app-plugin');

module.exports = [
  [
    '通用设置页面',
    'GET',
    registerApp('@wsc-pc-v4/setting-team', [
      '/v4/setting/team',
      '/v4/setting/wholesale/team',
      '/v4/setting/wholesale/index',
      '/v4/setting/team/index',
      '/v4/setting/team/xhs-independent-index',
    ]),
    'setting.TeamController',
    'getIndexHtml',
  ],

  [
    '获取展示载体信息',
    'GET',
    ['/v4/api/setting/team/display-carrier'],
    'setting.TeamController',
    'getDefault',
  ],
  [
    '设置展示载体信息',
    'POST',
    ['/v4/api/setting/team/display-carrier'],
    'setting.TeamController',
    'setDefault',
  ],
  [
    '获取会员卡列表',
    'GET',
    ['/v4/api/setting/team/card/list'],
    'setting.TeamController',
    'getAvailableList',
  ],
  [
    '获取等级列表',
    'GET',
    '/v4/api/setting/team/levelGroup/list',
    'setting.TeamController',
    'getGroupDetailList',
  ],
  ['POST', '/v4/setting/api/team/setting', 'setting.TeamController', 'editSettingByKdtId'],
  ['GET', '/v4/setting/api/team/setting', 'setting.TeamController', 'getSettingByKdtId'],
  [
    '获取是否展示微信支付优惠',
    'GET',
    '/v4/setting/api/team/show/wepay/setting',
    'setting.TeamController',
    'getIsShowWepaySetting',
  ],
  ['GET', '/v4/setting/api/team/pay/disable', 'setting.TeamController', 'getSelfPayIsOpen'],
  [
    'GET',
    '/v4/setting/api/team/getPushStatus4Manager',
    'setting.TeamController',
    'getPushStatus4Manager',
  ],
];
