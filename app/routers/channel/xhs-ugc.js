module.exports = [
  [
    'GET',
    '/v4/api/xhs-ugc/get-shop-industry-type.json',
    'channel.XhsUGCController',
    'getShopIndustryType',
  ],
  ['GET', '/v4/api/xhs-ugc/is-data-switch.json', 'channel.XhsUGCController', 'isDataSwitch'],
  ['POST', '/v4/api/xhs-ugc/add.json', 'channel.XhsUGCController', 'add'],
  ['GET', '/v4/api/xhs-ugc/get-example-note.json', 'channel.XhsUGCController', 'getExampleNote'],
  ['POST', '/v4/api/xhs-ugc/init.json', 'channel.XhsUGCController', 'initActivity'],
  [
    'POST',
    '/v4/api/xhs-ugc/createActivityTest.json',
    'channel.XhsUGCController',
    'createActivityTest',
  ],
  ['POST', '/v4/api/xhs-ugc/start.json', 'channel.XhsUGCController', 'start'],
  ['POST', '/v4/api/xhs-ugc/update.json', 'channel.XhsUGCController', 'update'],
  ['GET', '/v4/api/xhs-ugc/list-of-page.json', 'channel.XhsUGCController', 'listOfPage'],
  ['GET', '/v4/api/xhs-ugc/get-by-id.json', 'channel.XhsUGCController', 'getById'],
  [
    'GET',
    '/v4/api/xhs-ugc/get-latest-version.json',
    'channel.XhsUGCController',
    'getLatestVersion',
  ],
  ['GET', '/v4/api/xhs-ugc/is-finished.json', 'channel.XhsUGCController', 'isFinished'],
  [
    'GET',
    '/v4/api/xhs-ugc/overview-of-all-activity.json',
    'channel.XhsUGCController',
    'overviewOfAllActivity',
  ],
  [
    'GET',
    '/v4/api/xhs-ugc/overview-of-activity.json',
    'channel.XhsUGCController',
    'overviewOfActivity',
  ],
  ['POST', '/v4/api/xhs-ugc/publish-callback.json', 'channel.XhsUGCController', 'publishCallback'],
  ['GET', '/v4/api/xhs-ugc/list-of-page-note.json', 'channel.XhsUGCController', 'listOfPageNote'],
  [
    'GET',
    '/v4/api/xhs-ugc/list-of-page-moment.json',
    'channel.XhsUGCController',
    'listOfPageMoment',
  ],
  ['POST', '/v4/api/xhs-ugc/async-generate.json', 'channel.XhsUGCController', 'asyncGenerate'],
  [
    'GET',
    '/v4/api/xhs-ugc/get-generate-result.json',
    'channel.XhsUGCController',
    'getGenerateResult',
  ],
];
