# 开发必读!!!开发必读!!!开发必读!!!

## 路由命名规则

本仓库存在一些杂七杂八的代码 以及 设置的页面，没有统一进行管理，导致路由的命名及其混乱，从而引发问题。
比如A页面路由设置为`/v4/A`，B页面设置为`/v4/A/B`，导航的高亮选中就会失效。

__路由约定如下:__
页面路由统一为：`/v4/xxx1/xxx2/xxx3`
接口路由统一为：`/v4/xxx1/api/xxx...`

`xxx1`: 一级导航归属
  举例: 设置(`v4/setting`)、概况(`v4/dashboard`)、店铺(`v4/store`)，
`xxx2`: 业务归属
  举例: 店铺设置(`v4/setting/store`)、订单设置(`v4/setting/trade`)
`xxx3`: 功能
  举例: 店铺信息(`v4/setting/store/shopInfo`)、联系我们(`v4/setting/store/contact`)

