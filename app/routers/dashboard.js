module.exports = [
  ['GET', '/v4/dashboard', 'dashboard.IndexController', 'getIndexHtml'],
  ['GET', '/v4/api/dashboard/isPopTip', 'scrm.ScrmShopController', 'isPopTip'],
  ['GET', '/v4/api/dashboard/overview', 'dashboard.IndexController', 'getOverviewInfo'],
  ['GET', '/v4/api/dashboard/serviceNotice', 'dashboard.IndexController', 'getServiceNotice'],
  ['DELETE', '/v4/api/dashboard/serviceNotice', 'dashboard.IndexController', 'ignoreServiceNotice'],
  ['DELETE', '/v4/api/dashboard/cancelWarning', 'dashboard.IndexController', 'cancelWarning'],
  ['GET', '/v4/api/dashboard/shopbill', 'dashboard.IndexController', 'getShopBill'],
  ['GET', '/v4/api/dashboard/fenxiaoCms', 'dashboard.IndexController', 'getFenxiaoCms'],
  ['GET', '/v4/api/dashboard/listNotice', 'dashboard.IndexController', 'listNotice'],
  ['GET', '/v4/api/dashboard/getVirusInfo.json', 'dashboard.IndexController', 'getVirusInfo'],
  ['GET', '/v4/api/dashboard/getShopBaseInfo.json', 'dashboard.IndexController', 'getShopBaseInfo'],
  ['GET', '/v4/api/dashboard/getRiskNotice.json', 'dashboard.IndexController', 'getRiskNotice'],
  [
    'GET',
    '/v4/api/dashboard/getAnnouncementNotice.json',
    'dashboard.IndexController',
    'getAnnouncementNotice',
  ],
  [
    'GET',
    '/v4/api/dashboard/getLastProduceNotice.json',
    'dashboard.IndexController',
    'getLastProduceNotice',
  ],
  ['GET', '/v4/api/dashboard/getQrAd.json', 'dashboard.IndexController', 'getQrAd'],
  ['GET', '/v4/api/dashboard/isProviderShop.json', 'dashboard.IndexController', 'isProviderShop'],
  ['GET', '/v4/api/dashboard/getSurvey.json', 'dashboard.IndexController', 'getSurvey'],
  [
    'GET',
    '/v4/api/dashboard/listSubjectForShop.json',
    'dashboard.IndexController',
    'listSubjectForShop',
  ],
  ['GET', '/v4/api/dashboard/getCmsSuggest.json', 'dashboard.IndexController', 'getCmsSuggest'],
  ['GET', '/v4/api/dashboard/getSummaryData.json', 'dashboard.IndexController', 'getSummaryData'],
  [
    'GET',
    '/v4/api/dashboard/getRetailHelpCms.json',
    'dashboard.IndexController',
    'getRetailHelpCms',
  ],
  [
    'GET',
    '/v4/api/dashboard/getAllStoresWithPerm.json',
    'dashboard.IndexController',
    'getAllStoresWithPerm',
  ],
  [
    'GET',
    '/v4/api/dashboard/getActivityStatus.json',
    'dashboard.IndexController',
    'getActivityStatus',
  ],
  [
    'GET',
    '/v4/api/dashboard/getLiveSchedule.json',
    'dashboard.IndexController',
    'getLiveSchedule',
  ],
  [
    'DELETE',
    '/v4/api/dashboard/ignoreStewardNotice',
    'dashboard.IndexController',
    'ignoreStewardNotice',
  ],
  ['POST', '/v4/api/dashboard/execute.json', 'dashboard.IndexController', 'postExecuteJson'],
  [
    'GET',
    '/v4/api/dashboard/findMerchantTaskDashBoardView.json',
    'dashboard.IndexController',
    'getFindMerchantTaskDashBoardViewJson',
  ],
  [
    'GET',
    '/v4/api/dashboard/getPrincipalCertStatus.json',
    'dashboard.IndexController',
    'getPrincipalCertStatus',
  ],
];
