module.exports = [
  ['GET', '/v4/ipr*', 'ipr.IndexController', 'getIndexHtml'],
  ['POST', '/v4/api/ipr/token', 'ipr.IndexController', 'getPublicToken'],
  ['GET', '/v4/api/ipr/isOnComplaintBlackList', 'ipr.IndexController', 'isOnComplaintBlackList'],
  ['GET', '/v4/api/ipr/complain/history', 'ipr.IndexController', 'getComplainHistory'],
  ['GET', '/v4/api/ipr/complain/record', 'ipr.IndexController', 'getComplainRecord'],
  ['GET', '/v4/api/ipr/complain/detail', 'ipr.IndexController', 'getComplainDetail'],
  ['GET', '/v4/api/ipr/complain/progress', 'ipr.IndexController', 'getComplainProgress'],
  ['POST', '/v4/api/ipr/complain/submit', 'ipr.IndexController', 'submitComplain'],
  ['GET', '/v4/api/ipr/complain/cancel', 'ipr.IndexController', 'cancelComplain'],
  ['GET', '/v4/api/ipr/complain/validate', 'ipr.IndexController', 'validateLink'],
  ['GET', '/v4/api/ipr/appeal/history', 'ipr.IndexController', 'getAppealHistory'],
  ['GET', '/v4/api/ipr/appeal/record', 'ipr.IndexController', 'getAppealRecord'],
  ['GET', '/v4/api/ipr/appeal/detail', 'ipr.IndexController', 'getAppealDetail'],
  ['GET', '/v4/api/ipr/appeal/progress', 'ipr.IndexController', 'getAppealProgress'],
  ['POST', '/v4/api/ipr/appeal/submit', 'ipr.IndexController', 'submitAppeal'],
  ['GET', '/v4/api/ipr/appeal/check', 'ipr.IndexController', 'checkAppeal'],
  ['GET', '/v4/api/ipr/shop/list', 'ipr.IndexController', 'getShopList'],
];
