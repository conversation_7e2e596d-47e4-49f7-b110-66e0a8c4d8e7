const prefixPath = '/v4/api/dashboard/guide-alert';
const controller = 'dashboard.GuideAlertController';
module.exports = [
  ['GET', `${prefixPath}/getNeedGuideAlert.json`, controller, 'getNeedGuideAlert'],
  ['GET', `${prefixPath}/getNeedWxPayAuthAlert.json`, controller, 'getNeedWxPayAuthAlert'],
  ['POST', `${prefixPath}/recordYunUpgrade.json`, controller, 'recordYunUpgrade'],
  ['POST', `${prefixPath}/buyYunUpgrade.json`, controller, 'buyYunUpgrade'],
  ['GET', `${prefixPath}/checkYunAction.json`, controller, 'checkYunAction'],
  ['POST', `${prefixPath}/markRead.json`, controller, 'markRead'],
  ['POST', `${prefixPath}/markIgnore.json`, controller, 'markIgnore'],
];
