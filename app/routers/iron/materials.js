module.exports = [
  [
    'GET',
    '/v4/api/iron/materials/categoryList.json',
    'api.MaterialsController',
    'getCategoryListJson',
  ],
  ['GET', '/v4/api/iron/materials/mediaList.json', 'api.MaterialsController', 'getMediaListJson'],
  [
    'POST',
    '/v4/api/iron/materials/shopPubImgUploadToken.json',
    'api.MaterialsController',
    'postShopPubImgUploadTokenJson',
  ],
  [
    'POST',
    '/v4/api/iron/materials/shopPubImg.json',
    'api.MaterialsController',
    'postShopPubImgJson',
  ],
  [
    'POST',
    '/v4/api/iron/materials/audioUploadToken.json',
    'api.MaterialsController',
    'postAudioUploadTokenJson',
  ],
  [
    'POST',
    '/v4/api/iron/materials/onlyInNeutronAudioUploadToken.json',
    'api.MaterialsController',
    'onlyInNeutronPostAudioUploadTokenJson',
  ],
  [
    'POST',
    '/v4/api/iron/materials/videoCategoryList.json',
    'api.MaterialsController',
    'postVideoCategoryListJson',
  ],
  ['GET', '/v4/api/iron/materials/videoList.json', 'api.MaterialsController', 'getVideoListJson'],
  [
    'POST',
    '/v4/api/iron/materials/updateVideo.json',
    'api.MaterialsController',
    'postUpdateVideoJson',
  ],
  [
    'POST',
    '/v4/api/iron/materials/videoUploadToken.json',
    'api.MaterialsController',
    'postVideoUploadTokenJson',
  ],
  [
    'POST',
    '/v4/api/iron/materials/publishVideo.json',
    'api.MaterialsController',
    'postPublishVideoJson',
  ],
  [
    'POST',
    '/v4/api/iron/materials/confirmVideoUpload.json',
    'api.MaterialsController',
    'postConfirmVideoUploadJson',
  ],
  ['GET', '/v4/api/iron/materials/videoInfo.json', 'api.MaterialsController', 'getVideoInfoJson'],
];
