const prefix = '/v4/third-plugin/prepaid-expert';
const controller = 'prepaid-expert.IndexController';

module.exports = [
  ['GET', `${prefix}/agent-detail-init.json`, controller, 'agentDetailInit'],
  ['GET', `${prefix}/agent-flow-init.json`, controller, 'agentFlowInit'],
  ['GET', `${prefix}/prepaid/list.json`, controller, 'getPrepaidList'],
  ['GET', `${prefix}/prepaid/status.json`, controller, 'getPrepaidStatus'],
  ['GET', `${prefix}/prepaid/download.json`, controller, 'queryImageUrl'],
  ['POST', `${prefix}/prepaid/change-status.json`, controller, 'changeStatus'],
  ['GET', `${prefix}/flow/create-verify.json`, controller, 'checkBeforeCreateAgent'],
  ['POST', `${prefix}/flow/create-agent.json`, controller, 'createAgent'],
  // ['GET', `${prefix}/flow/get-agent-cot.json`, controller, 'getCardValueAgentCot'],
  ['GET', `${prefix}/flow/get-agent-activity.json`, controller, 'queryCardValueAgentActivity'],
  ['GET', `${prefix}/flow/query-agent-result.json`, controller, 'queryCardValueAgentResult'],
  ['GET', `${prefix}/flow/check-agent-time-verify.json`, controller, 'checkCreateAgentTimeRange'],
  [
    'POST',
    `${prefix}/shopPubImgUploadToken.json`,
    'api.MaterialsController',
    'postShopPubImgUploadTokenJson',
  ],
  ['POST', `${prefix}/getPoster.json`, controller, 'getPoster'],
  ['GET', `${prefix}/getPreviewQrcode.json`, controller, 'getPreviewQrcode'],
  ['POST', `${prefix}/scrm/crowd/estimate.json`, controller, 'estimateV2'],
  ['POST', `${prefix}/aigc/aigcMiddleId.json`, controller, 'aigcMiddleId'],
  ['POST', `${prefix}/aigc/aigcTextInfo.json`, controller, 'aigcTextInfo'],
  ['POST', `${prefix}/uploadNetworkImage.json`, controller, 'uploadNetworkImage'],
  ['GET', `${prefix}/getPrepaidPromotionQrCode.json`, controller, 'getPrepaidPromotionQrCode'],
  [
    'GET',
    `${prefix}/queryCategoryByCategoryTypeAndMediaType.json`,
    controller,
    'queryCategoryByCategoryTypeAndMediaType',
  ],
  ['GET', `${prefix}/queryGoods.json`, controller, 'queryGoods'],
  ['GET', `${prefix}/queryGoodsGroup.json`, controller, 'queryGoodsGroup'],
  ['GET', `${prefix}/getAgentDetail.json`, controller, 'getAgentDetail'],
  ['POST', `${prefix}/saveAgentDetail.json`, controller, 'saveAgentDetail'],
  ['GET', `${prefix}/queryAgentActivityAllData.json`, controller, 'queryAgentActivityAllData'],
  ['GET', `${prefix}/queryAgentActivityData.json`, controller, 'queryAgentActivityData'],
  ['GET', `${prefix}/queryAgentActivityTrendData.json`, controller, 'queryAgentActivityTrendData'],
];
