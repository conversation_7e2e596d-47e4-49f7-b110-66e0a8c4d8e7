const prefix = '/v4/third-plugin/birthday-marketing';
const controller = 'birthday-marketing.IndexController';

module.exports = [
  ['GET', `${prefix}/list.json`, controller, 'getTaskList'],
  ['GET', `${prefix}/initial-task-info.json`, controller, 'getInitialTaskInfo'],
  ['POST', `${prefix}/create-task.json`, controller, 'createTask'],
  ['GET', `${prefix}/task-result.json`, controller, 'getTaskResult'],
  ['GET', `${prefix}/detail.json`, controller, 'getTaskDetail'],
  ['POST', `${prefix}/stop-task.json`, controller, 'stopTask'],
  ['POST', `${prefix}/aigc/aigcMiddleId.json`, controller, 'aigcMiddleId'],
  ['POST', `${prefix}/aigc/aigcTextInfo.json`, controller, 'aigcTextInfo'],
  ['GET', `${prefix}/digger-status.json`, controller, 'getDiggerStatus'],
  ['POST', `${prefix}/update-digger-status.json`, controller, 'updateDiggerStatus'],
  ['POST', `${prefix}/execute-digger-task.json`, controller, 'executeDiggerTask'],
  ['GET', `${prefix}/order-digger-result.json`, controller, 'getOrderDiggerResult'],
  ['GET', `${prefix}/agent-overview-data.json`, controller, 'getOperateData'],
  ['GET', `${prefix}/digger-data.json`, controller, 'getDiggerData'],
  ['POST', `${prefix}/check-coupon.json`, controller, 'checkCoupon'],
];
