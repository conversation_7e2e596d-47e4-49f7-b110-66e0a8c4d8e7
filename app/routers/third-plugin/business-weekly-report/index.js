// 路由路径
const ROOT_BASE_URL = '/v4/third-plugin/business-weekly-report';
// 控制器路径
const CONTROLLER_BASE_PATH = 'third-plugin.business-weekly-report';

module.exports = [
  [
    '消息推送渠道状态',
    'GET',
    `${ROOT_BASE_URL}/message/push/channel/status.json`,
    `${CONTROLLER_BASE_PATH}.MessagepushController`,
    'getChannelStatus',
  ],
  [
    '消息推送配置查询',
    'GET',
    `${ROOT_BASE_URL}/agent-report/config.json`,
    `${CONTROLLER_BASE_PATH}.AgentReportPushConfigController`,
    'queryConfigByReportId',
  ],
  [
    '消息推送配置创建',
    'POST',
    `${ROOT_BASE_URL}/agent-report/config.json`,
    `${CONTROLLER_BASE_PATH}.AgentReportPushConfigController`,
    'createConfig',
  ],
  [
    '消息推送配置更新',
    'PUT',
    `${ROOT_BASE_URL}/agent-report/config.json`,
    `${CONTROLLER_BASE_PATH}.AgentReportPushConfigController`,
    'updateConfig',
  ],

  [
    '获取报表列表',
    'GET',
    `${ROOT_BASE_URL}/agent-report/list.json`,
    `${CONTROLLER_BASE_PATH}.ArentReportDetailController`,
    'getReportList',
  ],
  [
    '获取执行记录',
    'GET',
    `${ROOT_BASE_URL}/agent-report/execution-records.json`,
    `${CONTROLLER_BASE_PATH}.ArentReportDetailController`,
    'getExecutionRecords',
  ],
  [
    '获取员工列表',
    'GET',
    `${ROOT_BASE_URL}/staff/list.json`,
    `${CONTROLLER_BASE_PATH}.AgentReportPushConfigController`,
    'getStaffList',
  ],
  [
    '获取企业微信联系人列表',
    'GET',
    `${ROOT_BASE_URL}/staff/wecom-contact-list.json`,
    `${CONTROLLER_BASE_PATH}.AgentReportPushConfigController`,
    'getWecomContactList',
  ],

  [
    '获取店铺信息',
    'GET',
    `${ROOT_BASE_URL}/shop-info.json`,
    `${CONTROLLER_BASE_PATH}.AgentInitConfigController`,
    'getShopInfo',
  ],
  [
    '初始化流程',
    'POST',
    `${ROOT_BASE_URL}/init/set-config.json`,
    `${CONTROLLER_BASE_PATH}.AgentInitConfigController`,
    'createInitConfig',
  ],
  [
    '是否已初始化',
    'GET',
    `${ROOT_BASE_URL}/init/get-config.json`,
    `${CONTROLLER_BASE_PATH}.AgentInitConfigController`,
    'queryInitConfig',
  ],

  [
    '配置业绩目标',
    'POST',
    `${ROOT_BASE_URL}/performance-target/set-config.json`,
    `${CONTROLLER_BASE_PATH}.AgentPerformanceTargetController`,
    'createConfig',
  ],
  [
    '查询业绩目标',
    'GET',
    `${ROOT_BASE_URL}/performance-target/get-config.json`,
    `${CONTROLLER_BASE_PATH}.AgentPerformanceTargetController`,
    'queryConfig',
  ],

  [
    '获取维度指标元数据',
    'GET',
    `${ROOT_BASE_URL}/system-report/dimension-index-meta.json`,
    `${CONTROLLER_BASE_PATH}.ReportController`,
    'getDimensionAndIndexMeta',
  ],
  [
    '获取报表配置',
    'GET',
    `${ROOT_BASE_URL}/system-report/data.json`,
    `${CONTROLLER_BASE_PATH}.ReportController`,
    'queryConfigById',
  ],
  [
    '创建报表配置',
    'POST',
    `${ROOT_BASE_URL}/system-report/config.json`,
    `${CONTROLLER_BASE_PATH}.ReportController`,
    'createConfig',
  ],
  [
    '更新报表配置',
    'PUT',
    `${ROOT_BASE_URL}/system-report/config.json`,
    `${CONTROLLER_BASE_PATH}.ReportController`,
    'updateConfig',
  ],
  [
    '获取分析报告',
    'GET',
    `${ROOT_BASE_URL}/system-report/analysis.json`,
    `${CONTROLLER_BASE_PATH}.ReportController`,
    'getBatchReportData',
  ],
  [
    '流式获取报告解读',
    'GET',
    `${ROOT_BASE_URL}/system-report/async-response.json`,
    `${CONTROLLER_BASE_PATH}.ReportController`,
    'getBatchResponse',
  ],
  [
    '解析自定义表头',
    'POST',
    `${ROOT_BASE_URL}/agent-report/import.json`,
    `${CONTROLLER_BASE_PATH}.AgentImportReportController`,
    'importReportHead',
  ],
  [
    '获取解析自定义表头结果',
    'GET',
    `${ROOT_BASE_URL}/agent-report/import-response.json`,
    `${CONTROLLER_BASE_PATH}.AgentImportReportController`,
    'getReportHeadResponse',
  ],
  [
    '获取报表下载URL',
    'POST',
    `${ROOT_BASE_URL}/agent-report/download.json`,
    `${CONTROLLER_BASE_PATH}.ArentReportDetailController`,
    'getReportDownloadUrl',
  ],
  [
    '获取报表下载密码',
    'POST',
    `${ROOT_BASE_URL}/agent-report/password.json`,
    `${CONTROLLER_BASE_PATH}.ArentReportDetailController`,
    'getPasswd',
  ],
  [
    '获取店铺负责人信息',
    'GET',
    `${ROOT_BASE_URL}/shop-keeper-info.json`,
    `${CONTROLLER_BASE_PATH}.ArentReportDetailController`,
    'getShopKeeper',
  ],
  [
    '上报审计',
    'POST',
    `${ROOT_BASE_URL}/audit/report.json`,
    `${CONTROLLER_BASE_PATH}.ArentReportDetailController`,
    'reportAudit',
  ],
  [
    '获取智能报表概要',
    'GET',
    `${ROOT_BASE_URL}/agent-report/overview-data.json`,
    `${CONTROLLER_BASE_PATH}.ArentReportDetailController`,
    'getReportOverviewData',
  ],
];
