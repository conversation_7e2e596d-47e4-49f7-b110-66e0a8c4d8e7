module.exports = [
  // 查询帐号列表
  [
    'GET',
    '/v4/third-plugin/xhs-note/get-account-list',
    'third-plugin.xhs-note.XhsNoteAccountController',
    'getAccountList',
  ],
  // 保存账号
  [
    'POST',
    '/v4/third-plugin/xhs-note/save-account',
    'third-plugin.xhs-note.XhsNoteAccountController',
    'saveAccount',
  ],
  // 删除账号
  [
    'POST',
    '/v4/third-plugin/xhs-note/delete-account',
    'third-plugin.xhs-note.XhsNoteAccountController',
    'deleteAccount',
  ],
  // 查询账号详情
  [
    'GET',
    '/v4/third-plugin/xhs-note/get-account',
    'third-plugin.xhs-note.XhsNoteAccountController',
    'getAccount',
  ],
  // 查询行业列表
  [
    'GET',
    '/v4/third-plugin/xhs-note/query-industry-type-list',
    'third-plugin.xhs-note.XhsNoteAccountController',
    'queryIndustryTypeList',
  ],
  // 查询账号限制
  [
    'GET',
    '/v4/third-plugin/xhs-note/get-account-limit',
    'third-plugin.xhs-note.XhsNoteAccountController',
    'getAccountLimit',
  ],
  // 查询所有账号的统计数据
  [
    'GET',
    '/v4/third-plugin/xhs-note/query-account-total-data',
    'third-plugin.xhs-note.XhsNoteAccountController',
    'queryAccountTotalData',
  ],
  // 查询小红书小程序绑定情况
  [
    'GET',
    '/v4/third-plugin/xhs-note/query-account-applet-poi',
    'third-plugin.xhs-note.XhsNoteAccountController',
    'queryAccountAppletAndPoi',
  ],
  // 查询小红书小程序绑定情况
  [
    'GET',
    '/v4/third-plugin/xhs-note/query-mini-app',
    'third-plugin.xhs-note.XhsNoteAccountController',
    'queryMiniApp',
  ],
  [
    'GET',
    '/v4/third-plugin/xhs-note/get-plan-list',
    'third-plugin.xhs-note.XhsNotePlanController',
    'getPlanList',
  ],
  [
    'GET',
    '/v4/third-plugin/xhs-note/list-channel-paged',
    'third-plugin.xhs-note.XhsNotePlanController',
    'listChannelPaged',
  ],
  [
    'GET',
    '/v4/third-plugin/xhs-note/list-channel-item',
    'third-plugin.xhs-note.XhsNotePlanController',
    'listChannelItem',
  ],
  [
    'POST',
    '/v4/third-plugin/xhs-note/change-plan-state',
    'third-plugin.xhs-note.XhsNotePlanController',
    'changePlanState',
  ],
  [
    'POST',
    '/v4/third-plugin/xhs-note/modify-plan-config',
    'third-plugin.xhs-note.XhsNotePlanController',
    'modifyPlanConfig',
  ],
  [
    'GET',
    '/v4/third-plugin/xhs-note/get-category-list',
    'third-plugin.xhs-note.XhsNotePlanController',
    'queryCategoryList',
  ],
  [
    'GET',
    '/v4/third-plugin/xhs-note/get-staff-list',
    'third-plugin.xhs-note.XhsNotePlanController',
    'findStaff',
  ],
  [
    'GET',
    '/v4/third-plugin/xhs-note/get-next-creation-plan',
    'third-plugin.xhs-note.XhsNotePlanController',
    'nextCreationPlanItem',
  ],
  [
    'GET',
    '/v4/third-plugin/xhs-note/get-notes-list',
    'third-plugin.xhs-note.XhsNotePlanController',
    'queryPlanItems',
  ],
  [
    'GET',
    '/v4/third-plugin/xhs-note/get-wechat-work-staff-list',
    'third-plugin.xhs-note.XhsNotePlanController',
    'getWecomStaffList',
  ],
  [
    'GET',
    '/v4/third-plugin/xhs-note/get-wechat-work-status',
    'third-plugin.xhs-note.XhsNotePlanController',
    'getWechatWorkStatus',
  ],
  [
    'POST',
    '/v4/third-plugin/xhs-note/delete-plan-item',
    'third-plugin.xhs-note.XhsNotePlanController',
    'deletePlanItem',
  ],
  [
    'POST',
    '/v4/third-plugin/xhs-note/audit-plan-item',
    'third-plugin.xhs-note.XhsNotePlanController',
    'auditPlanItem',
  ],
  [
    'POST',
    '/v4/third-plugin/xhs-note/modify-plan-item',
    'third-plugin.xhs-note.XhsNotePlanController',
    'modifyPlanItem',
  ],
  [
    'GET',
    '/v4/third-plugin/xhs-note/get-plan-item',
    'third-plugin.xhs-note.XhsNotePlanController',
    'getPlanItem',
  ],
  [
    'GET',
    '/v4/third-plugin/xhs-note/get-hot-news-infos',
    'third-plugin.xhs-note.XhsNotePlanController',
    'getHotNewsInfos',
  ],
  [
    'POST',
    '/v4/third-plugin/xhs-note/relation-reference-account',
    'third-plugin.xhs-note.XhsNotePlanController',
    'relationReferenceAccount',
  ],
  [
    'POST',
    '/v4/third-plugin/xhs-note/remove-reference-account',
    'third-plugin.xhs-note.XhsNotePlanController',
    'removeReferenceAccount',
  ],
  [
    'GET',
    '/v4/third-plugin/xhs-note/query-reference-account',
    'third-plugin.xhs-note.XhsNotePlanController',
    'queryReferenceAccount',
  ],
  [
    'POST',
    '/v4/third-plugin/xhs-note/add-account-example-note',
    'third-plugin.xhs-note.XhsNotePlanController',
    'addAccountExampleNote',
  ],
  [
    'POST',
    '/v4/third-plugin/xhs-note/cancel-relation-example-note',
    'third-plugin.xhs-note.XhsNotePlanController',
    'cancelRelationExampleNote',
  ],
  [
    'GET',
    '/v4/third-plugin/xhs-note/get-account-example-note-info',
    'third-plugin.xhs-note.XhsNotePlanController',
    'getAccountExampleNoteInfo',
  ],
  [
    'GET',
    '/v4/third-plugin/xhs-note/search-topic-with-keyword-for-api',
    'third-plugin.xhs-note.XhsNotePlanController',
    'searchTopicWithKeywordForApi',
  ],
  [
    'GET',
    '/v4/third-plugin/xhs-note/use-hot-creation',
    'third-plugin.xhs-note.XhsNotePlanController',
    'useHotCreation',
  ],
];
