module.exports = [
  // 查询店铺能力
  [
    'GET',
    '/v4/third-plugin/salesman-recruit-agent/common/shop-ability',
    'third-plugin.salesman.SalesmanRecruitAgentController',
    'getShopAbility',
  ],
  // 查询分销员名称
  [
    'GET',
    '/v4/third-plugin/salesman-recruit-agent/common/salesman-name',
    'third-plugin.salesman.SalesmanRecruitAgentController',
    'getSalesmanName',
  ],
  // 查询概览数据
  [
    'GET',
    '/v4/third-plugin/salesman-recruit-agent/detail/coreDatas',
    'third-plugin.salesman.SalesmanRecruitAgentController',
    'getCoreDatas',
  ],
  // 查询文案
  [
    'GET',
    '/v4/third-plugin/salesman-recruit-agent/common/getCoT',
    'third-plugin.salesman.SalesmanRecruitAgentController',
    'getApolloCoT',
  ],
  // 查询智能体详情
  // [
  //   'GET',
  //   '/v4/third-plugin/salesman-recruit-agent/detail/getAgentDetail',
  //   'third-plugin.salesman.SalesmanRecruitAgentController',
  //   'getAgentDetail',
  // ],
  // 查询智能体执行记录
  [
    'GET',
    '/v4/third-plugin/salesman-recruit-agent/detail/executeRecords',
    'third-plugin.salesman.SalesmanRecruitAgentController',
    'listActivityPaged',
  ],
  // 查询智能体创建活动的初始值
  [
    'GET',
    '/v4/third-plugin/salesman-recruit-agent/detail/getRecruitActivityInitialInfo',
    'third-plugin.salesman.SalesmanRecruitAgentController',
    'getRecruitActivityInitialInfo',
  ],
  // 失效活动
  [
    'POST',
    '/v4/third-plugin/salesman-recruit-agent/detail/invalidateActivity',
    'third-plugin.salesman.SalesmanRecruitAgentController',
    'invalidateActivity',
  ],
  // 创建活动
  [
    'POST',
    '/v4/third-plugin/salesman-recruit-agent/plan/createActivity',
    'third-plugin.salesman.SalesmanRecruitAgentController',
    'createActivity',
  ],
  // 查看活动详情
  [
    'GET',
    '/v4/third-plugin/salesman-recruit-agent/detail/getActivityDetail',
    'third-plugin.salesman.SalesmanRecruitAgentController',
    'getActivityDetail',
  ],
  // 删除活动
  [
    'POST',
    '/v4/third-plugin/salesman-recruit-agent/detail/deleteActivity',
    'third-plugin.salesman.SalesmanRecruitAgentController',
    'deleteActivity',
  ],
  // 智能体创建优惠券
  [
    'POST',
    '/v4/third-plugin/salesman-recruit-agent/plan/createCoupon',
    'third-plugin.salesman.SalesmanRecruitAgentController',
    'createCardActivity',
  ],
  // 智能体创建任务
  [
    'POST',
    '/v4/third-plugin/salesman-recruit-agent/plan/createTask',
    'third-plugin.salesman.SalesmanRecruitAgentController',
    'getCreateOrUpdateTaskJson',
  ],
  // 智能体创建消息
  [
    'POST',
    '/v4/third-plugin/salesman-recruit-agent/plan/createMessage',
    'third-plugin.salesman.SalesmanRecruitAgentController',
    'createMessage',
  ],
  // 智能体导出任务
  [
    'POST',
    '/v4/third-plugin/salesman-recruit-agent/common/exportSubmit',
    'third-plugin.salesman.SalesmanRecruitAgentController',
    'exportSubmit',
  ],
  // 智能体轮询任务
  [
    'POST',
    '/v4/third-plugin/salesman-recruit-agent/common/listExportRecordById',
    'third-plugin.salesman.SalesmanRecruitAgentController',
    'listExportRecordById',
  ],
  // 智能体查询优惠券设置
  [
    'GET',
    '/v4/third-plugin/salesman-recruit-agent/common/getCouponSetting',
    'third-plugin.salesman.SalesmanRecruitAgentController',
    'getSetting',
  ],
  // 单独查海报
  [
    'GET',
    '/v4/third-plugin/salesman-recruit-agent/common/getPoster',
    'third-plugin.salesman.SalesmanRecruitAgentController',
    'getPoster',
  ],
  // 智能体开启优惠券设置
  [
    'POST',
    '/v4/third-plugin/salesman-recruit-agent/common/openCouponSetting',
    'third-plugin.salesman.SalesmanRecruitAgentController',
    'openCouponSetting',
  ],
  // 校验验证码
  [
    'POST',
    '/v4/third-plugin/salesman-recruit-agent/plan/checkBehaviorCaptcha',
    'third-plugin.salesman.SalesmanRecruitAgentController',
    'checkBehaviorCaptcha',
  ],
  // 发送验证码
  [
    'GET',
    '/v4/third-plugin/salesman-recruit-agent/plan/sendVerifyCode',
    'third-plugin.salesman.SalesmanRecruitAgentController',
    'sendVerifyCode',
  ],
  // 发送验证码
  [
    'POST',
    '/v4/third-plugin/salesman-recruit-agent/plan/checkVerifyCode',
    'third-plugin.salesman.SalesmanRecruitAgentController',
    'checkVerifyCode',
  ],
];
