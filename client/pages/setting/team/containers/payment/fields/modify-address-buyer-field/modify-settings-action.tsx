import React, { useState } from 'react';
import { Button, Drawer, Radio, Checkbox, Input } from 'zent';

interface IModifySettingsDrawerProps {
  onClose: () => void;
}

interface IFormData {
  expressOrderModifyTime: string;
  expressOrderModifyTimeMinutes: string;
  expressOrderModifyContent: string[];
  localOrderModifyTime: string;
  localOrderModifyTimeMinutes: string;
  localOrderModifyContent: string[];
  deliveryFeeIncrease: string;
}

const ModifySettingsDrawer: React.FC<IModifySettingsDrawerProps> = ({ onClose }) => {
  const [formData, setFormData] = useState<IFormData>({
    expressOrderModifyTime: 'beforeShipping', // 快递订单修改时间
    expressOrderModifyTimeMinutes: '', // 支付后分钟数
    expressOrderModifyContent: ['address'], // 快递订单修改内容
    localOrderModifyTime: 'beforeShipping', // 同城配送/自提订单修改时间
    localOrderModifyTimeMinutes: '', // 支付后分钟数
    localOrderModifyContent: ['address'], // 同城配送/自提订单修改内容
    deliveryFeeIncrease: '', // 配送费增加超过金额
  });

  const handleFormChange = (field: keyof IFormData, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleSave = () => {
    // TODO: 实现保存逻辑
    // eslint-disable-next-line no-console
    console.log('保存表单数据:', formData);
    onClose();
  };

  return (
    <Drawer
      title="自助修改设置"
      visible
      onClose={onClose}
      placement="right"
      footer={
        <div className="zent-drawer-demo-drawer-footer">
          <Button onClick={onClose}>取消</Button>
          <Button type="primary" onClick={handleSave}>
            保存
          </Button>
        </div>
      }
    >
      <div className="modify-settings-drawer-form">
        <div style={{ padding: '20px' }}>
          {/* 快递订单修改规则 */}
          <div style={{ marginBottom: '32px' }}>
            <h3
              style={{ fontSize: '13px', fontWeight: 500, marginBottom: '20px', color: '#323233' }}
            >
              快递订单修改规则
            </h3>

            {/* 支持修改的时间 */}
            <div style={{ marginBottom: '20px' }}>
              <div style={{ fontSize: '13px', color: '#323233', marginBottom: '12px' }}>
                支持修改的时间：
              </div>
              <Radio.Group
                value={formData.expressOrderModifyTime}
                onChange={e => handleFormChange('expressOrderModifyTime', e.target.value)}
              >
                <div style={{ marginBottom: '8px' }}>
                  <Radio value="beforeShipping">订单发货前，支持买家修改</Radio>
                </div>
                <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                  <Radio value="afterPayment">支付后</Radio>
                  <Input
                    style={{ width: '80px' }}
                    value={formData.expressOrderModifyTimeMinutes}
                    onChange={e =>
                      handleFormChange('expressOrderModifyTimeMinutes', e.target.value)
                    }
                    placeholder=""
                    disabled={formData.expressOrderModifyTime !== 'afterPayment'}
                  />
                  <span style={{ fontSize: '13px', color: '#111111' }}>
                    分钟内支持买家修改，发货后不支持修改；
                  </span>
                </div>
              </Radio.Group>
            </div>

            {/* 支持修改的内容 */}
            <div style={{ marginBottom: '20px' }}>
              <div style={{ fontSize: '13px', color: '#323233', marginBottom: '12px' }}>
                支持修改的内容：
              </div>
              <Checkbox.Group
                value={formData.expressOrderModifyContent}
                onChange={value => handleFormChange('expressOrderModifyContent', value)}
              >
                <Checkbox value="address">收货地址/联系人/联系方式</Checkbox>
              </Checkbox.Group>
            </div>

            {/* 注意事项 */}
            <div style={{ fontSize: '12px', color: '#969799', lineHeight: '20px' }}>
              注：修改快递订单地址时，当出现新地址超出运费模板配送范围或运费发生变化，不支持修改：
            </div>
          </div>

          {/* 同城配送订单/自提订单修改规则 */}
          <div style={{ marginBottom: '32px' }}>
            <h3
              style={{ fontSize: '13px', fontWeight: 500, marginBottom: '20px', color: '#323233' }}
            >
              同城配送订单/自提订单修改规则
            </h3>

            {/* 支持修改的内容 */}
            <div style={{ marginBottom: '20px' }}>
              <div style={{ fontSize: '13px', color: '#323233', marginBottom: '12px' }}>
                支持修改的内容：
              </div>
              <Checkbox.Group
                value={formData.localOrderModifyContent}
                onChange={value => handleFormChange('localOrderModifyContent', value)}
              >
                <div style={{ marginBottom: '8px' }}>
                  <Checkbox value="address">收货地址/联系人/联系方式</Checkbox>
                </div>
                <div>
                  <Checkbox value="deliveryTime">预约配送/自提时间</Checkbox>
                </div>
              </Checkbox.Group>
            </div>

            {/* 支持修改的时间 */}
            <div style={{ marginBottom: '20px' }}>
              <div style={{ fontSize: '13px', color: '#323233', marginBottom: '12px' }}>
                支持修改的时间：
              </div>
              <Radio.Group
                value={formData.localOrderModifyTime}
                onChange={e => handleFormChange('localOrderModifyTime', e.target.value)}
              >
                <div style={{ marginBottom: '8px' }}>
                  <Radio value="beforeShipping">订单发货前，支持买家修改</Radio>
                </div>
                <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                  <Radio value="afterPayment">支付后</Radio>
                  <Input
                    style={{ width: '80px' }}
                    value={formData.localOrderModifyTimeMinutes}
                    onChange={e => handleFormChange('localOrderModifyTimeMinutes', e.target.value)}
                    placeholder=""
                    disabled={formData.localOrderModifyTime !== 'afterPayment'}
                  />
                  <span style={{ fontSize: '13px', color: '#111111' }}>
                    分钟内支持修改订单，发货后不支持修改；
                  </span>
                </div>
              </Radio.Group>
            </div>

            {/* 配送费变化 */}
            <div style={{ marginBottom: '20px' }}>
              <div style={{ fontSize: '13px', color: '#323233', marginBottom: '12px' }}>
                配送费变化：
              </div>
              <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                <span style={{ fontSize: '13px', color: '#323233' }}>修改后，配送费增加超过</span>
                <Input
                  style={{ width: '80px' }}
                  value={formData.deliveryFeeIncrease}
                  onChange={e => handleFormChange('deliveryFeeIncrease', e.target.value)}
                  placeholder=""
                />
                <span style={{ fontSize: '13px', color: '#111111' }}>元，不支持买家修改</span>
              </div>
            </div>

            {/* 注意事项 */}
            <div style={{ fontSize: '12px', color: '#999999', lineHeight: '20px' }}>
              注：修改后，如果新地址配送费增加，暂不支持补差价。
            </div>
          </div>
        </div>
      </div>
    </Drawer>
  );
};

const ModifySettingsAction = () => {
  const [visible, setVisible] = useState(false);

  const handleShow = () => {
    setVisible(true);
  };
  const handleClose = () => {
    setVisible(false);
  };

  return (
    <div>
      <a href="javascript:void 0;" onClick={handleShow}>
        自助修改设置
      </a>
      {visible && <ModifySettingsDrawer onClose={handleClose} />}
    </div>
  );
};

export default ModifySettingsAction;
