import React, { useState } from 'react';
import { Drawer } from 'zent';

const ModifySettingsAction = () => {
  const [showDialog, setShowDialog] = useState(false);

  const handleClick = () => {
    setShowDialog(true);
  };

  return (
    <div>
      <a href="javascript:void 0;" onClick={handleClick}>
        自助修改设置
      </a>
      {showDialog && (
        <Drawer
          title="抽屉名称"
          visible={visible}
          onClose={() => setVisible(false)}
          maskClosable
          footer={
            <div className="zent-drawer-demo-drawer-footer">
              <Button onClick={close}>关闭</Button>
              <Button type="primary" onClick={close}>
                确定
              </Button>
            </div>
          }
        >
          <Button style={{ margin: '10px 20px' }} onClick={() => setVisible(false)}>
            关闭
          </Button>
          {new Array(30).fill().map((_, index) => (
            <div style={{ padding: '10px' }} key={index}>
              抽屉内容
              {index}
            </div>
          ))}
        </Drawer>
      )}
    </div>
  );
};

export default ModifySettingsAction;
