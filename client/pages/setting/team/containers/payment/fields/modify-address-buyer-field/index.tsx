import React from 'react';
import { Pop } from 'zent';
import { IFieldProps } from '@zent/compat';
import {
  isPureWscSingleStore,
  isEduSingleStore,
  isEduHqStore,
  isRetailMinimalistHqStore,
  isUnifiedHqStore,
  isUnifiedBranchStore,
  isRetailSingleStore,
} from '@youzan/utils-shop';
import { ArthurContainer } from '@youzan/arthur-scheduler-react';
import cx from 'classnames';
import ModifySettingsAction from './modify-settings-action';

interface IModifyAddressBuyerFieldProps extends IFieldProps {
  value: any;
}

// 灰度：买家自助修改收货地址
const isGrayAllowBuyerModifyAddress = _global.isGrayAllowBuyerModifyAddress;

// 微商城单店、有赞连锁D（电商解决方案）教育单店，教育连锁（连锁场景下，只有总部才有配置选项，单店不显示开启买家修改地址的配置开关）
const ModifyAddressBuyerField: React.FC<IModifyAddressBuyerFieldProps> = () => {
  if (
    isGrayAllowBuyerModifyAddress &&
    (isPureWscSingleStore ||
      isEduSingleStore ||
      isEduHqStore ||
      isRetailMinimalistHqStore ||
      isUnifiedHqStore ||
      isUnifiedBranchStore ||
      isRetailSingleStore)
  ) {
    return (
      <ArthurContainer name="orderSetting">
        <div className="modify-auto-ship-field ">
          <Pop content="仅总部可修改配置" trigger="hover" position="top-center" centerArrow>
            <div
              className={cx('modify-auto-ship-field-pop-hook', {
                'modify-auto-ship-field-pop-hook--disabled': !isUnifiedBranchStore,
              })}
            />
          </Pop>
          <div className="form-item">
            <div className="form-label">买家自助修改收货信息：</div>
            <div className="form-controller">
              <ModifySettingsAction />
            </div>
          </div>
        </div>
      </ArthurContainer>
    );
  }

  return null;
};

export default ModifyAddressBuyerField;
