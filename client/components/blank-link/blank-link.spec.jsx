import * as React from 'react';
import { render, cleanup } from '@testing-library/react';
import BlankLink from './index';

describe('<BlankLink /> render', () => {
  afterEach(cleanup);

  test('默认渲染正常', () => {
    const { container } = render(<BlankLink></BlankLink>);
    const renderA = container.querySelector('a') || {};
    const target = renderA.target || '';
    const rel = renderA.rel || '';
    expect(target).toBe('_blank');
    expect(rel).toBe('noopener noreferrer');
  });

  test('所有属性所有属性解析正确', () => {
    const { container } = render(
      <BlankLink className="class" href="//www.xxx.com">
        <span>一条连接</span>
      </BlankLink>
    );
    const { container: span } = render(<span>一条连接</span>);
    const renderA = container.querySelector('a') || {};
    const className = renderA.className;
    const href = renderA.href;
    const children = renderA.children;

    expect(className).toBe('class');
    expect(href).toBe('http://www.xxx.com/');
    expect(children).toEqual(span.children);
  });
});
