const detectImage = buf => {
  if (!(buf && buf.length > 1)) {
    return null;
  }

  if (buf[0] === 0xff && buf[1] === 0xd8 && buf[2] === 0xff) {
    return {
      ext: 'jpg',
      mime: 'image/jpeg',
    };
  }

  if (buf[0] === 0x89 && buf[1] === 0x50 && buf[2] === 0x4e && buf[3] === 0x47) {
    return {
      ext: 'png',
      mime: 'image/png',
    };
  }

  if (buf[0] === 0x47 && buf[1] === 0x49 && buf[2] === 0x46) {
    return {
      ext: 'gif',
      mime: 'image/gif',
    };
  }

  if (buf[8] === 0x57 && buf[9] === 0x45 && buf[10] === 0x42 && buf[11] === 0x50) {
    return {
      ext: 'webp',
      mime: 'image/webp',
    };
  }

  // needs to be before `tif` check
  if (
    ((buf[0] === 0x49 && buf[1] === 0x49 && buf[2] === 0x2a && buf[3] === 0x0) ||
      (buf[0] === 0x4d && buf[1] === 0x4d && buf[2] === 0x0 && buf[3] === 0x2a)) &&
    buf[8] === 0x43 &&
    buf[9] === 0x52
  ) {
    return {
      ext: 'cr2',
      mime: 'image/x-canon-cr2',
    };
  }

  if (
    (buf[0] === 0x49 && buf[1] === 0x49 && buf[2] === 0x2a && buf[3] === 0x0) ||
    (buf[0] === 0x4d && buf[1] === 0x4d && buf[2] === 0x0 && buf[3] === 0x2a)
  ) {
    return {
      ext: 'tif',
      mime: 'image/tiff',
    };
  }

  if (buf[0] === 0x42 && buf[1] === 0x4d) {
    return {
      ext: 'bmp',
      mime: 'image/bmp',
    };
  }

  if (buf[0] === 0x49 && buf[1] === 0x49 && buf[2] === 0xbc) {
    return {
      ext: 'jxr',
      mime: 'image/vnd.ms-photo',
    };
  }

  if (buf[0] === 0x38 && buf[1] === 0x42 && buf[2] === 0x50 && buf[3] === 0x53) {
    return {
      ext: 'psd',
      mime: 'image/vnd.adobe.photoshop',
    };
  }

  if (buf[0] === 0x00 && buf[1] === 0x00 && buf[2] === 0x01 && buf[3] === 0x00) {
    return {
      ext: 'ico',
      mime: 'image/x-icon',
    };
  }

  return null;
};

export default detectImage;
