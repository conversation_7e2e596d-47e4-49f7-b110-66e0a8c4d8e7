import React, { Component } from 'react';
import Recharts from 'echarts-for-react';
import cloneDeep from 'lodash/cloneDeep';
import findIndex from 'lodash/findIndex';
import { Notify } from 'zent';
import { ECHART_LINE_OPTION, CHART_STYLE } from './constants';
import { parseOriginData } from './common';

const getOption = (option, legendInfo, typeInfo, selected) => {
  try {
    const { legendData, xAxisData, ...params } = option;
    let base = {
      ...cloneDeep(ECHART_LINE_OPTION),
      ...params,
    };

    if (legendData) {
      base.legend.data = legendData;
    }
    if (xAxisData) {
      base.xAxis[0].data = xAxisData;
    }
    if (legendInfo) {
      base = parseOriginData('line', base, legendInfo, typeInfo, selected);
    }
    return base;
  } catch (e) {
    Notify.error('图表参数解析错误');
  }
};

class LineChart extends Component {
  state = {
    selected: null,
  };

  componentWillMount() {
    const { option } = this.props;

    if (option && option.legend && option.legend.selected) {
      this.setState({ selected: { ...option.legend.selected } });
    }
  }

  parseEvents(onEvents, legendInfo) {
    if (!legendInfo) return onEvents;
    const { yAxisKeys } = legendInfo;
    const singleColumn = findIndex(yAxisKeys, keyInfo => keyInfo.yAxisIndex === 1) === -1;
    const { legendselectchanged } = onEvents;
    if (!singleColumn) {
      onEvents.legendselectchanged = params => {
        const { selected } = params;
        this.setState({ selected });
        legendselectchanged && legendselectchanged(params);
      };
    }
    return onEvents;
  }

  render() {
    const {
      className = '',
      option = {},
      legendInfo,
      typeInfo,
      notMerge = false,
      style = {},
      onEvents = {},
    } = this.props;
    const { selected } = this.state;
    return (
      <Recharts
        className={className}
        option={getOption(option, legendInfo, typeInfo, selected)}
        notMerge={notMerge}
        onEvents={this.parseEvents(onEvents, legendInfo)}
        style={{
          ...CHART_STYLE,
          ...style,
        }}
      />
    );
  }
}

LineChart.getOption = getOption;

export default LineChart;
