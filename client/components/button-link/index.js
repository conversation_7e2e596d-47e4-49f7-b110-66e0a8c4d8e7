import React from 'react';
import PropTypes from 'prop-types';
import cx from 'classnames';

import { Button } from 'zent';

import './index.scss';

export default function ButtonLink(props) {
  const { className, children, ...buttonProps } = props;
  const cls = cx('zent-btn--link', className, 'overwrite');
  delete buttonProps.type;

  return (
    <Button className={cls} {...buttonProps}>
      {children}
    </Button>
  );
}

ButtonLink.propTypes = {
  className: PropTypes.string,
  children: PropTypes.node.isRequired,
};
