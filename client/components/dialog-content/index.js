import React, { Component } from 'react';
import { Button } from 'zent';
import cx from 'classnames';

import withAir from '../with-air';

class DialogContent extends Component {
  render() {
    const {
      cancel,
      confirm,
      content,
      className,
      cancelText,
      confirmText,
      isSubmitting,
    } = this.props;

    return (
      <div className={cx(className)}>
        {content}
        {(confirmText !== '' || cancelText !== '') && (
          <div className="zent-dialog-r-footer">
            {cancelText && <Button onClick={cancel}>{cancelText}</Button>}
            {confirmText && (
              <Button loading={isSubmitting} type="primary" onClick={confirm}>
                {confirmText}
              </Button>
            )}
          </div>
        )}
      </div>
    );
  }
}

export default withAir(DialogContent);
