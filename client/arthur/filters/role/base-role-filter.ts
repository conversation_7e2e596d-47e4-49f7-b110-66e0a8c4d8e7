import { IRoleListItem, RoleTypeEnum } from './types';

interface IBaseRoleFilterOpts {
  /** 保留哪些用户id */
  includeRoleId?: number[];
  /** 在includeRoleId基础上在进行判定 */
  predicate?(role: IRoleListItem): boolean;
}

const BASE_INCLUDE_IDS = [/** 高级管理员 */ 1, /** 普通管理员 */ 8];
/**
 * 保留店铺预设角色，即角色类型为1的角色
 * @param roleList 接口返回的角色列表
 */
function baseRoleFilter(roleList: IRoleListItem[], opt?: IBaseRoleFilterOpts): IRoleListItem[] {
  const { includeRoleId = BASE_INCLUDE_IDS, predicate } = opt || {};
  if (roleList.length) {
    return roleList.filter(role => {
      const { roleId, roleType, roleTypeValue } = role;
      const _roleType = roleType || roleTypeValue;
      if (_roleType === RoleTypeEnum.CUSTOM_ROLE) return false;
      let isLegalRole = includeRoleId.includes(roleId);
      if (predicate) {
        isLegalRole = predicate(role);
      }
      return isLegalRole;
    });
  }
  return roleList;
}

export { baseRoleFilter };
