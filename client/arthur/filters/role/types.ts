export const RoleTypeEnum = {
  /** 预设角色，包括高级管理员和普通管理员 */
  PRESET_ROLE: 1,
  /** 自定义角色 */
  CUSTOM_ROLE: 2,
};

/** getRoleList接口返回的role数据 */
export interface IRoleListItem {
  /** biz？信息 */
  biz?: string;
  bizCode?: string;
  name: string;
  /** 有些接口没有操作人信息 */
  operator?: string;
  remark: string;
  roleId: number;
  /** RoleTypeEnum */
  roleType?: number;
  roleTypeValue?: number;
  /** 有些没有员工统计 */
  staffCount?: number;
}
