import { baseRoleFilter } from './base-role-filter';
import { customRoleFilter } from './custom-role-filter';
import { IRoleListItem } from './types';

/**
 * 微课堂版本角色过滤器
 * @param roleList 角色列表
 */
function microClassFilter(roleList: IRoleListItem[]): IRoleListItem[] {
  const baseRoles = baseRoleFilter(roleList) || [];
  const customRoles = customRoleFilter(roleList) || [];

  return baseRoles.concat(customRoles);
}

export { microClassFilter };
